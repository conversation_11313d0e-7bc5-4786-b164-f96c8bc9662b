"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar/index.js":
/*!***************************************************!*\
  !*** ./src/components/dashboard/Sidebar/index.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Menu,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Menu,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Menu,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/dashboard/components */ \"(app-pages-browser)/./src/app/dashboard/components.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useSchedulingPreferences */ \"(app-pages-browser)/./src/hooks/useSchedulingPreferences.js\");\n/* harmony import */ var _components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/CustomScrollArea */ \"(app-pages-browser)/./src/components/ui/CustomScrollArea.js\");\n/* harmony import */ var _hooks_useConstructionMessage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useConstructionMessage */ \"(app-pages-browser)/./src/hooks/useConstructionMessage.js\");\n/* harmony import */ var _components_construction__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/construction */ \"(app-pages-browser)/./src/components/construction/index.js\");\n/* harmony import */ var _utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/constructionUtils */ \"(app-pages-browser)/./src/utils/constructionUtils.js\");\n/* harmony import */ var _components_dashboard_CompanySelector__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/CompanySelector */ \"(app-pages-browser)/./src/components/dashboard/CompanySelector.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ModuleTitle = (param)=>{\n    let { moduleId, title, icon: Icon, isCollapsed } = param;\n    // Exibir o logo no lugar do título do módulo, como botão para /dashboard\n    const handleLogoClick = ()=>{\n        if (true) {\n            window.location.href = '/dashboard';\n        }\n    };\n    if (isCollapsed) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-6 flex justify-center items-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleLogoClick,\n                className: \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 focus:outline-none cursor-pointer\",\n                \"aria-label\": \"Ir para o dashboard\",\n                title: \"High Tide - Ir para dashboard\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/logo_icon_sem_fundo.png\",\n                    alt: \"High Tide\",\n                    className: \"w-8 h-8 dark:invert transition-all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 33,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6 flex justify-center items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleLogoClick,\n            style: {\n                background: 'none',\n                border: 'none',\n                padding: 0\n            },\n            className: \"focus:outline-none cursor-pointer\",\n            \"aria-label\": \"Ir para o dashboard\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/logo_horizontal_sem_fundo.png\",\n                    alt: \"High Tide Logo\",\n                    className: \"h-12 dark:invert dark:text-white transition-all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ModuleTitle;\n// Mapeamento de submenus para permissões\nconst submenuPermissionsMap = {\n    // Admin\n    'admin.introduction': 'admin.dashboard.view',\n    'admin.dashboard': 'admin.dashboard.view',\n    'admin.users': 'admin.users.view',\n    'admin.professions': [\n        'admin.professions.view',\n        'admin.profession-groups.view'\n    ],\n    'admin.bug-reports': 'admin.dashboard.view',\n    'admin.plans': 'admin.dashboard.view',\n    'admin.logs': 'admin.logs.view',\n    'admin.settings': 'admin.config.edit',\n    'admin.backup': 'admin.config.edit',\n    'admin.affiliates': 'admin.dashboard.view',\n    // ABA+\n    'abaplus.anamnese': 'abaplus.anamnese.view',\n    'abaplus.evolucoes-diarias': 'abaplus.evolucoes-diarias.view',\n    'abaplus.sessao': 'abaplus.sessao.view',\n    // Financeiro\n    'financial.invoices': 'financial.invoices.view',\n    'financial.payments': 'financial.payments.view',\n    'financial.expenses': 'financial.expenses.view',\n    'financial.reports': 'financial.reports.view',\n    'financial.cashflow': 'financial.reports.view',\n    // RH\n    'hr.employees': 'rh.employees.view',\n    'hr.payroll': 'rh.payroll.view',\n    'hr.documents': 'rh.employees.view',\n    'hr.departments': 'rh.employees.view',\n    'hr.attendance': 'rh.attendance.view',\n    'hr.benefits': 'rh.benefits.view',\n    'hr.training': 'rh.employees.view',\n    // Pessoas\n    'people.clients': 'people.clients.view',\n    'people.persons': 'people.persons.view',\n    'people.documents': 'people.documents.view',\n    'people.insurances': 'people.insurances.view',\n    'people.insurance-limits': 'people.insurance-limits.view',\n    // Agendamento\n    'scheduler.calendar': 'scheduling.calendar.view',\n    'scheduler.working-hours': 'scheduling.working-hours.view',\n    'scheduler.service-types': 'scheduling.service-types.view',\n    'scheduler.locations': 'scheduling.locations.view',\n    'scheduler.occupancy': 'scheduling.occupancy.view',\n    'scheduler.appointment-requests': 'scheduling.appointment-requests.view',\n    'scheduler.appointments-report': 'scheduling.appointments-report.view',\n    'scheduler.appointments-dashboard': 'scheduling.appointments-dashboard.view'\n};\nconst Sidebar = (param)=>{\n    let { activeModule, activeModuleTitle, isSubmenuActive, handleModuleSubmenuClick, handleBackToModules, isSidebarOpen } = param;\n    var _moduleSubmenus_activeModule;\n    _s();\n    const { can, hasModule, isAdmin } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { schedulingPreferences, isLoading: preferencesLoading } = (0,_hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__.useSchedulingPreferences)();\n    // Estado para controlar se a sidebar está colapsada\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Sidebar.useState\": ()=>{\n            if (true) {\n                const savedCollapsed = localStorage.getItem('sidebarCollapsed');\n                return savedCollapsed === 'true';\n            }\n            return false;\n        }\n    }[\"Sidebar.useState\"]);\n    // Inicializar o estado de grupos expandidos a partir do localStorage\n    const [expandedGroups, setExpandedGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Sidebar.useState\": ()=>{\n            // Verificar se estamos no cliente (browser) antes de acessar localStorage\n            if (true) {\n                const savedState = localStorage.getItem('sidebarExpandedGroups');\n                return savedState ? JSON.parse(savedState) : {};\n            }\n            return {};\n        }\n    }[\"Sidebar.useState\"]);\n    // Encontrar o objeto do módulo ativo para acessar seu ícone\n    const activeModuleObject = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.modules.find((m)=>m.id === activeModule);\n    const ModuleIcon = activeModuleObject === null || activeModuleObject === void 0 ? void 0 : activeModuleObject.icon;\n    // Função para verificar se o usuário tem permissão para ver um submenu\n    const hasPermissionForSubmenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenu]\": (moduleId, submenuId)=>{\n            var _moduleSubmenus_moduleId;\n            // Ignorar verificação para grupos, pois a permissão é verificada para cada item\n            if (submenuId === 'cadastro' || submenuId === 'configuracoes' || submenuId === 'gestao' || submenuId === 'convenios' || submenuId === 'financeiro' || submenuId === 'relatorios') {\n                return true;\n            }\n            // Buscar o submenu específico para verificar systemAdminOnly\n            const submenuItem = (_moduleSubmenus_moduleId = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId]) === null || _moduleSubmenus_moduleId === void 0 ? void 0 : _moduleSubmenus_moduleId.find({\n                \"Sidebar.useCallback[hasPermissionForSubmenu]\": (item)=>item.id === submenuId\n            }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"]);\n            // Verificar se o submenu requer SYSTEM_ADMIN\n            if (submenuItem === null || submenuItem === void 0 ? void 0 : submenuItem.systemAdminOnly) {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            // Verificação especial adicional para afiliados - apenas SYSTEM_ADMIN\n            if (submenuId === 'affiliates') {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            // Verificação especial adicional para bug-reports - apenas SYSTEM_ADMIN\n            if (submenuId === 'bug-reports') {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            const permissionKey = \"\".concat(moduleId, \".\").concat(submenuId);\n            const requiredPermission = submenuPermissionsMap[permissionKey];\n            // Se não há mapeamento de permissão, permitir acesso\n            if (!requiredPermission) return true;\n            // Administradores têm acesso a tudo (exceto itens systemAdminOnly)\n            if (isAdmin()) return true;\n            // Verificar permissão específica\n            if (Array.isArray(requiredPermission)) {\n                // Se for um array de permissões, verificar se o usuário tem pelo menos uma delas\n                return requiredPermission.some({\n                    \"Sidebar.useCallback[hasPermissionForSubmenu]\": (perm)=>can(perm)\n                }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"]);\n            } else {\n                // Se for uma única permissão, verificar normalmente\n                return can(requiredPermission);\n            }\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"], [\n        can,\n        isAdmin,\n        user.role\n    ]);\n    // Função para verificar se um submenu deve ser exibido baseado nas preferências\n    const shouldShowSubmenuByPreferences = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[shouldShowSubmenuByPreferences]\": (moduleId, submenuId)=>{\n            // Apenas verificar preferências para o módulo de agendamento\n            if (moduleId !== 'scheduler') return true;\n            // Se as preferências ainda estão carregando, não mostrar os itens que podem ser escondidos\n            if (preferencesLoading) {\n                return false;\n            }\n            // Verificar se as preferências estão carregadas\n            if (!schedulingPreferences) {\n                return false;\n            }\n            switch(submenuId){\n                case 'locations':\n                    const showLocations = schedulingPreferences.showLocations !== false;\n                    return showLocations;\n                case 'service-types':\n                    const showServiceTypes = schedulingPreferences.showServiceTypes !== false;\n                    return showServiceTypes;\n                case 'insurances':\n                    const showInsurance = schedulingPreferences.showInsurance !== false;\n                    return showInsurance;\n                case 'working-hours':\n                    const showWorkingHours = schedulingPreferences.showWorkingHours !== false;\n                    return showWorkingHours;\n                default:\n                    return true;\n            }\n        }\n    }[\"Sidebar.useCallback[shouldShowSubmenuByPreferences]\"], [\n        schedulingPreferences,\n        preferencesLoading\n    ]);\n    // Função para alternar o colapso da sidebar\n    const toggleCollapse = ()=>{\n        const newCollapsedState = !isCollapsed;\n        setIsCollapsed(newCollapsedState);\n        if (true) {\n            localStorage.setItem('sidebarCollapsed', newCollapsedState.toString());\n        }\n    };\n    // Função para alternar a expansão de um grupo\n    const toggleGroup = (groupId)=>{\n        setExpandedGroups((prev)=>({\n                ...prev,\n                [groupId]: !prev[groupId]\n            }));\n    };\n    // Verificar se algum item dentro de um grupo está ativo\n    const isGroupActive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[isGroupActive]\": (moduleId, groupItems)=>{\n            return groupItems.some({\n                \"Sidebar.useCallback[isGroupActive]\": (item)=>isSubmenuActive(moduleId, item.id)\n            }[\"Sidebar.useCallback[isGroupActive]\"]);\n        }\n    }[\"Sidebar.useCallback[isGroupActive]\"], [\n        isSubmenuActive\n    ]);\n    // Expandir automaticamente grupos que contêm o item ativo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            if (activeModule && _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) {\n                const newExpandedGroups = {\n                    ...expandedGroups\n                };\n                _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule].forEach({\n                    \"Sidebar.useEffect\": (submenu)=>{\n                        if (submenu.type === 'group' && isGroupActive(activeModule, submenu.items)) {\n                            newExpandedGroups[submenu.id] = true;\n                        }\n                    }\n                }[\"Sidebar.useEffect\"]);\n                setExpandedGroups(newExpandedGroups);\n            }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"Sidebar.useEffect\"], [\n        activeModule,\n        pathname,\n        isGroupActive\n    ]);\n    // Verificar se um item de submenu tem permissão para ser exibido\n    const hasPermissionForSubmenuItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (moduleId, submenuId)=>{\n            var _moduleSubmenus_moduleId;\n            // Buscar o item dentro dos grupos do módulo\n            let submenuItem = null;\n            // Primeiro buscar nos itens diretos\n            submenuItem = (_moduleSubmenus_moduleId = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId]) === null || _moduleSubmenus_moduleId === void 0 ? void 0 : _moduleSubmenus_moduleId.find({\n                \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (item)=>item.id === submenuId\n            }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"]);\n            // Se não encontrou, buscar dentro dos grupos\n            if (!submenuItem) {\n                for (const item of _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId] || []){\n                    if (item.type === 'group' && item.items) {\n                        submenuItem = item.items.find({\n                            \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (subItem)=>subItem.id === submenuId\n                        }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"]);\n                        if (submenuItem) break;\n                    }\n                }\n            }\n            // Verificar se o item requer SYSTEM_ADMIN\n            if (submenuItem === null || submenuItem === void 0 ? void 0 : submenuItem.systemAdminOnly) {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            return hasPermissionForSubmenu(moduleId, submenuId);\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"], [\n        hasPermissionForSubmenu,\n        user === null || user === void 0 ? void 0 : user.role\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"\".concat(isCollapsed ? 'w-16' : 'w-72', \" bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col \").concat(isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'),\n        \"aria-label\": \"Navega\\xe7\\xe3o lateral\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"flex-1 p-5\",\n                moduleColor: activeModule,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toggleCollapse,\n                            className: \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200\",\n                            \"aria-label\": isCollapsed ? 'Expandir sidebar' : 'Minimizar sidebar',\n                            title: isCollapsed ? 'Expandir sidebar' : 'Minimizar sidebar',\n                            children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 313,\n                                columnNumber: 28\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 313,\n                                columnNumber: 57\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, undefined),\n                    activeModuleObject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModuleTitle, {\n                        moduleId: activeModule,\n                        title: activeModuleTitle,\n                        icon: ModuleIcon,\n                        isCollapsed: isCollapsed\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, undefined),\n                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_CompanySelector__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        activeModule: activeModule\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 328,\n                        columnNumber: 26\n                    }, undefined),\n                    preferencesLoading && activeModule === 'scheduler' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, undefined),\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-gray-500 dark:text-gray-400\",\n                                children: \"Carregando...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 334,\n                                columnNumber: 30\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2 flex flex-col justify-center items-center mt-8 \".concat(isCollapsed ? 'px-1' : ''),\n                        \"aria-labelledby\": \"sidebar-heading\",\n                        children: !(preferencesLoading && activeModule === 'scheduler') && activeModule && ((_moduleSubmenus_activeModule = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) === null || _moduleSubmenus_activeModule === void 0 ? void 0 : _moduleSubmenus_activeModule.map((submenu)=>{\n                            // Verificar se é um grupo ou um item individual\n                            if (submenu.type === 'group') {\n                                // Verificar se algum item do grupo tem permissão para ser exibido\n                                const hasAnyPermission = submenu.items.some((item)=>hasPermissionForSubmenuItem(activeModule, item.id));\n                                if (!hasAnyPermission) {\n                                    return null; // Não renderizar o grupo se nenhum item tiver permissão\n                                }\n                                const isGroupExpanded = expandedGroups[submenu.id] || false;\n                                const isAnyItemActive = isGroupActive(activeModule, submenu.items);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleGroup(submenu.id),\n                                            className: \"\\n                      group w-full flex items-center justify-between px-4 py-2.5 rounded-lg transition-all duration-300\\n                      \".concat(isAnyItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark font-medium\") : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700', \"\\n                    \"),\n                                            \"aria-expanded\": isGroupExpanded,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-left\",\n                                                    children: submenu.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-500 dark:text-gray-400\",\n                                                    children: isGroupExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 25\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 362,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isGroupExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4 pl-2 border-l border-gray-200 dark:border-gray-700 space-y-1\",\n                                            children: submenu.items.map((item)=>{\n                                                const isItemActive = isSubmenuActive(activeModule, item.id);\n                                                // Verificar permissão antes de renderizar o item\n                                                if (!hasPermissionForSubmenuItem(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não tiver permissão\n                                                }\n                                                // Verificar se o item deve ser exibido baseado nas preferências\n                                                if (!shouldShowSubmenuByPreferences(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não estiver habilitado nas preferências\n                                                }\n                                                // Verificar se o item está em construção\n                                                const itemKey = \"\".concat(activeModule, \".\").concat(item.id);\n                                                const isItemUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.isUnderConstruction)(activeModule, item.id);\n                                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.getConstructionMessage)(activeModule, item.id);\n                                                // Estilo comum para os itens\n                                                const itemClassName = \"\\n                          group w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-300\\n                          \".concat(isItemActive ? \"rounded-xl border border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                               bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                               shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n                        \");\n                                                // Se estiver em construção, usar o ConstructionButton\n                                                if (isItemUnderConstruction) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_9__.ConstructionButton, {\n                                                        className: itemClassName,\n                                                        \"aria-current\": isItemActive ? 'page' : undefined,\n                                                        title: constructionMessage.title,\n                                                        content: constructionMessage.content,\n                                                        icon: constructionMessage.icon,\n                                                        position: \"right\",\n                                                        children: [\n                                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\\n                                  \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                                \"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                    size: 18,\n                                                                    \"aria-hidden\": \"true\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 35\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 33\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                                children: item.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 31\n                                                            }, undefined)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 29\n                                                    }, undefined);\n                                                }\n                                                // Se não estiver em construção, usar o botão normal\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleModuleSubmenuClick(activeModule, item.id),\n                                                    className: itemClassName,\n                                                    \"aria-current\": isItemActive ? 'page' : undefined,\n                                                    children: [\n                                                        item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"\\n                                \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                              \"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                size: 18,\n                                                                \"aria-hidden\": \"true\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 31\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 27\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 385,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 360,\n                                    columnNumber: 17\n                                }, undefined);\n                            } else {\n                                // Renderização de itens individuais (não agrupados)\n                                const isActive = isSubmenuActive(activeModule, submenu.id);\n                                // Verificar permissão antes de renderizar o item\n                                const hasPermission = hasPermissionForSubmenu(activeModule, submenu.id);\n                                if (!hasPermission) {\n                                    return null; // Não renderizar se não tiver permissão\n                                }\n                                // Verificar se o submenu deve ser exibido baseado nas preferências\n                                const shouldShowByPreferences = shouldShowSubmenuByPreferences(activeModule, submenu.id);\n                                if (!shouldShowByPreferences) {\n                                    return null; // Não renderizar se não estiver habilitado nas preferências\n                                }\n                                // Verificar se o submenu está em construção\n                                const submenuKey = \"\".concat(activeModule, \".\").concat(submenu.id);\n                                const isSubmenuUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.isUnderConstruction)(activeModule, submenu.id);\n                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.getConstructionMessage)(activeModule, submenu.id);\n                                // Estilo comum para ambos os tipos de botões\n                                const buttonClassName = \"\\n                group w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-300\\n                \".concat(isActive ? \"rounded-xl border border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                     bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                     shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n              \");\n                                // Se estiver em construção, usar o ConstructionButton\n                                if (isSubmenuUnderConstruction) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_9__.ConstructionButton, {\n                                        className: buttonClassName,\n                                        \"aria-current\": isActive ? 'page' : undefined,\n                                        title: constructionMessage.title,\n                                        content: constructionMessage.content,\n                                        icon: constructionMessage.icon,\n                                        position: \"right\",\n                                        children: [\n                                            submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                    size: 20,\n                                                    \"aria-hidden\": \"true\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 521,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                                children: submenu.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 533,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, submenu.id, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                        lineNumber: 511,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }\n                                // Se não estiver em construção, usar o botão normal\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleModuleSubmenuClick(activeModule, submenu.id),\n                                    className: buttonClassName,\n                                    \"aria-current\": isActive ? 'page' : undefined,\n                                    children: [\n                                        submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                size: 20,\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 553,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 547,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                            children: submenu.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 559,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 540,\n                                    columnNumber: 17\n                                }, undefined);\n                            }\n                        }))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-5 border-t border-gray-100 dark:border-gray-700 mt-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToModules,\n                    className: \"\\n            group w-full py-3 px-4 rounded-lg flex items-center gap-3\\n            border-2 border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n            bg-transparent dark:bg-gray-800 hover:bg-module-\").concat(activeModule, \"-bg/10 dark:hover:bg-module-\").concat(activeModule, \"-bg-dark/10\\n            transition-all duration-300\\n          \"),\n                    \"aria-label\": \"Voltar para o dashboard principal\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            flex items-center justify-center w-8 h-8 rounded-full\\n            bg-module-\".concat(activeModule, \"-bg dark:bg-module-\").concat(activeModule, \"-bg-dark/70\\n            text-module-\").concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\\n            shadow-sm dark:shadow-md dark:shadow-black/20\\n            group-hover:scale-110 transition-transform duration-200\\n          \"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 586,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 579,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-gray-800 dark:text-gray-200\",\n                            children: \"Voltar a Tela Inicial\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 588,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 569,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 568,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 298,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Sidebar, \"VAGN2v+pOWDhD1NheeJLNqum7jI=\", false, function() {\n    return [\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__.useSchedulingPreferences\n    ];\n});\n_c1 = Sidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\nvar _c, _c1;\n$RefreshReg$(_c, \"ModuleTitle\");\n$RefreshReg$(_c1, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar/index.js\n"));

/***/ })

});