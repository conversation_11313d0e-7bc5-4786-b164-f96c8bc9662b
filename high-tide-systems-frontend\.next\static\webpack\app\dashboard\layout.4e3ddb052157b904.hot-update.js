"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/contexts/AuthContext.js":
/*!*************************************!*\
  !*** ./src/contexts/AuthContext.js ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Fetch user data from API\n    const fetchUserData = async (token)=>{\n        try {\n            _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.defaults.headers.Authorization = \"Bearer \".concat(token);\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.get('/auth/me');\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching user data:', error);\n            localStorage.removeItem('token');\n            delete _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.defaults.headers.Authorization;\n            return null;\n        }\n    };\n    // Initialize auth on page load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async ()=>{\n                    try {\n                        const token = localStorage.getItem('token');\n                        if (token) {\n                            const userData = await fetchUserData(token);\n                            if (userData) {\n                                setUser(userData);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Error initializing auth:', error);\n                        localStorage.removeItem('token');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Login function\n    const login = async function(identifier, password) {\n        let loginType = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'email';\n        try {\n            // Prepare login data based on login type\n            const loginData = {\n                password,\n                loginType\n            };\n            // Set the appropriate field based on login type\n            if (loginType === 'email') {\n                loginData.email = identifier;\n            } else {\n                loginData.username = identifier;\n            }\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.post('/auth/login', loginData);\n            // Check if 2FA is required\n            if (response.data.requiresTwoFactor) {\n                return {\n                    success: false,\n                    requiresTwoFactor: true,\n                    userId: response.data.userId,\n                    expiresAt: response.data.expiresAt,\n                    message: response.data.message\n                };\n            }\n            const { token, user: userData } = response.data;\n            localStorage.setItem('token', token);\n            _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.defaults.headers.Authorization = \"Bearer \".concat(token);\n            // Set user data including role information\n            setUser({\n                ...userData,\n                // Ensure role is properly set, defaulting to EMPLOYEE if not present\n                role: userData.role || (userData.isClient ? 'CLIENT' : 'EMPLOYEE')\n            });\n            router.push('/dashboard');\n            return {\n                success: true\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Login error:', error);\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao fazer login';\n            // Traduzir mensagens de erro específicas\n            const translatedMessage = translateErrorMessage(errorMessage);\n            throw new Error(translatedMessage);\n        }\n    };\n    // Função para traduzir mensagens de erro do backend\n    const translateErrorMessage = (message)=>{\n        const translations = {\n            'Invalid credentials': 'Credenciais inválidas',\n            'Invalid email or password': 'Email ou senha inválidos',\n            'User not found': 'Usuário não encontrado',\n            'Account disabled': 'Conta desabilitada',\n            'Account locked': 'Conta bloqueada',\n            'Too many login attempts': 'Muitas tentativas de login',\n            'Session expired': 'Sessão expirada',\n            'Token invalid': 'Token inválido',\n            'Token expired': 'Token expirado',\n            'Access denied': 'Acesso negado',\n            'Unauthorized': 'Não autorizado',\n            'Forbidden': 'Acesso proibido',\n            'Not found': 'Não encontrado',\n            'Bad request': 'Requisição inválida',\n            'Internal server error': 'Erro interno do servidor',\n            'Service unavailable': 'Serviço indisponível',\n            'Network error': 'Erro de conexão',\n            'Request timeout': 'Tempo limite da requisição',\n            'Connection refused': 'Conexão recusada',\n            'Server error': 'Erro do servidor'\n        };\n        return translations[message] || message;\n    };\n    // Logout function\n    const logout = async ()=>{\n        try {\n            // Chamar endpoint de logout do backend para invalidar o token e desconectar sockets\n            const token = localStorage.getItem('token');\n            if (token) {\n                await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.post('/auth/logout', {}, {\n                    headers: {\n                        Authorization: \"Bearer \".concat(token)\n                    }\n                });\n                console.log('Logout realizado com sucesso no backend');\n            }\n        } catch (error) {\n            console.warn('Erro ao fazer logout no backend:', error.message);\n        // Continuar com o logout local mesmo se o backend falhar\n        } finally{\n            // Limpar estado local\n            localStorage.removeItem('token');\n            delete _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.defaults.headers.Authorization;\n            setUser(null);\n            router.push('/login');\n        }\n    };\n    // Check if user has a specific role\n    const hasRole = (role)=>{\n        if (!user) return false;\n        if (Array.isArray(role)) {\n            return role.includes(user.role);\n        }\n        return user.role === role;\n    };\n    // Check if user is a system admin\n    const isSystemAdmin = ()=>{\n        return hasRole('SYSTEM_ADMIN');\n    };\n    // Check if user is a company admin\n    const isCompanyAdmin = ()=>{\n        return hasRole('COMPANY_ADMIN');\n    };\n    // Check if user is an employee\n    const isEmployee = ()=>{\n        return hasRole('EMPLOYEE');\n    };\n    // Check if user belongs to a specific company\n    const belongsToCompany = (companyId)=>{\n        if (!user || !user.companyId) return false;\n        return user.companyId === companyId;\n    };\n    // Check if user has specific module\n    const hasModule = (moduleId)=>{\n        if (!user || !user.modules) return false;\n        return user.modules.includes(moduleId);\n    };\n    // Atualizar informações do usuário (ex: após atualização de perfil)\n    const refreshUserData = async ()=>{\n        try {\n            const token = localStorage.getItem('token');\n            if (token) {\n                const userData = await fetchUserData(token);\n                if (userData) {\n                    setUser(userData);\n                    return true;\n                }\n            }\n            return false;\n        } catch (error) {\n            console.error('Error refreshing user data:', error);\n            return false;\n        }\n    };\n    // Verify 2FA token\n    const verify2FAToken = async (userId, token)=>{\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.post('/auth/verify-2fa', {\n                userId,\n                token\n            });\n            const { token: authToken, user: userData } = response.data;\n            localStorage.setItem('token', authToken);\n            _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.defaults.headers.Authorization = \"Bearer \".concat(authToken);\n            setUser({\n                ...userData,\n                role: userData.role || (userData.isClient ? 'CLIENT' : 'EMPLOYEE')\n            });\n            router.push('/dashboard');\n            return {\n                success: true\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('2FA verification error:', error);\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao verificar código 2FA';\n            throw new Error(translateErrorMessage(errorMessage));\n        }\n    };\n    // Resend 2FA token\n    const resend2FAToken = async (userId)=>{\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.post('/auth/resend-2fa', {\n                userId\n            });\n            return {\n                success: true,\n                expiresAt: response.data.expiresAt,\n                message: response.data.message\n            };\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Resend 2FA error:', error);\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao reenviar código 2FA';\n            throw new Error(translateErrorMessage(errorMessage));\n        }\n    };\n    // Obter o token atual do localStorage\n    const getToken = ()=>{\n        if (true) {\n            return localStorage.getItem('token');\n        }\n        return null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            token: getToken(),\n            login,\n            logout,\n            loading,\n            hasRole,\n            isSystemAdmin,\n            isCompanyAdmin,\n            isEmployee,\n            belongsToCompany,\n            hasModule,\n            refreshUserData,\n            verify2FAToken,\n            resend2FAToken\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n        lineNumber: 262,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"J17Kp8z+0ojgAqGoY5o3BCjwWms=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = AuthProvider;\nconst useAuth = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error('useAuth deve ser usado dentro de um AuthProvider');\n    }\n    return context;\n};\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb250ZXh0cy9BdXRoQ29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFdUU7QUFDckM7QUFDVTtBQUU1QyxNQUFNTSw0QkFBY04sb0RBQWFBLENBQUMsQ0FBQztBQUU1QixTQUFTTyxhQUFhLEtBQVk7UUFBWixFQUFFQyxRQUFRLEVBQUUsR0FBWjs7SUFDM0IsTUFBTSxDQUFDQyxNQUFNQyxRQUFRLEdBQUdSLCtDQUFRQSxDQUFDO0lBQ2pDLE1BQU0sQ0FBQ1MsU0FBU0MsV0FBVyxHQUFHViwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNVyxTQUFTUiwwREFBU0E7SUFFeEIsMkJBQTJCO0lBQzNCLE1BQU1TLGdCQUFnQixPQUFPQztRQUMzQixJQUFJO1lBQ0ZYLDJDQUFHQSxDQUFDWSxRQUFRLENBQUNDLE9BQU8sQ0FBQ0MsYUFBYSxHQUFHLFVBQWdCLE9BQU5IO1lBQy9DLE1BQU1JLFdBQVcsTUFBTWYsMkNBQUdBLENBQUNnQixHQUFHLENBQUM7WUFDL0IsT0FBT0QsU0FBU0UsSUFBSTtRQUN0QixFQUFFLE9BQU9DLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkE7WUFDM0NFLGFBQWFDLFVBQVUsQ0FBQztZQUN4QixPQUFPckIsMkNBQUdBLENBQUNZLFFBQVEsQ0FBQ0MsT0FBTyxDQUFDQyxhQUFhO1lBQ3pDLE9BQU87UUFDVDtJQUNGO0lBRUEsK0JBQStCO0lBQy9CZixnREFBU0E7a0NBQUM7WUFDUixNQUFNdUI7eURBQWlCO29CQUNyQixJQUFJO3dCQUNGLE1BQU1YLFFBQVFTLGFBQWFHLE9BQU8sQ0FBQzt3QkFDbkMsSUFBSVosT0FBTzs0QkFDVCxNQUFNYSxXQUFXLE1BQU1kLGNBQWNDOzRCQUNyQyxJQUFJYSxVQUFVO2dDQUNabEIsUUFBUWtCOzRCQUNWO3dCQUNGO29CQUNGLEVBQUUsT0FBT04sT0FBTzt3QkFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7d0JBQzFDRSxhQUFhQyxVQUFVLENBQUM7b0JBQzFCLFNBQVU7d0JBQ1JiLFdBQVc7b0JBQ2I7Z0JBQ0Y7O1lBRUFjO1FBQ0Y7aUNBQUcsRUFBRTtJQUVMLGlCQUFpQjtJQUNqQixNQUFNRyxRQUFRLGVBQU9DLFlBQVlDO1lBQVVDLDZFQUFZO1FBQ3JELElBQUk7WUFDRix5Q0FBeUM7WUFDekMsTUFBTUMsWUFBWTtnQkFDaEJGO2dCQUNBQztZQUNGO1lBRUEsZ0RBQWdEO1lBQ2hELElBQUlBLGNBQWMsU0FBUztnQkFDekJDLFVBQVVDLEtBQUssR0FBR0o7WUFDcEIsT0FBTztnQkFDTEcsVUFBVUUsUUFBUSxHQUFHTDtZQUN2QjtZQUVBLE1BQU1YLFdBQVcsTUFBTWYsMkNBQUdBLENBQUNnQyxJQUFJLENBQUMsZUFBZUg7WUFFL0MsMkJBQTJCO1lBQzNCLElBQUlkLFNBQVNFLElBQUksQ0FBQ2dCLGlCQUFpQixFQUFFO2dCQUNuQyxPQUFPO29CQUNMQyxTQUFTO29CQUNURCxtQkFBbUI7b0JBQ25CRSxRQUFRcEIsU0FBU0UsSUFBSSxDQUFDa0IsTUFBTTtvQkFDNUJDLFdBQVdyQixTQUFTRSxJQUFJLENBQUNtQixTQUFTO29CQUNsQ0MsU0FBU3RCLFNBQVNFLElBQUksQ0FBQ29CLE9BQU87Z0JBQ2hDO1lBQ0Y7WUFFQSxNQUFNLEVBQUUxQixLQUFLLEVBQUVOLE1BQU1tQixRQUFRLEVBQUUsR0FBR1QsU0FBU0UsSUFBSTtZQUUvQ0csYUFBYWtCLE9BQU8sQ0FBQyxTQUFTM0I7WUFDOUJYLDJDQUFHQSxDQUFDWSxRQUFRLENBQUNDLE9BQU8sQ0FBQ0MsYUFBYSxHQUFHLFVBQWdCLE9BQU5IO1lBRS9DLDJDQUEyQztZQUMzQ0wsUUFBUTtnQkFDTixHQUFHa0IsUUFBUTtnQkFDWCxxRUFBcUU7Z0JBQ3JFZSxNQUFNZixTQUFTZSxJQUFJLElBQUtmLENBQUFBLFNBQVNnQixRQUFRLEdBQUcsV0FBVyxVQUFTO1lBQ2xFO1lBRUEvQixPQUFPZ0MsSUFBSSxDQUFDO1lBQ1osT0FBTztnQkFBRVAsU0FBUztZQUFLO1FBQ3pCLEVBQUUsT0FBT2hCLE9BQU87Z0JBRU9BLHNCQUFBQTtZQURyQkMsUUFBUUQsS0FBSyxDQUFDLGdCQUFnQkE7WUFDOUIsTUFBTXdCLGVBQWV4QixFQUFBQSxrQkFBQUEsTUFBTUgsUUFBUSxjQUFkRyx1Q0FBQUEsdUJBQUFBLGdCQUFnQkQsSUFBSSxjQUFwQkMsMkNBQUFBLHFCQUFzQm1CLE9BQU8sS0FBSTtZQUV0RCx5Q0FBeUM7WUFDekMsTUFBTU0sb0JBQW9CQyxzQkFBc0JGO1lBQ2hELE1BQU0sSUFBSUcsTUFBTUY7UUFDbEI7SUFDRjtJQUVBLG9EQUFvRDtJQUNwRCxNQUFNQyx3QkFBd0IsQ0FBQ1A7UUFDN0IsTUFBTVMsZUFBZTtZQUNuQix1QkFBdUI7WUFDdkIsNkJBQTZCO1lBQzdCLGtCQUFrQjtZQUNsQixvQkFBb0I7WUFDcEIsa0JBQWtCO1lBQ2xCLDJCQUEyQjtZQUMzQixtQkFBbUI7WUFDbkIsaUJBQWlCO1lBQ2pCLGlCQUFpQjtZQUNqQixpQkFBaUI7WUFDakIsZ0JBQWdCO1lBQ2hCLGFBQWE7WUFDYixhQUFhO1lBQ2IsZUFBZTtZQUNmLHlCQUF5QjtZQUN6Qix1QkFBdUI7WUFDdkIsaUJBQWlCO1lBQ2pCLG1CQUFtQjtZQUNuQixzQkFBc0I7WUFDdEIsZ0JBQWdCO1FBQ2xCO1FBRUEsT0FBT0EsWUFBWSxDQUFDVCxRQUFRLElBQUlBO0lBQ2xDO0lBRUEsa0JBQWtCO0lBQ2xCLE1BQU1VLFNBQVM7UUFDYixJQUFJO1lBQ0Ysb0ZBQW9GO1lBQ3BGLE1BQU1wQyxRQUFRUyxhQUFhRyxPQUFPLENBQUM7WUFDbkMsSUFBSVosT0FBTztnQkFDVCxNQUFNWCwyQ0FBR0EsQ0FBQ2dDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxHQUFHO29CQUNqQ25CLFNBQVM7d0JBQ1BDLGVBQWUsVUFBZ0IsT0FBTkg7b0JBQzNCO2dCQUNGO2dCQUNBUSxRQUFRNkIsR0FBRyxDQUFDO1lBQ2Q7UUFDRixFQUFFLE9BQU85QixPQUFPO1lBQ2RDLFFBQVE4QixJQUFJLENBQUMsb0NBQW9DL0IsTUFBTW1CLE9BQU87UUFDOUQseURBQXlEO1FBQzNELFNBQVU7WUFDUixzQkFBc0I7WUFDdEJqQixhQUFhQyxVQUFVLENBQUM7WUFDeEIsT0FBT3JCLDJDQUFHQSxDQUFDWSxRQUFRLENBQUNDLE9BQU8sQ0FBQ0MsYUFBYTtZQUN6Q1IsUUFBUTtZQUNSRyxPQUFPZ0MsSUFBSSxDQUFDO1FBQ2Q7SUFDRjtJQUVBLG9DQUFvQztJQUNwQyxNQUFNUyxVQUFVLENBQUNYO1FBQ2YsSUFBSSxDQUFDbEMsTUFBTSxPQUFPO1FBQ2xCLElBQUk4QyxNQUFNQyxPQUFPLENBQUNiLE9BQU87WUFDdkIsT0FBT0EsS0FBS2MsUUFBUSxDQUFDaEQsS0FBS2tDLElBQUk7UUFDaEM7UUFDQSxPQUFPbEMsS0FBS2tDLElBQUksS0FBS0E7SUFDdkI7SUFFQSxrQ0FBa0M7SUFDbEMsTUFBTWUsZ0JBQWdCO1FBQ3BCLE9BQU9KLFFBQVE7SUFDakI7SUFFQSxtQ0FBbUM7SUFDbkMsTUFBTUssaUJBQWlCO1FBQ3JCLE9BQU9MLFFBQVE7SUFDakI7SUFFQSwrQkFBK0I7SUFDL0IsTUFBTU0sYUFBYTtRQUNqQixPQUFPTixRQUFRO0lBQ2pCO0lBRUEsOENBQThDO0lBQzlDLE1BQU1PLG1CQUFtQixDQUFDQztRQUN4QixJQUFJLENBQUNyRCxRQUFRLENBQUNBLEtBQUtxRCxTQUFTLEVBQUUsT0FBTztRQUNyQyxPQUFPckQsS0FBS3FELFNBQVMsS0FBS0E7SUFDNUI7SUFFQSxvQ0FBb0M7SUFDcEMsTUFBTUMsWUFBWSxDQUFDQztRQUNqQixJQUFJLENBQUN2RCxRQUFRLENBQUNBLEtBQUt3RCxPQUFPLEVBQUUsT0FBTztRQUNuQyxPQUFPeEQsS0FBS3dELE9BQU8sQ0FBQ1IsUUFBUSxDQUFDTztJQUMvQjtJQUVBLG9FQUFvRTtJQUNwRSxNQUFNRSxrQkFBa0I7UUFDdEIsSUFBSTtZQUNGLE1BQU1uRCxRQUFRUyxhQUFhRyxPQUFPLENBQUM7WUFDbkMsSUFBSVosT0FBTztnQkFDVCxNQUFNYSxXQUFXLE1BQU1kLGNBQWNDO2dCQUNyQyxJQUFJYSxVQUFVO29CQUNabEIsUUFBUWtCO29CQUNSLE9BQU87Z0JBQ1Q7WUFDRjtZQUNBLE9BQU87UUFDVCxFQUFFLE9BQU9OLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLCtCQUErQkE7WUFDN0MsT0FBTztRQUNUO0lBQ0Y7SUFFQSxtQkFBbUI7SUFDbkIsTUFBTTZDLGlCQUFpQixPQUFPNUIsUUFBUXhCO1FBQ3BDLElBQUk7WUFDRixNQUFNSSxXQUFXLE1BQU1mLDJDQUFHQSxDQUFDZ0MsSUFBSSxDQUFDLG9CQUFvQjtnQkFDbERHO2dCQUNBeEI7WUFDRjtZQUVBLE1BQU0sRUFBRUEsT0FBT3FELFNBQVMsRUFBRTNELE1BQU1tQixRQUFRLEVBQUUsR0FBR1QsU0FBU0UsSUFBSTtZQUUxREcsYUFBYWtCLE9BQU8sQ0FBQyxTQUFTMEI7WUFDOUJoRSwyQ0FBR0EsQ0FBQ1ksUUFBUSxDQUFDQyxPQUFPLENBQUNDLGFBQWEsR0FBRyxVQUFvQixPQUFWa0Q7WUFFL0MxRCxRQUFRO2dCQUNOLEdBQUdrQixRQUFRO2dCQUNYZSxNQUFNZixTQUFTZSxJQUFJLElBQUtmLENBQUFBLFNBQVNnQixRQUFRLEdBQUcsV0FBVyxVQUFTO1lBQ2xFO1lBRUEvQixPQUFPZ0MsSUFBSSxDQUFDO1lBQ1osT0FBTztnQkFBRVAsU0FBUztZQUFLO1FBQ3pCLEVBQUUsT0FBT2hCLE9BQU87Z0JBRU9BLHNCQUFBQTtZQURyQkMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7WUFDekMsTUFBTXdCLGVBQWV4QixFQUFBQSxrQkFBQUEsTUFBTUgsUUFBUSxjQUFkRyx1Q0FBQUEsdUJBQUFBLGdCQUFnQkQsSUFBSSxjQUFwQkMsMkNBQUFBLHFCQUFzQm1CLE9BQU8sS0FBSTtZQUN0RCxNQUFNLElBQUlRLE1BQU1ELHNCQUFzQkY7UUFDeEM7SUFDRjtJQUVBLG1CQUFtQjtJQUNuQixNQUFNdUIsaUJBQWlCLE9BQU85QjtRQUM1QixJQUFJO1lBQ0YsTUFBTXBCLFdBQVcsTUFBTWYsMkNBQUdBLENBQUNnQyxJQUFJLENBQUMsb0JBQW9CO2dCQUFFRztZQUFPO1lBQzdELE9BQU87Z0JBQ0xELFNBQVM7Z0JBQ1RFLFdBQVdyQixTQUFTRSxJQUFJLENBQUNtQixTQUFTO2dCQUNsQ0MsU0FBU3RCLFNBQVNFLElBQUksQ0FBQ29CLE9BQU87WUFDaEM7UUFDRixFQUFFLE9BQU9uQixPQUFPO2dCQUVPQSxzQkFBQUE7WUFEckJDLFFBQVFELEtBQUssQ0FBQyxxQkFBcUJBO1lBQ25DLE1BQU13QixlQUFleEIsRUFBQUEsa0JBQUFBLE1BQU1ILFFBQVEsY0FBZEcsdUNBQUFBLHVCQUFBQSxnQkFBZ0JELElBQUksY0FBcEJDLDJDQUFBQSxxQkFBc0JtQixPQUFPLEtBQUk7WUFDdEQsTUFBTSxJQUFJUSxNQUFNRCxzQkFBc0JGO1FBQ3hDO0lBQ0Y7SUFFQSxzQ0FBc0M7SUFDdEMsTUFBTXdCLFdBQVc7UUFDZixJQUFJLElBQTZCLEVBQUU7WUFDakMsT0FBTzlDLGFBQWFHLE9BQU8sQ0FBQztRQUM5QjtRQUNBLE9BQU87SUFDVDtJQUVBLHFCQUNFLDhEQUFDckIsWUFBWWlFLFFBQVE7UUFDbkJDLE9BQU87WUFDTC9EO1lBQ0FNLE9BQU91RDtZQUNQekM7WUFDQXNCO1lBQ0F4QztZQUNBMkM7WUFDQUk7WUFDQUM7WUFDQUM7WUFDQUM7WUFDQUU7WUFDQUc7WUFDQUM7WUFDQUU7UUFDRjtrQkFFQzdEOzs7Ozs7QUFHUDtHQWxSZ0JEOztRQUdDRixzREFBU0E7OztLQUhWRTtBQW9SVCxNQUFNa0UsVUFBVTs7SUFDckIsTUFBTUMsVUFBVXpFLGlEQUFVQSxDQUFDSztJQUMzQixJQUFJLENBQUNvRSxTQUFTO1FBQ1osTUFBTSxJQUFJekIsTUFBTTtJQUNsQjtJQUNBLE9BQU95QjtBQUNULEVBQUU7SUFOV0QiLCJzb3VyY2VzIjpbIkM6XFxQcm9ncmFtIEZpbGVzICh4ODYpXFxIaWdoIFRpZGVcXGhpZ2gtdGlkZS1zeXN0ZW1zLWZyb250ZW5kXFxzcmNcXGNvbnRleHRzXFxBdXRoQ29udGV4dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyBhcGkgfSBmcm9tICdAL3V0aWxzL2FwaSc7XHJcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XHJcblxyXG5jb25zdCBBdXRoQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQoe30pO1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIEF1dGhQcm92aWRlcih7IGNoaWxkcmVuIH0pIHtcclxuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZShudWxsKTtcclxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcclxuXHJcbiAgLy8gRmV0Y2ggdXNlciBkYXRhIGZyb20gQVBJXHJcbiAgY29uc3QgZmV0Y2hVc2VyRGF0YSA9IGFzeW5jICh0b2tlbikgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgYXBpLmRlZmF1bHRzLmhlYWRlcnMuQXV0aG9yaXphdGlvbiA9IGBCZWFyZXIgJHt0b2tlbn1gO1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5nZXQoJy9hdXRoL21lJyk7XHJcbiAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgdXNlciBkYXRhOicsIGVycm9yKTtcclxuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3Rva2VuJyk7XHJcbiAgICAgIGRlbGV0ZSBhcGkuZGVmYXVsdHMuaGVhZGVycy5BdXRob3JpemF0aW9uO1xyXG4gICAgICByZXR1cm4gbnVsbDtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBJbml0aWFsaXplIGF1dGggb24gcGFnZSBsb2FkXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGluaXRpYWxpemVBdXRoID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyk7XHJcbiAgICAgICAgaWYgKHRva2VuKSB7XHJcbiAgICAgICAgICBjb25zdCB1c2VyRGF0YSA9IGF3YWl0IGZldGNoVXNlckRhdGEodG9rZW4pO1xyXG4gICAgICAgICAgaWYgKHVzZXJEYXRhKSB7XHJcbiAgICAgICAgICAgIHNldFVzZXIodXNlckRhdGEpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbml0aWFsaXppbmcgYXV0aDonLCBlcnJvcik7XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3Rva2VuJyk7XHJcbiAgICAgIH0gZmluYWxseSB7XHJcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgaW5pdGlhbGl6ZUF1dGgoKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8vIExvZ2luIGZ1bmN0aW9uXHJcbiAgY29uc3QgbG9naW4gPSBhc3luYyAoaWRlbnRpZmllciwgcGFzc3dvcmQsIGxvZ2luVHlwZSA9ICdlbWFpbCcpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIFByZXBhcmUgbG9naW4gZGF0YSBiYXNlZCBvbiBsb2dpbiB0eXBlXHJcbiAgICAgIGNvbnN0IGxvZ2luRGF0YSA9IHtcclxuICAgICAgICBwYXNzd29yZCxcclxuICAgICAgICBsb2dpblR5cGVcclxuICAgICAgfTtcclxuXHJcbiAgICAgIC8vIFNldCB0aGUgYXBwcm9wcmlhdGUgZmllbGQgYmFzZWQgb24gbG9naW4gdHlwZVxyXG4gICAgICBpZiAobG9naW5UeXBlID09PSAnZW1haWwnKSB7XHJcbiAgICAgICAgbG9naW5EYXRhLmVtYWlsID0gaWRlbnRpZmllcjtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBsb2dpbkRhdGEudXNlcm5hbWUgPSBpZGVudGlmaWVyO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5wb3N0KCcvYXV0aC9sb2dpbicsIGxvZ2luRGF0YSk7XHJcbiAgICAgIFxyXG4gICAgICAvLyBDaGVjayBpZiAyRkEgaXMgcmVxdWlyZWRcclxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEucmVxdWlyZXNUd29GYWN0b3IpIHtcclxuICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgc3VjY2VzczogZmFsc2UsXHJcbiAgICAgICAgICByZXF1aXJlc1R3b0ZhY3RvcjogdHJ1ZSxcclxuICAgICAgICAgIHVzZXJJZDogcmVzcG9uc2UuZGF0YS51c2VySWQsXHJcbiAgICAgICAgICBleHBpcmVzQXQ6IHJlc3BvbnNlLmRhdGEuZXhwaXJlc0F0LFxyXG4gICAgICAgICAgbWVzc2FnZTogcmVzcG9uc2UuZGF0YS5tZXNzYWdlXHJcbiAgICAgICAgfTtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgeyB0b2tlbiwgdXNlcjogdXNlckRhdGEgfSA9IHJlc3BvbnNlLmRhdGE7XHJcblxyXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgndG9rZW4nLCB0b2tlbik7XHJcbiAgICAgIGFwaS5kZWZhdWx0cy5oZWFkZXJzLkF1dGhvcml6YXRpb24gPSBgQmVhcmVyICR7dG9rZW59YDtcclxuXHJcbiAgICAgIC8vIFNldCB1c2VyIGRhdGEgaW5jbHVkaW5nIHJvbGUgaW5mb3JtYXRpb25cclxuICAgICAgc2V0VXNlcih7XHJcbiAgICAgICAgLi4udXNlckRhdGEsXHJcbiAgICAgICAgLy8gRW5zdXJlIHJvbGUgaXMgcHJvcGVybHkgc2V0LCBkZWZhdWx0aW5nIHRvIEVNUExPWUVFIGlmIG5vdCBwcmVzZW50XHJcbiAgICAgICAgcm9sZTogdXNlckRhdGEucm9sZSB8fCAodXNlckRhdGEuaXNDbGllbnQgPyAnQ0xJRU5UJyA6ICdFTVBMT1lFRScpXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgcm91dGVyLnB1c2goJy9kYXNoYm9hcmQnKTtcclxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSB9O1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignTG9naW4gZXJyb3I6JywgZXJyb3IpO1xyXG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSB8fCAnRXJybyBhbyBmYXplciBsb2dpbic7XHJcbiAgICAgIFxyXG4gICAgICAvLyBUcmFkdXppciBtZW5zYWdlbnMgZGUgZXJybyBlc3BlY8OtZmljYXNcclxuICAgICAgY29uc3QgdHJhbnNsYXRlZE1lc3NhZ2UgPSB0cmFuc2xhdGVFcnJvck1lc3NhZ2UoZXJyb3JNZXNzYWdlKTtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKHRyYW5zbGF0ZWRNZXNzYWdlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBGdW7Dp8OjbyBwYXJhIHRyYWR1emlyIG1lbnNhZ2VucyBkZSBlcnJvIGRvIGJhY2tlbmRcclxuICBjb25zdCB0cmFuc2xhdGVFcnJvck1lc3NhZ2UgPSAobWVzc2FnZSkgPT4ge1xyXG4gICAgY29uc3QgdHJhbnNsYXRpb25zID0ge1xyXG4gICAgICAnSW52YWxpZCBjcmVkZW50aWFscyc6ICdDcmVkZW5jaWFpcyBpbnbDoWxpZGFzJyxcclxuICAgICAgJ0ludmFsaWQgZW1haWwgb3IgcGFzc3dvcmQnOiAnRW1haWwgb3Ugc2VuaGEgaW52w6FsaWRvcycsXHJcbiAgICAgICdVc2VyIG5vdCBmb3VuZCc6ICdVc3XDoXJpbyBuw6NvIGVuY29udHJhZG8nLFxyXG4gICAgICAnQWNjb3VudCBkaXNhYmxlZCc6ICdDb250YSBkZXNhYmlsaXRhZGEnLFxyXG4gICAgICAnQWNjb3VudCBsb2NrZWQnOiAnQ29udGEgYmxvcXVlYWRhJyxcclxuICAgICAgJ1RvbyBtYW55IGxvZ2luIGF0dGVtcHRzJzogJ011aXRhcyB0ZW50YXRpdmFzIGRlIGxvZ2luJyxcclxuICAgICAgJ1Nlc3Npb24gZXhwaXJlZCc6ICdTZXNzw6NvIGV4cGlyYWRhJyxcclxuICAgICAgJ1Rva2VuIGludmFsaWQnOiAnVG9rZW4gaW52w6FsaWRvJyxcclxuICAgICAgJ1Rva2VuIGV4cGlyZWQnOiAnVG9rZW4gZXhwaXJhZG8nLFxyXG4gICAgICAnQWNjZXNzIGRlbmllZCc6ICdBY2Vzc28gbmVnYWRvJyxcclxuICAgICAgJ1VuYXV0aG9yaXplZCc6ICdOw6NvIGF1dG9yaXphZG8nLFxyXG4gICAgICAnRm9yYmlkZGVuJzogJ0FjZXNzbyBwcm9pYmlkbycsXHJcbiAgICAgICdOb3QgZm91bmQnOiAnTsOjbyBlbmNvbnRyYWRvJyxcclxuICAgICAgJ0JhZCByZXF1ZXN0JzogJ1JlcXVpc2nDp8OjbyBpbnbDoWxpZGEnLFxyXG4gICAgICAnSW50ZXJuYWwgc2VydmVyIGVycm9yJzogJ0Vycm8gaW50ZXJubyBkbyBzZXJ2aWRvcicsXHJcbiAgICAgICdTZXJ2aWNlIHVuYXZhaWxhYmxlJzogJ1NlcnZpw6dvIGluZGlzcG9uw612ZWwnLFxyXG4gICAgICAnTmV0d29yayBlcnJvcic6ICdFcnJvIGRlIGNvbmV4w6NvJyxcclxuICAgICAgJ1JlcXVlc3QgdGltZW91dCc6ICdUZW1wbyBsaW1pdGUgZGEgcmVxdWlzacOnw6NvJyxcclxuICAgICAgJ0Nvbm5lY3Rpb24gcmVmdXNlZCc6ICdDb25leMOjbyByZWN1c2FkYScsXHJcbiAgICAgICdTZXJ2ZXIgZXJyb3InOiAnRXJybyBkbyBzZXJ2aWRvcidcclxuICAgIH07XHJcblxyXG4gICAgcmV0dXJuIHRyYW5zbGF0aW9uc1ttZXNzYWdlXSB8fCBtZXNzYWdlO1xyXG4gIH07XHJcblxyXG4gIC8vIExvZ291dCBmdW5jdGlvblxyXG4gIGNvbnN0IGxvZ291dCA9IGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIENoYW1hciBlbmRwb2ludCBkZSBsb2dvdXQgZG8gYmFja2VuZCBwYXJhIGludmFsaWRhciBvIHRva2VuIGUgZGVzY29uZWN0YXIgc29ja2V0c1xyXG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpO1xyXG4gICAgICBpZiAodG9rZW4pIHtcclxuICAgICAgICBhd2FpdCBhcGkucG9zdCgnL2F1dGgvbG9nb3V0Jywge30sIHtcclxuICAgICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWBcclxuICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuICAgICAgICBjb25zb2xlLmxvZygnTG9nb3V0IHJlYWxpemFkbyBjb20gc3VjZXNzbyBubyBiYWNrZW5kJyk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUud2FybignRXJybyBhbyBmYXplciBsb2dvdXQgbm8gYmFja2VuZDonLCBlcnJvci5tZXNzYWdlKTtcclxuICAgICAgLy8gQ29udGludWFyIGNvbSBvIGxvZ291dCBsb2NhbCBtZXNtbyBzZSBvIGJhY2tlbmQgZmFsaGFyXHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICAvLyBMaW1wYXIgZXN0YWRvIGxvY2FsXHJcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd0b2tlbicpO1xyXG4gICAgICBkZWxldGUgYXBpLmRlZmF1bHRzLmhlYWRlcnMuQXV0aG9yaXphdGlvbjtcclxuICAgICAgc2V0VXNlcihudWxsKTtcclxuICAgICAgcm91dGVyLnB1c2goJy9sb2dpbicpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIENoZWNrIGlmIHVzZXIgaGFzIGEgc3BlY2lmaWMgcm9sZVxyXG4gIGNvbnN0IGhhc1JvbGUgPSAocm9sZSkgPT4ge1xyXG4gICAgaWYgKCF1c2VyKSByZXR1cm4gZmFsc2U7XHJcbiAgICBpZiAoQXJyYXkuaXNBcnJheShyb2xlKSkge1xyXG4gICAgICByZXR1cm4gcm9sZS5pbmNsdWRlcyh1c2VyLnJvbGUpO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIHVzZXIucm9sZSA9PT0gcm9sZTtcclxuICB9O1xyXG5cclxuICAvLyBDaGVjayBpZiB1c2VyIGlzIGEgc3lzdGVtIGFkbWluXHJcbiAgY29uc3QgaXNTeXN0ZW1BZG1pbiA9ICgpID0+IHtcclxuICAgIHJldHVybiBoYXNSb2xlKCdTWVNURU1fQURNSU4nKTtcclxuICB9O1xyXG5cclxuICAvLyBDaGVjayBpZiB1c2VyIGlzIGEgY29tcGFueSBhZG1pblxyXG4gIGNvbnN0IGlzQ29tcGFueUFkbWluID0gKCkgPT4ge1xyXG4gICAgcmV0dXJuIGhhc1JvbGUoJ0NPTVBBTllfQURNSU4nKTtcclxuICB9O1xyXG5cclxuICAvLyBDaGVjayBpZiB1c2VyIGlzIGFuIGVtcGxveWVlXHJcbiAgY29uc3QgaXNFbXBsb3llZSA9ICgpID0+IHtcclxuICAgIHJldHVybiBoYXNSb2xlKCdFTVBMT1lFRScpO1xyXG4gIH07XHJcblxyXG4gIC8vIENoZWNrIGlmIHVzZXIgYmVsb25ncyB0byBhIHNwZWNpZmljIGNvbXBhbnlcclxuICBjb25zdCBiZWxvbmdzVG9Db21wYW55ID0gKGNvbXBhbnlJZCkgPT4ge1xyXG4gICAgaWYgKCF1c2VyIHx8ICF1c2VyLmNvbXBhbnlJZCkgcmV0dXJuIGZhbHNlO1xyXG4gICAgcmV0dXJuIHVzZXIuY29tcGFueUlkID09PSBjb21wYW55SWQ7XHJcbiAgfTtcclxuXHJcbiAgLy8gQ2hlY2sgaWYgdXNlciBoYXMgc3BlY2lmaWMgbW9kdWxlXHJcbiAgY29uc3QgaGFzTW9kdWxlID0gKG1vZHVsZUlkKSA9PiB7XHJcbiAgICBpZiAoIXVzZXIgfHwgIXVzZXIubW9kdWxlcykgcmV0dXJuIGZhbHNlO1xyXG4gICAgcmV0dXJuIHVzZXIubW9kdWxlcy5pbmNsdWRlcyhtb2R1bGVJZCk7XHJcbiAgfTtcclxuXHJcbiAgLy8gQXR1YWxpemFyIGluZm9ybWHDp8O1ZXMgZG8gdXN1w6FyaW8gKGV4OiBhcMOzcyBhdHVhbGl6YcOnw6NvIGRlIHBlcmZpbClcclxuICBjb25zdCByZWZyZXNoVXNlckRhdGEgPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpO1xyXG4gICAgICBpZiAodG9rZW4pIHtcclxuICAgICAgICBjb25zdCB1c2VyRGF0YSA9IGF3YWl0IGZldGNoVXNlckRhdGEodG9rZW4pO1xyXG4gICAgICAgIGlmICh1c2VyRGF0YSkge1xyXG4gICAgICAgICAgc2V0VXNlcih1c2VyRGF0YSk7XHJcbiAgICAgICAgICByZXR1cm4gdHJ1ZTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgICAgcmV0dXJuIGZhbHNlO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgcmVmcmVzaGluZyB1c2VyIGRhdGE6JywgZXJyb3IpO1xyXG4gICAgICByZXR1cm4gZmFsc2U7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8gVmVyaWZ5IDJGQSB0b2tlblxyXG4gIGNvbnN0IHZlcmlmeTJGQVRva2VuID0gYXN5bmMgKHVzZXJJZCwgdG9rZW4pID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnBvc3QoJy9hdXRoL3ZlcmlmeS0yZmEnLCB7XHJcbiAgICAgICAgdXNlcklkLFxyXG4gICAgICAgIHRva2VuXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY29uc3QgeyB0b2tlbjogYXV0aFRva2VuLCB1c2VyOiB1c2VyRGF0YSB9ID0gcmVzcG9uc2UuZGF0YTtcclxuXHJcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd0b2tlbicsIGF1dGhUb2tlbik7XHJcbiAgICAgIGFwaS5kZWZhdWx0cy5oZWFkZXJzLkF1dGhvcml6YXRpb24gPSBgQmVhcmVyICR7YXV0aFRva2VufWA7XHJcblxyXG4gICAgICBzZXRVc2VyKHtcclxuICAgICAgICAuLi51c2VyRGF0YSxcclxuICAgICAgICByb2xlOiB1c2VyRGF0YS5yb2xlIHx8ICh1c2VyRGF0YS5pc0NsaWVudCA/ICdDTElFTlQnIDogJ0VNUExPWUVFJylcclxuICAgICAgfSk7XHJcblxyXG4gICAgICByb3V0ZXIucHVzaCgnL2Rhc2hib2FyZCcpO1xyXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlIH07XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCcyRkEgdmVyaWZpY2F0aW9uIGVycm9yOicsIGVycm9yKTtcclxuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwgJ0Vycm8gYW8gdmVyaWZpY2FyIGPDs2RpZ28gMkZBJztcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKHRyYW5zbGF0ZUVycm9yTWVzc2FnZShlcnJvck1lc3NhZ2UpKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBSZXNlbmQgMkZBIHRva2VuXHJcbiAgY29uc3QgcmVzZW5kMkZBVG9rZW4gPSBhc3luYyAodXNlcklkKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5wb3N0KCcvYXV0aC9yZXNlbmQtMmZhJywgeyB1c2VySWQgfSk7XHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgc3VjY2VzczogdHJ1ZSxcclxuICAgICAgICBleHBpcmVzQXQ6IHJlc3BvbnNlLmRhdGEuZXhwaXJlc0F0LFxyXG4gICAgICAgIG1lc3NhZ2U6IHJlc3BvbnNlLmRhdGEubWVzc2FnZVxyXG4gICAgICB9O1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignUmVzZW5kIDJGQSBlcnJvcjonLCBlcnJvcik7XHJcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICdFcnJvIGFvIHJlZW52aWFyIGPDs2RpZ28gMkZBJztcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKHRyYW5zbGF0ZUVycm9yTWVzc2FnZShlcnJvck1lc3NhZ2UpKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBPYnRlciBvIHRva2VuIGF0dWFsIGRvIGxvY2FsU3RvcmFnZVxyXG4gIGNvbnN0IGdldFRva2VuID0gKCkgPT4ge1xyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgIHJldHVybiBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcclxuICAgIH1cclxuICAgIHJldHVybiBudWxsO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8QXV0aENvbnRleHQuUHJvdmlkZXJcclxuICAgICAgdmFsdWU9e3tcclxuICAgICAgICB1c2VyLFxyXG4gICAgICAgIHRva2VuOiBnZXRUb2tlbigpLFxyXG4gICAgICAgIGxvZ2luLFxyXG4gICAgICAgIGxvZ291dCxcclxuICAgICAgICBsb2FkaW5nLFxyXG4gICAgICAgIGhhc1JvbGUsXHJcbiAgICAgICAgaXNTeXN0ZW1BZG1pbixcclxuICAgICAgICBpc0NvbXBhbnlBZG1pbixcclxuICAgICAgICBpc0VtcGxveWVlLFxyXG4gICAgICAgIGJlbG9uZ3NUb0NvbXBhbnksXHJcbiAgICAgICAgaGFzTW9kdWxlLFxyXG4gICAgICAgIHJlZnJlc2hVc2VyRGF0YSxcclxuICAgICAgICB2ZXJpZnkyRkFUb2tlbixcclxuICAgICAgICByZXNlbmQyRkFUb2tlblxyXG4gICAgICB9fVxyXG4gICAgPlxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICA8L0F1dGhDb250ZXh0LlByb3ZpZGVyPlxyXG4gICk7XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCB1c2VBdXRoID0gKCkgPT4ge1xyXG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KEF1dGhDb250ZXh0KTtcclxuICBpZiAoIWNvbnRleHQpIHtcclxuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXV0aCBkZXZlIHNlciB1c2FkbyBkZW50cm8gZGUgdW0gQXV0aFByb3ZpZGVyJyk7XHJcbiAgfVxyXG4gIHJldHVybiBjb250ZXh0O1xyXG59OyJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiYXBpIiwidXNlUm91dGVyIiwiQXV0aENvbnRleHQiLCJBdXRoUHJvdmlkZXIiLCJjaGlsZHJlbiIsInVzZXIiLCJzZXRVc2VyIiwibG9hZGluZyIsInNldExvYWRpbmciLCJyb3V0ZXIiLCJmZXRjaFVzZXJEYXRhIiwidG9rZW4iLCJkZWZhdWx0cyIsImhlYWRlcnMiLCJBdXRob3JpemF0aW9uIiwicmVzcG9uc2UiLCJnZXQiLCJkYXRhIiwiZXJyb3IiLCJjb25zb2xlIiwibG9jYWxTdG9yYWdlIiwicmVtb3ZlSXRlbSIsImluaXRpYWxpemVBdXRoIiwiZ2V0SXRlbSIsInVzZXJEYXRhIiwibG9naW4iLCJpZGVudGlmaWVyIiwicGFzc3dvcmQiLCJsb2dpblR5cGUiLCJsb2dpbkRhdGEiLCJlbWFpbCIsInVzZXJuYW1lIiwicG9zdCIsInJlcXVpcmVzVHdvRmFjdG9yIiwic3VjY2VzcyIsInVzZXJJZCIsImV4cGlyZXNBdCIsIm1lc3NhZ2UiLCJzZXRJdGVtIiwicm9sZSIsImlzQ2xpZW50IiwicHVzaCIsImVycm9yTWVzc2FnZSIsInRyYW5zbGF0ZWRNZXNzYWdlIiwidHJhbnNsYXRlRXJyb3JNZXNzYWdlIiwiRXJyb3IiLCJ0cmFuc2xhdGlvbnMiLCJsb2dvdXQiLCJsb2ciLCJ3YXJuIiwiaGFzUm9sZSIsIkFycmF5IiwiaXNBcnJheSIsImluY2x1ZGVzIiwiaXNTeXN0ZW1BZG1pbiIsImlzQ29tcGFueUFkbWluIiwiaXNFbXBsb3llZSIsImJlbG9uZ3NUb0NvbXBhbnkiLCJjb21wYW55SWQiLCJoYXNNb2R1bGUiLCJtb2R1bGVJZCIsIm1vZHVsZXMiLCJyZWZyZXNoVXNlckRhdGEiLCJ2ZXJpZnkyRkFUb2tlbiIsImF1dGhUb2tlbiIsInJlc2VuZDJGQVRva2VuIiwiZ2V0VG9rZW4iLCJQcm92aWRlciIsInZhbHVlIiwidXNlQXV0aCIsImNvbnRleHQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.js\n"));

/***/ })

});