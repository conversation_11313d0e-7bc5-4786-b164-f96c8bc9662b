"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(auth)/login/page",{

/***/ "(app-pages-browser)/./src/app/(auth)/login/page.js":
/*!**************************************!*\
  !*** ./src/app/(auth)/login/page.js ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,HelpCircle,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,HelpCircle,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,HelpCircle,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,HelpCircle,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,HelpCircle,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,HelpCircle,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _config_appConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/appConfig */ \"(app-pages-browser)/./src/config/appConfig.js\");\n/* harmony import */ var _components_auth_TwoFactorModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/auth/TwoFactorModal */ \"(app-pages-browser)/./src/components/auth/TwoFactorModal.js\");\n// app/(auth)/login/page.js\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction LoginPage() {\n    _s();\n    const [loginType, setLoginType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('email'); // 'email' ou 'username'\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [show2FAModal, setShow2FAModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [twoFactorData, setTwoFactorData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { login, verify2FAToken, resend2FAToken, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        try {\n            // Decide qual dado enviar com base no tipo de login selecionado\n            const identifier = loginType === 'email' ? email : username;\n            const result = await login(identifier, password, loginType);\n            // Check if 2FA is required\n            if (result && result.requiresTwoFactor) {\n                setTwoFactorData({\n                    userId: result.userId,\n                    expiresAt: result.expiresAt\n                });\n                setShow2FAModal(true);\n            }\n        } catch (err) {\n            if (err.message === 'USER_LIMIT_EXCEEDED') {\n                setError('O limite de usuários ativos do seu plano foi excedido. Entre em contato com o administrador da empresa para liberar acesso.');\n            } else {\n                setError(err.message || 'Erro ao fazer login');\n            }\n        }\n    };\n    const toggleShowPassword = ()=>{\n        setShowPassword(!showPassword);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-orange-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-orange-50 dark:bg-background flex justify-items-center flex-col md:flex-row transition-colors\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full flex items-center justify-center p-6 bg-gradient-to-br from-orange-200 to-orange-600 dark:from-background dark:to-background transition-colors\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-orange-50/80 dark:bg-[#23272f]/90 rounded-2xl shadow-xl w-full max-w-md p-8 transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"/logo_horizontal_sem_fundo.png\",\n                                alt: \"High Tide Logo\",\n                                className: \"max-w-full max-h-20 dark:invert transition-all\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute -bottom-1 right-3 text-xs text-gray-500 dark:text-gray-400 font-mono\",\n                                children: _config_appConfig__WEBPACK_IMPORTED_MODULE_4__.APP_VERSION\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 dark:bg-red-900 text-red-500 dark:text-red-200 p-3 rounded-lg text-sm mb-6 transition-colors\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                        lineNumber: 74,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex mb-6 border-b border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex-1 py-3 px-4 text-center font-medium text-sm transition-colors \".concat(loginType === 'username' ? 'border-b-2 border-orange-500 text-orange-600 dark:text-orange-400 dark:border-orange-400' : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'),\n                                onClick: ()=>setLoginType('username'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            size: 16,\n                                            className: \"mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Entrar com Login\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex-1 py-3 px-4 text-center font-medium text-sm transition-colors \".concat(loginType === 'email' ? 'border-b-2 border-orange-500 text-orange-600 dark:text-orange-400 dark:border-orange-400' : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'),\n                                onClick: ()=>setLoginType('email'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            size: 16,\n                                            className: \"mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Entrar com Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-6\",\n                        children: [\n                            loginType === 'email' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-200\",\n                                        htmlFor: \"email\",\n                                        children: \"Email\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                        lineNumber: 112,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1 relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-y-0 left-0 pl-3 flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"email\",\n                                                type: \"email\",\n                                                required: true,\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                className: \"block w-full text-gray-600 dark:text-gray-100 pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:focus:ring-orange-400 dark:focus:border-orange-400 bg-white dark:bg-background\",\n                                                placeholder: \"<EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                lineNumber: 111,\n                                columnNumber: 15\n                            }, this),\n                            loginType === 'username' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-200\",\n                                        htmlFor: \"username\",\n                                        children: \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                        lineNumber: 135,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1 relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-y-0 left-0 pl-3 flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"username\",\n                                                type: \"text\",\n                                                required: true,\n                                                value: username,\n                                                onChange: (e)=>setUsername(e.target.value),\n                                                className: \"block w-full text-gray-600 dark:text-gray-100 pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:focus:ring-orange-400 dark:focus:border-orange-400 bg-white dark:bg-background\",\n                                                placeholder: \"Seu Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                lineNumber: 142,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                lineNumber: 134,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-200\",\n                                                htmlFor: \"password\",\n                                                children: \"Senha\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/forgot-password\",\n                                                className: \"text-sm text-orange-500 dark:text-orange-400 hover:text-orange-600 dark:hover:text-orange-300\",\n                                                children: \"Esqueci a senha\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1 relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-y-0 left-0 pl-3 flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"password\",\n                                                type: showPassword ? 'text' : 'password',\n                                                required: true,\n                                                value: password,\n                                                onChange: (e)=>setPassword(e.target.value),\n                                                className: \"block w-full text-gray-600 dark:text-gray-100 pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:focus:ring-orange-400 dark:focus:border-orange-400 bg-white dark:bg-background\",\n                                                placeholder: \"••••••••\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: toggleShowPassword,\n                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"w-full py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-orange-500 dark:bg-orange-600 hover:bg-orange-600 dark:hover:bg-orange-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-orange-400 transition-colors\",\n                                children: \"Entrar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-300 mr-1\",\n                                        children: \"Ainda n\\xe3o tem conta?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/subscription/signup\",\n                                        className: \"text-sm text-orange-500 dark:text-orange-400 hover:text-orange-600 dark:hover:text-orange-300 font-medium transition-colors\",\n                                        children: \"Criar uma conta\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/tutorial\",\n                            className: \"text-sm text-gray-500 dark:text-gray-300 hover:text-orange-500 dark:hover:text-orange-400 flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    size: 16,\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this),\n                                \"Precisa de ajuda? Acesse nossos tutoriais\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 text-center text-xs text-gray-500 dark:text-gray-400\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"Ao entrar, voc\\xea concorda com nossos\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/legal/termos-uso\",\n                                    className: \"text-orange-500 dark:text-orange-400 hover:underline\",\n                                    children: \"Termos de Uso\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this),\n                                ' ',\n                                \"e\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/legal/politica-privacidade\",\n                                    className: \"text-orange-500 dark:text-orange-400 hover:underline\",\n                                    children: \"Pol\\xedtica de Privacidade\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                            lineNumber: 222,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"UJAe0PD82SJXcX7DcD4ZyGjXqOc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(auth)/login/page.js\n"));

/***/ })

});