"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar/ClientSidebar.js":
/*!***********************************************************!*\
  !*** ./src/components/dashboard/Sidebar/ClientSidebar.js ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronLeft,Clock,FileText,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronLeft,Clock,FileText,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronLeft,Clock,FileText,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronLeft,Clock,FileText,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronLeft,Clock,FileText,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronLeft,Clock,FileText,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/CustomScrollArea */ \"(app-pages-browser)/./src/components/ui/CustomScrollArea.js\");\n// src/components/dashboard/Sidebar/ClientSidebar.js\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Client-specific submenu configuration\nconst clientModuleSubmenus = {\n    people: [\n        {\n            id: 'persons',\n            title: 'Pessoas',\n            icon: _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            description: 'Gerenciar pessoas relacionadas'\n        }\n    ],\n    scheduler: [\n        {\n            id: 'calendar',\n            title: 'Calendário',\n            icon: _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            description: 'Visualizar agenda'\n        },\n        {\n            id: 'appointments',\n            title: 'Meus Agendamentos',\n            icon: _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: 'Visualizar meus agendamentos'\n        },\n        {\n            id: 'requests',\n            title: 'Minhas Solicitações',\n            icon: _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: 'Acompanhar solicitações de agendamento'\n        }\n    ],\n    profile: [\n        {\n            id: 'profile',\n            title: 'Meu Perfil',\n            icon: _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: 'Gerenciar meu perfil e dados pessoais'\n        }\n    ]\n};\nconst ModuleTitle = (param)=>{\n    let { moduleId, title, icon: Icon, onDashboardClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6 flex justify-center items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: onDashboardClick,\n            style: {\n                background: 'none',\n                border: 'none',\n                padding: 0\n            },\n            className: \"focus:outline-none cursor-pointer\",\n            \"aria-label\": \"Ir para o dashboard\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/logo_horizontal_sem_fundo.png\",\n                    alt: \"High Tide Logo\",\n                    className: \"h-12 dark:invert dark:text-white transition-all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                    lineNumber: 33,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ModuleTitle;\nconst ClientSidebar = (param)=>{\n    let { activeModule, activeModuleTitle, isSubmenuActive, handleModuleSubmenuClick, handleBackToModules, isSidebarOpen, isMinimized, toggleMinimized } = param;\n    var _clientModuleSubmenus_activeModule;\n    // Encontrar o objeto do módulo ativo para acessar seu ícone\n    const ModuleIcon = activeModule === 'people' ? _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"] : activeModule === 'scheduler' ? _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"] : activeModule === 'profile' ? _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"] : _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"\".concat(isMinimized ? 'w-16' : 'w-72', \" bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col \").concat(isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'),\n        \"aria-label\": \"Navega\\xe7\\xe3o lateral\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"flex-1 \".concat(isMinimized ? 'p-2' : 'p-5'),\n                moduleColor: activeModule,\n                children: [\n                    activeModule && !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModuleTitle, {\n                        moduleId: activeModule,\n                        title: activeModuleTitle,\n                        icon: ModuleIcon,\n                        onDashboardClick: handleBackToModules\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2\",\n                        \"aria-labelledby\": \"sidebar-heading\",\n                        children: activeModule && ((_clientModuleSubmenus_activeModule = clientModuleSubmenus[activeModule]) === null || _clientModuleSubmenus_activeModule === void 0 ? void 0 : _clientModuleSubmenus_activeModule.map((submenu)=>{\n                            const isActive = isSubmenuActive(activeModule, submenu.id);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleModuleSubmenuClick(activeModule, submenu.id),\n                                className: \"\\n                  group \".concat(isMinimized ? 'w-12 h-12 flex items-center justify-center rounded-full mx-auto' : 'w-full flex items-center gap-3 px-4 py-3 rounded-lg', \" transition-all duration-300\\n                  \").concat(isActive ? \"\".concat(isMinimized ? 'rounded-full' : 'rounded-xl', \" border border-module-\").concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                       bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                       shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n                \"),\n                                \"aria-current\": isActive ? 'page' : undefined,\n                                title: isMinimized ? submenu.title : undefined,\n                                children: [\n                                    submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\\n                    \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                  \"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                            size: 20,\n                                            \"aria-hidden\": \"true\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                                            lineNumber: 108,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                                        lineNumber: 102,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                        children: submenu.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                                        lineNumber: 114,\n                                        columnNumber: 34\n                                    }, undefined)\n                                ]\n                            }, submenu.id, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, undefined);\n                        }))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(isMinimized ? 'p-2' : 'p-5', \" border-t border-gray-100 dark:border-gray-700 mt-auto\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToModules,\n                    className: \"\\n            group \".concat(isMinimized ? 'w-12 h-12 flex items-center justify-center rounded-full mx-auto' : 'w-full py-3 px-4 rounded-lg flex items-center gap-3', \"\\n            border-2 border-module-\").concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n            bg-transparent dark:bg-gray-800 hover:bg-module-\").concat(activeModule, \"-bg/10 dark:hover:bg-module-\").concat(activeModule, \"-bg-dark/10\\n            transition-all duration-300\\n          \"),\n                    title: isMinimized ? \"Voltar a Tela Inicial\" : undefined,\n                    \"aria-label\": \"Voltar para o dashboard principal\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            flex items-center justify-center w-8 h-8 rounded-full\\n            bg-module-\".concat(activeModule, \"-bg dark:bg-module-\").concat(activeModule, \"-bg-dark/70\\n            text-module-\").concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\\n            shadow-sm dark:shadow-md dark:shadow-black/20\\n            group-hover:scale-110 transition-transform duration-200\\n          \"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, undefined),\n                        !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-gray-800 dark:text-gray-200\",\n                            children: \"Voltar a Tela Inicial\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                            lineNumber: 143,\n                            columnNumber: 28\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ClientSidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClientSidebar);\nvar _c, _c1;\n$RefreshReg$(_c, \"ModuleTitle\");\n$RefreshReg$(_c1, \"ClientSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9TaWRlYmFyL0NsaWVudFNpZGViYXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBQSxvREFBb0Q7O0FBRzFCO0FBQ3lEO0FBQ25CO0FBRWhFLHdDQUF3QztBQUN4QyxNQUFNUSx1QkFBdUI7SUFDM0JDLFFBQVE7UUFDTjtZQUFFQyxJQUFJO1lBQVdDLE9BQU87WUFBV0MsTUFBTVYsMEhBQUtBO1lBQUVXLGFBQWE7UUFBaUM7S0FDL0Y7SUFDREMsV0FBVztRQUNUO1lBQUVKLElBQUk7WUFBWUMsT0FBTztZQUFjQyxNQUFNVCwwSEFBUUE7WUFBRVUsYUFBYTtRQUFvQjtRQUN4RjtZQUFFSCxJQUFJO1lBQWdCQyxPQUFPO1lBQXFCQyxNQUFNUCwwSEFBS0E7WUFBRVEsYUFBYTtRQUErQjtRQUMzRztZQUFFSCxJQUFJO1lBQVlDLE9BQU87WUFBdUJDLE1BQU1OLDBIQUFRQTtZQUFFTyxhQUFhO1FBQXlDO0tBQ3ZIO0lBQ0RFLFNBQVM7UUFDUDtZQUFFTCxJQUFJO1lBQVdDLE9BQU87WUFBY0MsTUFBTVIsMEhBQUlBO1lBQUVTLGFBQWE7UUFBd0M7S0FDeEc7QUFDSDtBQUVBLE1BQU1HLGNBQWM7UUFBQyxFQUFFQyxRQUFRLEVBQUVOLEtBQUssRUFBRUMsTUFBTU0sSUFBSSxFQUFFQyxnQkFBZ0IsRUFBRTtJQUNwRSxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0M7WUFDQ0MsU0FBU0o7WUFDVEssT0FBTztnQkFBRUMsWUFBWTtnQkFBUUMsUUFBUTtnQkFBUUMsU0FBUztZQUFFO1lBQ3hETixXQUFVO1lBQ1ZPLGNBQVc7c0JBRVgsNEVBQUNSO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDUTtvQkFDQ0MsS0FBSTtvQkFDSkMsS0FBSTtvQkFDSlYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXRCO0tBbkJNTDtBQXFCTixNQUFNZ0IsZ0JBQWdCO1FBQUMsRUFDckJDLFlBQVksRUFDWkMsaUJBQWlCLEVBQ2pCQyxlQUFlLEVBQ2ZDLHdCQUF3QixFQUN4QkMsbUJBQW1CLEVBQ25CQyxhQUFhLEVBQ2JDLFdBQVcsRUFDWEMsZUFBZSxFQUNoQjtRQTZCMEJoQztJQTVCekIsNERBQTREO0lBQzVELE1BQU1pQyxhQUFhUixpQkFBaUIsV0FBVy9CLDBIQUFLQSxHQUNqQytCLGlCQUFpQixjQUFjOUIsMEhBQVFBLEdBQ3ZDOEIsaUJBQWlCLFlBQVk3QiwwSEFBSUEsR0FBR0EsMEhBQUlBO0lBRTNELHFCQUNFLDhEQUFDc0M7UUFDQ3JCLFdBQVcsR0FDVGlCLE9BRFlDLGNBQWMsU0FBUyxRQUFPLDZIQUUzQyxPQURDRCxnQkFBZ0Isa0JBQWtCO1FBRXBDVixjQUFXOzswQkFHWCw4REFBQ3JCLHVFQUFnQkE7Z0JBQUNjLFdBQVcsVUFBc0MsT0FBNUJrQixjQUFjLFFBQVE7Z0JBQVNJLGFBQWFWOztvQkFFaEZBLGdCQUFnQixDQUFDTSw2QkFDaEIsOERBQUN2Qjt3QkFDQ0MsVUFBVWdCO3dCQUNWdEIsT0FBT3VCO3dCQUNQdEIsTUFBTTZCO3dCQUNOdEIsa0JBQWtCa0I7Ozs7OztrQ0FJdEIsOERBQUNPO3dCQUNDdkIsV0FBVTt3QkFDVndCLG1CQUFnQjtrQ0FFZlosa0JBQWdCekIscUNBQUFBLG9CQUFvQixDQUFDeUIsYUFBYSxjQUFsQ3pCLHlEQUFBQSxtQ0FBb0NzQyxHQUFHLENBQUMsQ0FBQ0M7NEJBQ3hELE1BQU1DLFdBQVdiLGdCQUFnQkYsY0FBY2MsUUFBUXJDLEVBQUU7NEJBRXpELHFCQUNFLDhEQUFDWTtnQ0FFQ0MsU0FBUyxJQUFNYSx5QkFBeUJILGNBQWNjLFFBQVFyQyxFQUFFO2dDQUNoRVcsV0FBVyw2QkFFUDJCLE9BRE1ULGNBQWMsb0VBQW9FLHVEQUFzRCxvREFNL0ksT0FMQ1MsV0FDRSxHQUF1RWYsT0FBcEVNLGNBQWMsaUJBQWlCLGNBQWEsMEJBQWtFTixPQUExQ0EsY0FBYSwrQkFDdkVBLE9BRG9HQSxjQUFhLG1EQUNwRyxPQUFiQSxjQUFhLG9HQUUxQixvUEFDSDtnQ0FFSGdCLGdCQUFjRCxXQUFXLFNBQVNFO2dDQUNsQ3ZDLE9BQU80QixjQUFjUSxRQUFRcEMsS0FBSyxHQUFHdUM7O29DQUVwQ0gsUUFBUW5DLElBQUksa0JBQ1gsOERBQUNRO3dDQUFJQyxXQUFXLHlCQUliLE9BSEMyQixXQUNFLGVBQXFEZixPQUF0Q0EsY0FBYSwyQkFBc0MsT0FBYkEsY0FBYSxnQkFDbEUsNERBQThHQSxPQUFsREEsY0FBYSx1Q0FBa0QsT0FBYkEsY0FBYSw4Q0FDOUg7a0RBRUQsNEVBQUNjLFFBQVFuQyxJQUFJOzRDQUNYdUMsTUFBTTs0Q0FDTkMsZUFBWTs7Ozs7Ozs7Ozs7b0NBSWpCLENBQUNiLDZCQUFlLDhEQUFDYzt3Q0FBS2hDLFdBQVcseUJBQTJELE9BQWxDMkIsV0FBVyxvQkFBb0I7a0RBQU9ELFFBQVFwQyxLQUFLOzs7Ozs7OytCQTNCekdvQyxRQUFRckMsRUFBRTs7Ozs7d0JBOEJyQjs7Ozs7Ozs7Ozs7OzBCQUtKLDhEQUFDVTtnQkFBSUMsV0FBVyxHQUErQixPQUE1QmtCLGNBQWMsUUFBUSxPQUFNOzBCQUM3Qyw0RUFBQ2pCO29CQUNDQyxTQUFTYztvQkFDVGhCLFdBQVcsdUJBRWdCWSxPQURqQk0sY0FBYyxvRUFBb0UsdURBQXNELHlDQUM3RU4sT0FBMUNBLGNBQWEsK0JBQ1lBLE9BRGlCQSxjQUFhLDhFQUNhQSxPQUEzQ0EsY0FBYSxnQ0FBMkMsT0FBYkEsY0FBYTtvQkFHNUd0QixPQUFPNEIsY0FBYywwQkFBMEJXO29CQUMvQ3RCLGNBQVc7O3NDQUVYLDhEQUFDUjs0QkFBSUMsV0FBVyw4RkFFZ0NZLE9BQWxDQSxjQUFhLHVCQUNYQSxPQURnQ0EsY0FBYSx5Q0FDUEEsT0FBdENBLGNBQWEsMkJBQXNDLE9BQWJBLGNBQWE7c0NBSWpFLDRFQUFDaEMsMEhBQVdBO2dDQUFDa0QsTUFBTTtnQ0FBSUMsZUFBWTs7Ozs7Ozs7Ozs7d0JBRXBDLENBQUNiLDZCQUFlLDhEQUFDYzs0QkFBS2hDLFdBQVU7c0NBQStDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUsxRjtNQXhHTVc7QUEwR04saUVBQWVBLGFBQWFBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxQcm9ncmFtIEZpbGVzICh4ODYpXFxIaWdoIFRpZGVcXGhpZ2gtdGlkZS1zeXN0ZW1zLWZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGRhc2hib2FyZFxcU2lkZWJhclxcQ2xpZW50U2lkZWJhci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvU2lkZWJhci9DbGllbnRTaWRlYmFyLmpzXHJcbid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IENoZXZyb25MZWZ0LCBVc2VycywgQ2FsZW5kYXIsIFVzZXIsIENsb2NrLCBGaWxlVGV4dCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XHJcbmltcG9ydCBDdXN0b21TY3JvbGxBcmVhIGZyb20gJ0AvY29tcG9uZW50cy91aS9DdXN0b21TY3JvbGxBcmVhJztcclxuXHJcbi8vIENsaWVudC1zcGVjaWZpYyBzdWJtZW51IGNvbmZpZ3VyYXRpb25cclxuY29uc3QgY2xpZW50TW9kdWxlU3VibWVudXMgPSB7XHJcbiAgcGVvcGxlOiBbXHJcbiAgICB7IGlkOiAncGVyc29ucycsIHRpdGxlOiAnUGVzc29hcycsIGljb246IFVzZXJzLCBkZXNjcmlwdGlvbjogJ0dlcmVuY2lhciBwZXNzb2FzIHJlbGFjaW9uYWRhcycgfSxcclxuICBdLFxyXG4gIHNjaGVkdWxlcjogW1xyXG4gICAgeyBpZDogJ2NhbGVuZGFyJywgdGl0bGU6ICdDYWxlbmTDoXJpbycsIGljb246IENhbGVuZGFyLCBkZXNjcmlwdGlvbjogJ1Zpc3VhbGl6YXIgYWdlbmRhJyB9LFxyXG4gICAgeyBpZDogJ2FwcG9pbnRtZW50cycsIHRpdGxlOiAnTWV1cyBBZ2VuZGFtZW50b3MnLCBpY29uOiBDbG9jaywgZGVzY3JpcHRpb246ICdWaXN1YWxpemFyIG1ldXMgYWdlbmRhbWVudG9zJyB9LFxyXG4gICAgeyBpZDogJ3JlcXVlc3RzJywgdGl0bGU6ICdNaW5oYXMgU29saWNpdGHDp8O1ZXMnLCBpY29uOiBGaWxlVGV4dCwgZGVzY3JpcHRpb246ICdBY29tcGFuaGFyIHNvbGljaXRhw6fDtWVzIGRlIGFnZW5kYW1lbnRvJyB9LFxyXG4gIF0sXHJcbiAgcHJvZmlsZTogW1xyXG4gICAgeyBpZDogJ3Byb2ZpbGUnLCB0aXRsZTogJ01ldSBQZXJmaWwnLCBpY29uOiBVc2VyLCBkZXNjcmlwdGlvbjogJ0dlcmVuY2lhciBtZXUgcGVyZmlsIGUgZGFkb3MgcGVzc29haXMnIH0sXHJcbiAgXVxyXG59O1xyXG5cclxuY29uc3QgTW9kdWxlVGl0bGUgPSAoeyBtb2R1bGVJZCwgdGl0bGUsIGljb246IEljb24sIG9uRGFzaGJvYXJkQ2xpY2sgfSkgPT4ge1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgZmxleCBqdXN0aWZ5LWNlbnRlciBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgPGJ1dHRvblxyXG4gICAgICAgIG9uQ2xpY2s9e29uRGFzaGJvYXJkQ2xpY2t9XHJcbiAgICAgICAgc3R5bGU9e3sgYmFja2dyb3VuZDogJ25vbmUnLCBib3JkZXI6ICdub25lJywgcGFkZGluZzogMCB9fVxyXG4gICAgICAgIGNsYXNzTmFtZT1cImZvY3VzOm91dGxpbmUtbm9uZSBjdXJzb3ItcG9pbnRlclwiXHJcbiAgICAgICAgYXJpYS1sYWJlbD1cIklyIHBhcmEgbyBkYXNoYm9hcmRcIlxyXG4gICAgICA+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxyXG4gICAgICAgICAgPGltZ1xyXG4gICAgICAgICAgICBzcmM9XCIvbG9nb19ob3Jpem9udGFsX3NlbV9mdW5kby5wbmdcIlxyXG4gICAgICAgICAgICBhbHQ9XCJIaWdoIFRpZGUgTG9nb1wiXHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtMTIgZGFyazppbnZlcnQgZGFyazp0ZXh0LXdoaXRlIHRyYW5zaXRpb24tYWxsXCJcclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvYnV0dG9uPlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuXHJcbmNvbnN0IENsaWVudFNpZGViYXIgPSAoe1xyXG4gIGFjdGl2ZU1vZHVsZSxcclxuICBhY3RpdmVNb2R1bGVUaXRsZSxcclxuICBpc1N1Ym1lbnVBY3RpdmUsXHJcbiAgaGFuZGxlTW9kdWxlU3VibWVudUNsaWNrLFxyXG4gIGhhbmRsZUJhY2tUb01vZHVsZXMsXHJcbiAgaXNTaWRlYmFyT3BlbixcclxuICBpc01pbmltaXplZCxcclxuICB0b2dnbGVNaW5pbWl6ZWRcclxufSkgPT4ge1xyXG4gIC8vIEVuY29udHJhciBvIG9iamV0byBkbyBtw7NkdWxvIGF0aXZvIHBhcmEgYWNlc3NhciBzZXUgw61jb25lXHJcbiAgY29uc3QgTW9kdWxlSWNvbiA9IGFjdGl2ZU1vZHVsZSA9PT0gJ3Blb3BsZScgPyBVc2VycyA6XHJcbiAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZU1vZHVsZSA9PT0gJ3NjaGVkdWxlcicgPyBDYWxlbmRhciA6XHJcbiAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZU1vZHVsZSA9PT0gJ3Byb2ZpbGUnID8gVXNlciA6IFVzZXI7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8YXNpZGVcclxuICAgICAgY2xhc3NOYW1lPXtgJHtpc01pbmltaXplZCA/ICd3LTE2JyA6ICd3LTcyJ30gYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCBib3JkZXItciBkYXJrOmJvcmRlci1ncmF5LTcwMCBoLXNjcmVlbiBzdGlja3kgdG9wLTAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGZsZXggZmxleC1jb2wgJHtcclxuICAgICAgICBpc1NpZGViYXJPcGVuID8gJ3RyYW5zbGF0ZS14LTAnIDogJy10cmFuc2xhdGUteC1mdWxsIGxnOnRyYW5zbGF0ZS14LTAnXHJcbiAgICAgIH1gfVxyXG4gICAgICBhcmlhLWxhYmVsPVwiTmF2ZWdhw6fDo28gbGF0ZXJhbFwiXHJcbiAgICA+XHJcbiAgICAgIHsvKiBDb250ZcO6ZG8gcHJpbmNpcGFsIGRhIHNpZGViYXIgKi99XHJcbiAgICAgIDxDdXN0b21TY3JvbGxBcmVhIGNsYXNzTmFtZT17YGZsZXgtMSAke2lzTWluaW1pemVkID8gJ3AtMicgOiAncC01J31gfSBtb2R1bGVDb2xvcj17YWN0aXZlTW9kdWxlfT5cclxuICAgICAgICB7LyogVMOtdHVsbyBkbyBtw7NkdWxvIGVzdGlsaXphZG8gKi99XHJcbiAgICAgICAge2FjdGl2ZU1vZHVsZSAmJiAhaXNNaW5pbWl6ZWQgJiYgKFxyXG4gICAgICAgICAgPE1vZHVsZVRpdGxlXHJcbiAgICAgICAgICAgIG1vZHVsZUlkPXthY3RpdmVNb2R1bGV9XHJcbiAgICAgICAgICAgIHRpdGxlPXthY3RpdmVNb2R1bGVUaXRsZX1cclxuICAgICAgICAgICAgaWNvbj17TW9kdWxlSWNvbn1cclxuICAgICAgICAgICAgb25EYXNoYm9hcmRDbGljaz17aGFuZGxlQmFja1RvTW9kdWxlc31cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgKX1cclxuXHJcbiAgICAgICAgPG5hdlxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwic3BhY2UteS0yXCJcclxuICAgICAgICAgIGFyaWEtbGFiZWxsZWRieT1cInNpZGViYXItaGVhZGluZ1wiXHJcbiAgICAgICAgPlxyXG4gICAgICAgICAge2FjdGl2ZU1vZHVsZSAmJiBjbGllbnRNb2R1bGVTdWJtZW51c1thY3RpdmVNb2R1bGVdPy5tYXAoKHN1Ym1lbnUpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgaXNBY3RpdmUgPSBpc1N1Ym1lbnVBY3RpdmUoYWN0aXZlTW9kdWxlLCBzdWJtZW51LmlkKTtcclxuXHJcbiAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAga2V5PXtzdWJtZW51LmlkfVxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlTW9kdWxlU3VibWVudUNsaWNrKGFjdGl2ZU1vZHVsZSwgc3VibWVudS5pZCl9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BcclxuICAgICAgICAgICAgICAgICAgZ3JvdXAgJHtpc01pbmltaXplZCA/ICd3LTEyIGgtMTIgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1mdWxsIG14LWF1dG8nIDogJ3ctZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyBweC00IHB5LTMgcm91bmRlZC1sZyd9IHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFxyXG4gICAgICAgICAgICAgICAgICAke2lzQWN0aXZlXHJcbiAgICAgICAgICAgICAgICAgICAgPyBgJHtpc01pbmltaXplZCA/ICdyb3VuZGVkLWZ1bGwnIDogJ3JvdW5kZWQteGwnfSBib3JkZXIgYm9yZGVyLW1vZHVsZS0ke2FjdGl2ZU1vZHVsZX0tYm9yZGVyIGRhcms6Ym9yZGVyLW1vZHVsZS0ke2FjdGl2ZU1vZHVsZX0tYm9yZGVyLWRhcmtcclxuICAgICAgICAgICAgICAgICAgICAgICBiZy1tb2R1bGUtJHthY3RpdmVNb2R1bGV9LWJnIGRhcms6YmctZ3JheS03MDAgZGFyazpiZy1vcGFjaXR5LTkwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgc2hhZG93LW1kIGRhcms6c2hhZG93LWJsYWNrLzIwYFxyXG4gICAgICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktMzAwIGhvdmVyOmJnLW1vZHVsZS0ke2FjdGl2ZU1vZHVsZX0taG92ZXIgZGFyazpob3ZlcjpiZy1tb2R1bGUtJHthY3RpdmVNb2R1bGV9LWhvdmVyLWRhcmsgYm9yZGVyIGJvcmRlci10cmFuc3BhcmVudCBob3Zlcjpib3JkZXItbW9kdWxlLSR7YWN0aXZlTW9kdWxlfS1ib3JkZXIgZGFyazpob3Zlcjpib3JkZXItbW9kdWxlLSR7YWN0aXZlTW9kdWxlfS1ib3JkZXItZGFyaydcclxuICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgYH1cclxuICAgICAgICAgICAgICAgIGFyaWEtY3VycmVudD17aXNBY3RpdmUgPyAncGFnZScgOiB1bmRlZmluZWR9XHJcbiAgICAgICAgICAgICAgICB0aXRsZT17aXNNaW5pbWl6ZWQgPyBzdWJtZW51LnRpdGxlIDogdW5kZWZpbmVkfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHtzdWJtZW51Lmljb24gJiYgKFxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YFxyXG4gICAgICAgICAgICAgICAgICAgICR7aXNBY3RpdmVcclxuICAgICAgICAgICAgICAgICAgICAgID8gYHRleHQtbW9kdWxlLSR7YWN0aXZlTW9kdWxlfS1pY29uIGRhcms6dGV4dC1tb2R1bGUtJHthY3RpdmVNb2R1bGV9LWljb24tZGFya2BcclxuICAgICAgICAgICAgICAgICAgICAgIDogYHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwIGdyb3VwLWhvdmVyOnRleHQtbW9kdWxlLSR7YWN0aXZlTW9kdWxlfS1pY29uIGRhcms6Z3JvdXAtaG92ZXI6dGV4dC1tb2R1bGUtJHthY3RpdmVNb2R1bGV9LWljb24tZGFyayB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBgXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICBgfT5cclxuICAgICAgICAgICAgICAgICAgICA8c3VibWVudS5pY29uXHJcbiAgICAgICAgICAgICAgICAgICAgICBzaXplPXsyMH1cclxuICAgICAgICAgICAgICAgICAgICAgIGFyaWEtaGlkZGVuPVwidHJ1ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgeyFpc01pbmltaXplZCAmJiA8c3BhbiBjbGFzc05hbWU9e2Bmb250LW1lZGl1bSB0ZXh0LWxlZnQgJHtpc0FjdGl2ZSA/ICdkYXJrOnRleHQtd2hpdGUnIDogJyd9YH0+e3N1Ym1lbnUudGl0bGV9PC9zcGFuPn1cclxuICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgKTtcclxuICAgICAgICAgIH0pfVxyXG4gICAgICAgIDwvbmF2PlxyXG4gICAgICA8L0N1c3RvbVNjcm9sbEFyZWE+XHJcblxyXG4gICAgICB7LyogQm90w6NvIGRlIHZvbHRhciBmaXhvIG5hIHBhcnRlIGluZmVyaW9yICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17YCR7aXNNaW5pbWl6ZWQgPyAncC0yJyA6ICdwLTUnfSBib3JkZXItdCBib3JkZXItZ3JheS0xMDAgZGFyazpib3JkZXItZ3JheS03MDAgbXQtYXV0b2B9PlxyXG4gICAgICAgIDxidXR0b25cclxuICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUJhY2tUb01vZHVsZXN9XHJcbiAgICAgICAgICBjbGFzc05hbWU9e2BcclxuICAgICAgICAgICAgZ3JvdXAgJHtpc01pbmltaXplZCA/ICd3LTEyIGgtMTIgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1mdWxsIG14LWF1dG8nIDogJ3ctZnVsbCBweS0zIHB4LTQgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyd9XHJcbiAgICAgICAgICAgIGJvcmRlci0yIGJvcmRlci1tb2R1bGUtJHthY3RpdmVNb2R1bGV9LWJvcmRlciBkYXJrOmJvcmRlci1tb2R1bGUtJHthY3RpdmVNb2R1bGV9LWJvcmRlci1kYXJrXHJcbiAgICAgICAgICAgIGJnLXRyYW5zcGFyZW50IGRhcms6YmctZ3JheS04MDAgaG92ZXI6YmctbW9kdWxlLSR7YWN0aXZlTW9kdWxlfS1iZy8xMCBkYXJrOmhvdmVyOmJnLW1vZHVsZS0ke2FjdGl2ZU1vZHVsZX0tYmctZGFyay8xMFxyXG4gICAgICAgICAgICB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcclxuICAgICAgICAgIGB9XHJcbiAgICAgICAgICB0aXRsZT17aXNNaW5pbWl6ZWQgPyBcIlZvbHRhciBhIFRlbGEgSW5pY2lhbFwiIDogdW5kZWZpbmVkfVxyXG4gICAgICAgICAgYXJpYS1sYWJlbD1cIlZvbHRhciBwYXJhIG8gZGFzaGJvYXJkIHByaW5jaXBhbFwiXHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BcclxuICAgICAgICAgICAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdy04IGgtOCByb3VuZGVkLWZ1bGxcclxuICAgICAgICAgICAgYmctbW9kdWxlLSR7YWN0aXZlTW9kdWxlfS1iZyBkYXJrOmJnLW1vZHVsZS0ke2FjdGl2ZU1vZHVsZX0tYmctZGFyay83MFxyXG4gICAgICAgICAgICB0ZXh0LW1vZHVsZS0ke2FjdGl2ZU1vZHVsZX0taWNvbiBkYXJrOnRleHQtbW9kdWxlLSR7YWN0aXZlTW9kdWxlfS1pY29uLWRhcmtcclxuICAgICAgICAgICAgc2hhZG93LXNtIGRhcms6c2hhZG93LW1kIGRhcms6c2hhZG93LWJsYWNrLzIwXHJcbiAgICAgICAgICAgIGdyb3VwLWhvdmVyOnNjYWxlLTExMCB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0yMDBcclxuICAgICAgICAgIGB9PlxyXG4gICAgICAgICAgICA8Q2hldnJvbkxlZnQgc2l6ZT17MjB9IGFyaWEtaGlkZGVuPVwidHJ1ZVwiIC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIHshaXNNaW5pbWl6ZWQgJiYgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTgwMCBkYXJrOnRleHQtZ3JheS0yMDBcIj5Wb2x0YXIgYSBUZWxhIEluaWNpYWw8L3NwYW4+fVxyXG4gICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvYXNpZGU+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IENsaWVudFNpZGViYXI7XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNoZXZyb25MZWZ0IiwiVXNlcnMiLCJDYWxlbmRhciIsIlVzZXIiLCJDbG9jayIsIkZpbGVUZXh0IiwiQ3VzdG9tU2Nyb2xsQXJlYSIsImNsaWVudE1vZHVsZVN1Ym1lbnVzIiwicGVvcGxlIiwiaWQiLCJ0aXRsZSIsImljb24iLCJkZXNjcmlwdGlvbiIsInNjaGVkdWxlciIsInByb2ZpbGUiLCJNb2R1bGVUaXRsZSIsIm1vZHVsZUlkIiwiSWNvbiIsIm9uRGFzaGJvYXJkQ2xpY2siLCJkaXYiLCJjbGFzc05hbWUiLCJidXR0b24iLCJvbkNsaWNrIiwic3R5bGUiLCJiYWNrZ3JvdW5kIiwiYm9yZGVyIiwicGFkZGluZyIsImFyaWEtbGFiZWwiLCJpbWciLCJzcmMiLCJhbHQiLCJDbGllbnRTaWRlYmFyIiwiYWN0aXZlTW9kdWxlIiwiYWN0aXZlTW9kdWxlVGl0bGUiLCJpc1N1Ym1lbnVBY3RpdmUiLCJoYW5kbGVNb2R1bGVTdWJtZW51Q2xpY2siLCJoYW5kbGVCYWNrVG9Nb2R1bGVzIiwiaXNTaWRlYmFyT3BlbiIsImlzTWluaW1pemVkIiwidG9nZ2xlTWluaW1pemVkIiwiTW9kdWxlSWNvbiIsImFzaWRlIiwibW9kdWxlQ29sb3IiLCJuYXYiLCJhcmlhLWxhYmVsbGVkYnkiLCJtYXAiLCJzdWJtZW51IiwiaXNBY3RpdmUiLCJhcmlhLWN1cnJlbnQiLCJ1bmRlZmluZWQiLCJzaXplIiwiYXJpYS1oaWRkZW4iLCJzcGFuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar/ClientSidebar.js\n"));

/***/ })

});