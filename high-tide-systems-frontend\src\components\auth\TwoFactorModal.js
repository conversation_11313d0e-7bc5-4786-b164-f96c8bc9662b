import React, { useState, useEffect } from 'react';
import { api } from '@/utils/api';
import { useToast } from '@/contexts/ToastContext';
import { Loader2, Mail, RefreshCw, Shield, Clock, AlertCircle } from 'lucide-react';

const TwoFactorModal = ({ 
  isOpen, 
  userId, 
  expiresAt, 
  onSuccess, 
  onCancel,
  onResend
}) => {
  const [token, setToken] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const [error, setError] = useState('');
  const { toast_success, toast_error } = useToast();

  // Calculate time left
  useEffect(() => {
    if (!expiresAt) return;

    const updateTimeLeft = () => {
      const now = new Date().getTime();
      const expiry = new Date(expiresAt).getTime();
      const diff = Math.max(0, expiry - now);
      setTimeLeft(Math.floor(diff / 1000));
    };

    updateTimeLeft();
    const interval = setInterval(updateTimeLeft, 1000);

    return () => clearInterval(interval);
  }, [expiresAt]);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleTokenChange = (e) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setToken(value);
    setError('');
  };

  const handleVerifyToken = async (e) => {
    e.preventDefault();
    
    if (token.length !== 6) {
      setError('Token deve ter 6 dígitos');
      return;
    }

    if (timeLeft <= 0) {
      setError('Token expirou. Solicite um novo token.');
      return;
    }

    setIsVerifying(true);
    setError('');

    try {
      if (onSuccess) {
        await onSuccess(token); // Let the parent handle the API call
      }
    } catch (error) {
      console.error('2FA verification error:', error);
      const message = error.message || 'Erro ao verificar token';
      setError(message);
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResendToken = async () => {
    setIsResending(true);
    setError('');

    try {
      if (onResend) {
        const result = await onResend();
        toast_success(result.message || 'Novo token enviado para seu email');
      } else {
        // Fallback to direct API call
        const response = await api.post('/auth/resend-2fa', { userId });
        
        if (response.data.expiresAt) {
          toast_success('Novo token enviado para seu email');
          // Reset timer with new expiration
          const now = new Date().getTime();
          const expiry = new Date(response.data.expiresAt).getTime();
          const diff = Math.max(0, expiry - now);
          setTimeLeft(Math.floor(diff / 1000));
        }
      }
    } catch (error) {
      console.error('Resend 2FA error:', error);
      const message = error.response?.data?.message || error.message || 'Erro ao reenviar token';
      setError(message);
      toast_error(message);
    } finally {
      setIsResending(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center mb-6">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mr-4">
              <Shield className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Verificação de Dois Fatores
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Digite o código enviado para seu email
              </p>
            </div>
          </div>

          {/* Timer */}
          {timeLeft > 0 ? (
            <div className="flex items-center justify-center mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <Clock className="w-4 h-4 text-green-600 dark:text-green-400 mr-2" />
              <span className="text-sm text-green-700 dark:text-green-300">
                Token expira em: <strong>{formatTime(timeLeft)}</strong>
              </span>
            </div>
          ) : (
            <div className="flex items-center justify-center mb-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400 mr-2" />
              <span className="text-sm text-red-700 dark:text-red-300">
                Token expirado
              </span>
            </div>
          )}

          {/* Token Input Form */}
          <form onSubmit={handleVerifyToken}>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Código de Verificação
              </label>
              <input
                type="text"
                value={token}
                onChange={handleTokenChange}
                placeholder="123456"
                className="w-full px-4 py-3 text-center text-2xl font-mono tracking-widest border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                maxLength={6}
                autoComplete="off"
                disabled={isVerifying || timeLeft <= 0}
              />
              {error && (
                <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                  {error}
                </p>
              )}
            </div>

            {/* Actions */}
            <div className="flex flex-col space-y-3">
              <button
                type="submit"
                disabled={token.length !== 6 || isVerifying || timeLeft <= 0}
                className="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isVerifying ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    Verificando...
                  </>
                ) : (
                  <>
                    <Shield className="w-4 h-4 mr-2" />
                    Verificar Código
                  </>
                )}
              </button>

              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={handleResendToken}
                  disabled={isResending}
                  className="flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  {isResending ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                      Reenviando...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Reenviar
                    </>
                  )}
                </button>

                <button
                  type="button"
                  onClick={onCancel}
                  className="flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancelar
                </button>
              </div>
            </div>
          </form>

          {/* Help Text */}
          <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-start">
              <Mail className="w-5 h-5 text-gray-400 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Não recebeu o código? Verifique sua caixa de spam ou clique em "Reenviar" para solicitar um novo código.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TwoFactorModal;