// src/controllers/securitySettingsController.js
const prisma = require('../utils/prisma');
const bcrypt = require('bcryptjs');

// Gerar descrição legível das alterações
function generateChangeDescription(changes) {
  const fieldLabels = {
    requireUppercase: 'Exigir letra maiúscula',
    requireNumber: 'Exigir número',
    requireSpecialChar: 'Exigir caractere especial',
    minPasswordLength: '<PERSON>an<PERSON> mínimo da senha',
    passwordExpirationDays: 'Expira<PERSON> da senha (dias)',
    limitLoginAttempts: 'Limitar tentativas de login',
    maxLoginAttempts: 'Máximo de tentativas de login',
    accountLockoutDuration: 'Duração do bloqueio (minutos)',
    enforceIpTracking: 'Rastreamento de IP',
    enforceSessionTimeout: 'Timeout de sessão',
    sessionTimeoutMinutes: 'Timeout de sessão (minutos)',
    require2FA: 'Exigir autenticação de dois fatores',
    allow2FA: 'Permitir autenticação de dois fatores',
    twoFactorMethods: 'Métodos de 2FA permitidos'
  };

  const formatValue = (value) => {
    if (typeof value === 'boolean') {
      return value ? 'Ativado' : 'Desativado';
    }
    return value;
  };

  const changesText = changes.map(change => {
    const fieldLabel = fieldLabels[change.field] || change.field;
    const oldValue = formatValue(change.oldValue);
    const newValue = formatValue(change.newValue);
    return `${fieldLabel}: ${oldValue} → ${newValue}`;
  }).join('; ');
  
  return `[Configurações > Segurança] ${changesText}`;
}

class SecuritySettingsController {
  // Obter configurações de segurança da empresa
  async getSecuritySettings(req, res) {
    try {
      const { companyId: userCompanyId, role } = req.user;
      
      // Para SYSTEM_ADMIN, permitir seleção de empresa via header ou query
      let companyId = userCompanyId;
      if (role === 'SYSTEM_ADMIN') {
        // Aceitar company ID do header X-Company-Id ou query parameter
        companyId = req.headers['x-company-id'] || req.query.companyId || userCompanyId;
      }
      
      if (!companyId) {
        return res.status(400).json({ message: 'Empresa não identificada' });
      }

      let preferences = await prisma.companyPreference.findUnique({
        where: { companyId }
      });

      // Se não existir, criar configurações padrão
      if (!preferences) {
        preferences = await prisma.companyPreference.create({
          data: {
            companyId,
            preferences: {
              security: {
                requireUppercase: true,
                requireNumber: true,
                requireSpecialChar: false,
                minPasswordLength: 8,
                passwordExpirationDays: 0,
                limitLoginAttempts: true,
                maxLoginAttempts: 5,
                accountLockoutDuration: 30,
                enforceIpTracking: true,
                enforceSessionTimeout: true,
                sessionTimeoutMinutes: 30,
                // 2FA Settings
                require2FA: false,
                allow2FA: true,
                twoFactorMethods: ['email']
              }
            }
          }
        });
      }

      const securitySettings = preferences.preferences?.security;

      res.json({
        success: true,
        data: securitySettings
      });
    } catch (error) {
      console.error('Erro ao obter configurações de segurança:', error);
      res.status(500).json({ 
        success: false, 
        message: 'Erro interno do servidor' 
      });
    }
  }

  // Atualizar configurações de segurança
  async updateSecuritySettings(req, res) {
    try {
      const { companyId: userCompanyId, role } = req.user;
      const settings = req.body;

      // Verificar permissões
      if (role !== 'SYSTEM_ADMIN' && role !== 'COMPANY_ADMIN') {
        return res.status(403).json({ 
          success: false, 
          message: 'Acesso negado' 
        });
      }

      // Para SYSTEM_ADMIN, permitir seleção de empresa via header ou query
      let companyId = userCompanyId;
      if (role === 'SYSTEM_ADMIN') {
        companyId = req.headers['x-company-id'] || req.query.companyId || userCompanyId;
      }

      if (!companyId) {
        return res.status(400).json({ 
          success: false, 
          message: 'Empresa não identificada' 
        });
      }

      // Validações apenas se os campos estão presentes
      if (settings.minPasswordLength !== undefined && (settings.minPasswordLength < 6 || settings.minPasswordLength > 32)) {
        return res.status(400).json({
          success: false,
          message: 'Tamanho mínimo da senha deve estar entre 6 e 32 caracteres'
        });
      }

      if (settings.maxLoginAttempts !== undefined && (settings.maxLoginAttempts < 3 || settings.maxLoginAttempts > 10)) {
        return res.status(400).json({
          success: false,
          message: 'Máximo de tentativas deve estar entre 3 e 10'
        });
      }

      if (settings.sessionTimeoutMinutes !== undefined && (settings.sessionTimeoutMinutes < 5 || settings.sessionTimeoutMinutes > 240)) {
        return res.status(400).json({
          success: false,
          message: 'Tempo de sessão deve estar entre 5 e 240 minutos'
        });
      }

      // Obter configurações atuais para comparar
      const currentPreferences = await prisma.companyPreference.findUnique({
        where: { companyId }
      });
      
      const currentSecurity = currentPreferences?.preferences?.security || {};
      
      // Identificar campos realmente alterados e capturar valores
      const changes = [];
      const changedFields = [];
      
      Object.keys(settings).forEach(field => {
        const oldValue = currentSecurity[field];
        const newValue = settings[field];
        
        if (oldValue !== newValue) {
          changedFields.push(field);
          changes.push({
            field,
            oldValue,
            newValue
          });
        }
      });
      
      const mergedSecurity = { ...currentSecurity, ...settings };
      
      // Atualizar ou criar configurações
      if (currentPreferences) {
        await prisma.companyPreference.update({
          where: { companyId },
          data: {
            preferences: {
              ...currentPreferences.preferences,
              security: mergedSecurity
            }
          }
        });
      } else {
        await prisma.companyPreference.create({
          data: {
            companyId,
            preferences: {
              security: mergedSecurity
            }
          }
        });
      }

      // Log da alteração apenas se houver campos realmente alterados
      if (changes.length > 0) {
        await prisma.SecurityLog.create({
          data: {
            companyId,
            userId: req.user.id,
            action: 'SECURITY_SETTINGS_UPDATED',
            status: 'SUCCESS',
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            details: { 
              updatedFields: changedFields,
              changes,
              description: generateChangeDescription(changes),
              source: 'Configurações do Sistema > Segurança',
              location: '/dashboard/admin/settings'
            }
          }
        });
      }

      res.json({
        success: true,
        message: 'Configurações de segurança atualizadas com sucesso',
        data: mergedSecurity
      });
    } catch (error) {
      console.error('Erro ao atualizar configurações de segurança:', error);
      res.status(500).json({ 
        success: false, 
        message: 'Erro interno do servidor' 
      });
    }
  }

  // Obter logs de segurança
  async getSecurityLogs(req, res) {
    try {
      const { companyId: userCompanyId, role } = req.user;
      const { page = 1, limit = 20, action, status, userId } = req.query;

      // Verificar permissões
      if (role !== 'SYSTEM_ADMIN' && role !== 'COMPANY_ADMIN') {
        return res.status(403).json({ 
          success: false, 
          message: 'Acesso negado' 
        });
      }

      const skip = (parseInt(page) - 1) * parseInt(limit);
      
      const where = {};
      
      // Para SYSTEM_ADMIN, verificar se há empresa selecionada
      if (role === 'SYSTEM_ADMIN') {
        const selectedCompanyId = req.query.companyId;
        if (selectedCompanyId) {
          // Se empresa selecionada, mostrar apenas logs dessa empresa
          where.companyId = selectedCompanyId;
        }
        // Se não há empresa selecionada, mostrar todos os logs (não adiciona filtro companyId)
      } else {
        // Para outros roles, usar empresa do usuário
        where.companyId = userCompanyId;
      }

      if (action) where.action = action;
      if (status) where.status = status;
      if (userId) where.userId = userId;

      const [logs, total] = await Promise.all([
        prisma.SecurityLog.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: parseInt(limit)
        }),
        prisma.SecurityLog.count({ where })
      ]);

      res.json({
        success: true,
        data: {
          logs,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / parseInt(limit))
          }
        }
      });
    } catch (error) {
      console.error('Erro ao obter logs de segurança:', error);
      res.status(500).json({ 
        success: false, 
        message: 'Erro interno do servidor' 
      });
    }
  }

  // Validar senha com base nas políticas de segurança
  async validatePassword(password, companyId) {
    try {
      const preferences = await prisma.companyPreference.findUnique({
        where: { companyId }
      });

      const settings = preferences?.preferences?.security || {
        requireUppercase: true,
        requireNumber: true,
        requireSpecialChar: false,
        minPasswordLength: 8
      };

      return this.validatePasswordWithRules(password, settings);
    } catch (error) {
      console.error('Erro ao validar senha:', error);
      return { valid: false, message: 'Erro ao validar senha' };
    }
  }

  // Validar senha com regras específicas
  validatePasswordWithRules(password, rules) {
    const errors = [];

    if (password.length < rules.minPasswordLength) {
      errors.push(`A senha deve ter pelo menos ${rules.minPasswordLength} caracteres`);
    }

    if (rules.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('A senha deve conter pelo menos uma letra maiúscula');
    }

    if (rules.requireNumber && !/\d/.test(password)) {
      errors.push('A senha deve conter pelo menos um número');
    }

    if (rules.requireSpecialChar && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('A senha deve conter pelo menos um caractere especial');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  // Verificar se a conta está bloqueada
  async isAccountLocked(userId) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { accountLockedUntil: true }
      });

      if (!user || !user.accountLockedUntil) {
        return false;
      }

      return new Date() < user.accountLockedUntil;
    } catch (error) {
      console.error('Erro ao verificar bloqueio da conta:', error);
      return false;
    }
  }

  // Bloquear conta após tentativas falhadas
  async lockAccount(userId, companyId) {
    try {
      const preferences = await prisma.companyPreference.findUnique({
        where: { companyId }
      });

      const lockoutDuration = preferences?.preferences?.security?.accountLockoutDuration || 30;
      const lockoutUntil = new Date(Date.now() + lockoutDuration * 60 * 1000);

      await prisma.user.update({
        where: { id: userId },
        data: { 
          accountLockedUntil: lockoutUntil,
          failedLoginAttempts: 0
        }
      });

      await this.createSecurityLog({
        companyId,
        userId,
        action: 'ACCOUNT_LOCKED',
        status: 'WARNING',
        details: { lockoutUntil, reason: 'Exceeded maximum login attempts' }
      });

      return true;
    } catch (error) {
      console.error('Erro ao bloquear conta:', error);
      return false;
    }
  }

  // Criar log de segurança
  async createSecurityLog(logData) {
    try {
      return await prisma.SecurityLog.create({
        data: {
          companyId: logData.companyId,
          userId: logData.userId,
          action: logData.action,
          status: logData.status,
          ipAddress: logData.ipAddress,
          userAgent: logData.userAgent,
          details: logData.details
        }
      });
    } catch (error) {
      console.error('Erro ao criar log de segurança:', error);
      return null;
    }
  }

  // Verificar histórico de senhas
  async checkPasswordHistory(userId, newPassword) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { companyId: true }
      });

      if (!user?.companyId) return { canUse: true };

      const preferences = await prisma.companyPreference.findUnique({
        where: { companyId: user.companyId }
      });

      const preventPasswordReuse = preferences?.preferences?.security?.preventPasswordReuse;
      const passwordHistoryCount = preferences?.preferences?.security?.passwordHistoryCount || 5;

      if (!preventPasswordReuse) {
        return { canUse: true };
      }

      const passwordHistory = await prisma.PasswordHistory.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: passwordHistoryCount
      });

      for (const historyEntry of passwordHistory) {
        const isMatch = await bcrypt.compare(newPassword, historyEntry.passwordHash);
        if (isMatch) {
          return { 
            canUse: false, 
            message: `Não é possível reutilizar uma das últimas ${passwordHistoryCount} senhas` 
          };
        }
      }

      return { canUse: true };
    } catch (error) {
      console.error('Erro ao verificar histórico de senhas:', error);
      return { canUse: true };
    }
  }

  // Adicionar senha ao histórico
  async addPasswordToHistory(userId, passwordHash) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { companyId: true }
      });

      if (!user?.companyId) return;

      const preferences = await prisma.companyPreference.findUnique({
        where: { companyId: user.companyId }
      });

      const preventPasswordReuse = preferences?.preferences?.security?.preventPasswordReuse;
      const passwordHistoryCount = preferences?.preferences?.security?.passwordHistoryCount || 5;

      if (!preventPasswordReuse) return;

      await prisma.PasswordHistory.create({
        data: {
          userId,
          passwordHash
        }
      });

      const allHistory = await prisma.PasswordHistory.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' }
      });

      if (allHistory.length > passwordHistoryCount) {
        const toDelete = allHistory.slice(passwordHistoryCount);
        await prisma.PasswordHistory.deleteMany({
          where: {
            id: { in: toDelete.map(h => h.id) }
          }
        });
      }
    } catch (error) {
      console.error('Erro ao adicionar senha ao histórico:', error);
    }
  }

  /**
   * Check if 2FA is required for a company
   */
  async is2FARequired(companyId) {
    try {
      const preferences = await prisma.companyPreference.findUnique({
        where: { companyId }
      });

      const securitySettings = preferences?.preferences?.security;
      return securitySettings?.require2FA || false;
    } catch (error) {
      console.error('Error checking 2FA requirement:', error);
      return false;
    }
  }

  /**
   * Check if 2FA is allowed for a company
   */
  async is2FAAllowed(companyId) {
    try {
      const preferences = await prisma.companyPreference.findUnique({
        where: { companyId }
      });

      const securitySettings = preferences?.preferences?.security;
      return securitySettings?.allow2FA !== false; // Default to true if not set
    } catch (error) {
      console.error('Error checking 2FA allowance:', error);
      return true; // Default to allowing 2FA
    }
  }

  /**
   * Get allowed 2FA methods for a company
   */
  async getAllowed2FAMethods(companyId) {
    try {
      const preferences = await prisma.companyPreference.findUnique({
        where: { companyId }
      });

      const securitySettings = preferences?.preferences?.security;
      return securitySettings?.twoFactorMethods || ['email'];
    } catch (error) {
      console.error('Error getting allowed 2FA methods:', error);
      return ['email'];
    }
  }


}

module.exports = new SecuritySettingsController();