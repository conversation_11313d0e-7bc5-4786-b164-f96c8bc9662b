import { useState, useEffect } from "react";
import { Card } from "../../ui/Card";
import Button from "../../ui/Button";
import ModuleCheckbox from "../../ui/ModuleCheckbox";
import PreferencesSection from "./PreferencesSection";
import { preferencesService } from "@/services/preferencesService";
import { useToast } from "@/contexts/ToastContext";
import { Bell, UserPlus, Clock, Shield, Database, Download, Calendar, UserCheck, Users, FileText, ClipboardCheck } from "lucide-react";

// Definir tipos de notificação organizados por categoria
const NOTIFICATION_CATEGORIES = {
  appointments: {
    title: "Consultas",
    description: "Notificações relacionadas a agendamentos e consultas",
    moduleColor: "scheduler", // Roxo
    notifications: [
      {
        key: "APPOINTMENT_COMING",
        label: "Consultas Chegando",
        description: "Avisos de consultas agendadas (1 hora antes)",
        icon: <Clock size={16} />
      },
      {
        key: "NEW_APPOINTMENT_SCHEDULED",
        label: "Nova Consulta Agendada",
        description: "Notificações quando uma nova consulta é agendada no sistema",
        icon: <Calendar size={16} />
      },
      {
        key: "APPOINTMENT_SCHEDULED_FOR_PROVIDER",
        label: "Consulta Agendada (Profissional)",
        description: "Notificar profissionais quando consultas são agendadas para eles",
        icon: <UserCheck size={16} />
      },
      {
        key: "APPOINTMENT_SCHEDULED_FOR_PATIENT",
        label: "Consulta Agendada (Paciente)",
        description: "Notificar pacientes/clientes quando consultas são agendadas",
        icon: <Users size={16} />
      }
    ]
  },
  requests: {
    title: "Solicitações",
    description: "Notificações sobre solicitações de agendamento",
    moduleColor: "scheduler", // Roxo
    notifications: [
      {
        key: "NEW_APPOINTMENT_REQUEST",
        label: "Nova Solicitação de Agendamento",
        description: "Notificações quando clientes criam novas solicitações de agendamento",
        icon: <ClipboardCheck size={16} />
      },
      {
        key: "APPOINTMENT_REQUEST_APPROVED",
        label: "Solicitação Aprovada",
        description: "Notificar clientes quando suas solicitações são aprovadas",
        icon: <UserCheck size={16} />
      },
      {
        key: "APPOINTMENT_REQUEST_SUGGESTED",
        label: "Sugestão de Alteração",
        description: "Notificar clientes quando há sugestões de alteração em suas solicitações",
        icon: <Clock size={16} />
      },
      {
        key: "APPOINTMENT_REQUEST_REJECTED",
        label: "Solicitação Rejeitada",
        description: "Notificar clientes quando suas solicitações são rejeitadas",
        icon: <UserPlus size={16} />
      }
    ]
  },
  registrations: {
    title: "Novos Registros",
    description: "Notificações sobre novos cadastros e acessos",
    moduleColor: "admin", // Cinza
    notifications: [
      {
        key: "NEW_REGISTRATION",
        label: "Novos Cadastros",
        description: "Notificações sobre novos cadastros no sistema",
        icon: <UserPlus size={16} />
      },
      {
        key: "NEW_ACCESS",
        label: "Novos Acessos",
        description: "Notificações sobre novos acessos ao sistema",
        icon: <Shield size={16} />
      },
      {
        key: "NEW_BACKUP",
        label: "Novo Backup",
        description: "Confirmação de backups realizados",
        icon: <Database size={16} />
      }
    ]
  },
  documents: {
    title: "Documentos/Exportações",
    description: "Notificações sobre documentos e exportações",
    moduleColor: "people", // Laranja
    notifications: [
      {
        key: "DOCUMENT_SHARED",
        label: "Documentos Compartilhados",
        description: "Notificações quando documentos são compartilhados com usuários ou clientes",
        icon: <FileText size={16} />
      },
      {
        key: "NEW_EXPORT",
        label: "Exportações",
        description: "Notificações sobre exportações concluídas",
        icon: <Download size={16} />
      }
    ]
  }
};

// Função para obter todas as notificações em formato plano (para compatibilidade)
const getAllNotifications = () => {
  const allNotifications = [];
  Object.values(NOTIFICATION_CATEGORIES).forEach(category => {
    allNotifications.push(...category.notifications);
  });
  return allNotifications;
};

const NOTIFICATION_TYPES = getAllNotifications();

// Função para obter as cores do ícone baseado no módulo
const getIconColors = (moduleColor) => {
  const colorMap = {
    scheduler: {
      text: "text-purple-600 dark:text-purple-400"
    },
    people: {
      text: "text-orange-600 dark:text-orange-400"
    },
    admin: {
      text: "text-gray-600 dark:text-gray-400"
    },
    default: {
      text: "text-cyan-600 dark:text-cyan-400"
    }
  };

  return colorMap[moduleColor] || colorMap.default;
};

export default function NotificationPreferences({
  search = "",
  searchMode = false,
  preferences = null,
  selectedCompanyId = null,
  onSave = null
}) {
  // Estados para preferências de notificação
  const [enabledNotifications, setEnabledNotifications] = useState({});
  const [requiredNotifications, setRequiredNotifications] = useState({});
  const [isSaving, setIsSaving] = useState(false);
  
  const { toast_success, toast_error } = useToast();

  // Carregar preferências quando o componente receber os dados
  useEffect(() => {
    if (preferences && preferences.notifications) {
      setEnabledNotifications(preferences.notifications.enabled || {});
      setRequiredNotifications(preferences.notifications.required || {});
    } else {
      // Definir valores padrão se não houver preferências
      const defaultEnabled = {};
      const defaultRequired = {};
      NOTIFICATION_TYPES.forEach(type => {
        defaultEnabled[type.key] = false;
        defaultRequired[type.key] = false;
      });
      setEnabledNotifications(defaultEnabled);
      setRequiredNotifications(defaultRequired);
    }
  }, [preferences]);

  // Filtrar categorias baseado na pesquisa
  const filteredCategories = searchMode && search
    ? Object.entries(NOTIFICATION_CATEGORIES).reduce((acc, [key, category]) => {
        const filteredNotifications = category.notifications.filter(notification =>
          notification.label.toLowerCase().includes(search.toLowerCase()) ||
          notification.description.toLowerCase().includes(search.toLowerCase())
        );
        if (filteredNotifications.length > 0) {
          acc[key] = { ...category, notifications: filteredNotifications };
        }
        return acc;
      }, {})
    : NOTIFICATION_CATEGORIES;

  // Manter compatibilidade com filteredNotifications para o resto do código
  const filteredNotifications = searchMode && search
    ? NOTIFICATION_TYPES.filter(notification =>
        notification.label.toLowerCase().includes(search.toLowerCase()) ||
        notification.description.toLowerCase().includes(search.toLowerCase())
      )
    : NOTIFICATION_TYPES;

  // Se não há resultados na pesquisa, não mostrar nada
  if (searchMode && Object.keys(filteredCategories).length === 0) {
    return null;
  }


  // Função para toggle de notificação habilitada
  const toggleNotificationEnabled = (key) => {
    setEnabledNotifications(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
    
    // Se desabilitar, também desabilitar obrigatoriedade
    if (enabledNotifications[key]) {
      setRequiredNotifications(prev => ({
        ...prev,
        [key]: false
      }));
    }
  };

  // Função para toggle de obrigatoriedade
  const toggleNotificationRequired = (key) => {
    setRequiredNotifications(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Função para salvar preferências
  const handleSave = async () => {
    setIsSaving(true);
    try {
      const notificationPreferences = {
        enabled: enabledNotifications,
        required: requiredNotifications
      };

      // Manter as preferências existentes e adicionar as de notificação
      const currentPreferences = preferences || {};
      const updatedPreferences = {
        ...currentPreferences,
        notifications: notificationPreferences
      };

      if (onSave) {
        // Usar função de salvamento fornecida pelo pai (suporta empresas específicas)
        const success = await onSave(updatedPreferences);
        if (!success) {
          // Se o onSave retornar false, não continuar
          return;
        }
      } else {
        toast_error("Erro: função de salvamento não fornecida");
      }
    } catch (error) {
      console.error("Erro ao salvar preferências de notificação:", error);
      toast_error("Erro ao salvar preferências de notificação");
    } finally {
      setIsSaving(false);
    }
  };

  if (searchMode && filteredNotifications.length === 0) {
    return null;
  }

  const showNotifications = !searchMode || filteredNotifications.length > 0;

  return (
    <div className="space-y-8">
      {/* Configurações de Notificação */}
      {showNotifications && (
        <Card className="p-6 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <h4 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4">Preferências de Notificação</h4>
          <p className="text-sm text-neutral-600 dark:text-neutral-300 mb-6">Configure quais tipos de notificação estarão disponíveis no sistema. Notificações <span className="font-semibold text-blue-600 dark:text-blue-400">habilitadas</span> aparecerão para os usuários. Notificações <span className="font-semibold text-purple-600 dark:text-purple-400">obrigatórias</span> não podem ser desativadas pelos usuários.</p>

          <div className="space-y-8">
            {Object.entries(filteredCategories).map(([categoryKey, category]) => (
              <div key={categoryKey} className="space-y-4">
                {/* Cabeçalho da categoria */}
                <div className="flex items-center gap-3 pb-2 border-b border-gray-200 dark:border-gray-600">
                  <Bell className={`${getIconColors(category.moduleColor).text}`} size={20} />
                  <div>
                    <h5 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {category.title}
                    </h5>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {category.description}
                    </p>
                  </div>
                </div>

                {/* Cards das notificações da categoria */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {category.notifications.map((notification) => {
                    const iconColors = getIconColors(category.moduleColor);
                    return (
                      <div key={notification.key} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                        <div className="flex items-start gap-3 mb-4">
                          <div className={`${iconColors.text}`}>
                            {notification.icon}
                          </div>
                          <div className="flex-1">
                            <h6 className="font-medium text-gray-900 dark:text-white">
                              {notification.label}
                            </h6>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                              {notification.description}
                            </p>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <ModuleCheckbox
                            id={`enabled-${notification.key}`}
                            checked={enabledNotifications[notification.key] || false}
                            onChange={() => toggleNotificationEnabled(notification.key)}
                            disabled={isSaving}
                            label="Habilitada no sistema"
                            moduleColor="admin"
                          />

                          <ModuleCheckbox
                            id={`required-${notification.key}`}
                            checked={requiredNotifications[notification.key] || false}
                            onChange={() => toggleNotificationRequired(notification.key)}
                            disabled={!enabledNotifications[notification.key] || isSaving}
                            label="Obrigatória para todos os usuários"
                            moduleColor="admin"
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Informações importantes */}
      <Card className="p-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700">
        <div className="flex items-start gap-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
            <Bell className="text-blue-600 dark:text-blue-400" size={16} />
          </div>
          <div>
            <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
              Como funcionam as notificações
            </h4>
            <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>• <strong>Habilitadas:</strong> Aparecem nas configurações do usuário e podem ser ativadas/desativadas</li>
              <li>• <strong>Obrigatórias:</strong> Ficam sempre ativas e não podem ser desativadas pelo usuário</li>
              <li>• <strong>Desabilitadas:</strong> Não aparecem no sistema e não são enviadas</li>
              <li>• As configurações se aplicam a todos os usuários da empresa selecionada</li>
            </ul>
          </div>
        </div>
      </Card>

      {/* Botão de Salvar */}
      <div className="flex justify-end mt-8">
        <button
          className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
          onClick={handleSave}
          disabled={isSaving}
        >
          {isSaving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Salvando...
            </>
          ) : (
            <>
              <Bell size={16} />
              Salvar Preferências
            </>
          )}
        </button>
      </div>
    </div>
  );
}
