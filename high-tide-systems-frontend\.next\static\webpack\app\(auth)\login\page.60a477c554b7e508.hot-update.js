"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(auth)/login/page",{

/***/ "(app-pages-browser)/./src/components/auth/TwoFactorModal.js":
/*!***********************************************!*\
  !*** ./src/components/auth/TwoFactorModal.js ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Loader2,Mail,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Loader2,Mail,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Loader2,Mail,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Loader2,Mail,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Loader2,Mail,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Loader2,Mail,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst TwoFactorModal = (param)=>{\n    let { isOpen, userId, expiresAt, onSuccess, onCancel, onResend } = param;\n    _s();\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isVerifying, setIsVerifying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResending, setIsResending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    // Calculate time left\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TwoFactorModal.useEffect\": ()=>{\n            if (!expiresAt) return;\n            const updateTimeLeft = {\n                \"TwoFactorModal.useEffect.updateTimeLeft\": ()=>{\n                    const now = new Date().getTime();\n                    const expiry = new Date(expiresAt).getTime();\n                    const diff = Math.max(0, expiry - now);\n                    setTimeLeft(Math.floor(diff / 1000));\n                }\n            }[\"TwoFactorModal.useEffect.updateTimeLeft\"];\n            updateTimeLeft();\n            const interval = setInterval(updateTimeLeft, 1000);\n            return ({\n                \"TwoFactorModal.useEffect\": ()=>clearInterval(interval)\n            })[\"TwoFactorModal.useEffect\"];\n        }\n    }[\"TwoFactorModal.useEffect\"], [\n        expiresAt\n    ]);\n    const formatTime = (seconds)=>{\n        const minutes = Math.floor(seconds / 60);\n        const remainingSeconds = seconds % 60;\n        return \"\".concat(minutes, \":\").concat(remainingSeconds.toString().padStart(2, '0'));\n    };\n    const handleTokenChange = (e)=>{\n        const value = e.target.value.replace(/\\D/g, '').slice(0, 6);\n        setToken(value);\n        setError('');\n    };\n    const handleVerifyToken = async (e)=>{\n        e.preventDefault();\n        if (token.length !== 6) {\n            setError('Token deve ter 6 dígitos');\n            return;\n        }\n        if (timeLeft <= 0) {\n            setError('Token expirou. Solicite um novo token.');\n            return;\n        }\n        setIsVerifying(true);\n        setError('');\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.post('/auth/verify-2fa', {\n                userId,\n                token\n            });\n            if (response.data.user && response.data.token) {\n                toast_success('Login realizado com sucesso!');\n                onSuccess(response.data);\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('2FA verification error:', error);\n            const message = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao verificar token';\n            setError(message);\n            toast_error(message);\n        } finally{\n            setIsVerifying(false);\n        }\n    };\n    const handleResendToken = async ()=>{\n        setIsResending(true);\n        setError('');\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.post('/auth/resend-2fa', {\n                userId\n            });\n            if (response.data.expiresAt) {\n                toast_success('Novo token enviado para seu email');\n                // Reset timer with new expiration\n                const now = new Date().getTime();\n                const expiry = new Date(response.data.expiresAt).getTime();\n                const diff = Math.max(0, expiry - now);\n                setTimeLeft(Math.floor(diff / 1000));\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Resend 2FA error:', error);\n            const message = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao reenviar token';\n            setError(message);\n            toast_error(message);\n        } finally{\n            setIsResending(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mr-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: \"Verifica\\xe7\\xe3o de Dois Fatores\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"Digite o c\\xf3digo enviado para seu email\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, undefined),\n                    timeLeft > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 text-green-600 dark:text-green-400 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 135,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-green-700 dark:text-green-300\",\n                                children: [\n                                    \"Token expira em: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: formatTime(timeLeft)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 137,\n                                        columnNumber: 34\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                        lineNumber: 134,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mb-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4 text-red-600 dark:text-red-400 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 142,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-red-700 dark:text-red-300\",\n                                children: \"Token expirado\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 143,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                        lineNumber: 141,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleVerifyToken,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                        children: \"C\\xf3digo de Verifica\\xe7\\xe3o\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: token,\n                                        onChange: handleTokenChange,\n                                        placeholder: \"123456\",\n                                        className: \"w-full px-4 py-3 text-center text-2xl font-mono tracking-widest border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                        maxLength: 6,\n                                        autoComplete: \"off\",\n                                        disabled: isVerifying || timeLeft <= 0\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-red-600 dark:text-red-400\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: token.length !== 6 || isVerifying || timeLeft <= 0,\n                                        className: \"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isVerifying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4 animate-spin mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Verificando...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Verificar C\\xf3digo\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: handleResendToken,\n                                                disabled: isResending,\n                                                className: \"flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                children: isResending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-4 h-4 animate-spin mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Reenviando...\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Reenviar\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: onCancel,\n                                                className: \"flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                children: \"Cancelar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-400 mt-0.5 mr-3 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: 'N\\xe3o recebeu o c\\xf3digo? Verifique sua caixa de spam ou clique em \"Reenviar\" para solicitar um novo c\\xf3digo.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                            lineNumber: 225,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TwoFactorModal, \"tEzSLLVn27kUs/jKoxjzb++bu5c=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TwoFactorModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TwoFactorModal);\nvar _c;\n$RefreshReg$(_c, \"TwoFactorModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/TwoFactorModal.js\n"));

/***/ })

});