// src/controllers/emailConfigController.js
const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const prisma = require("../utils/prisma");
const nodemailer = require("nodemailer");

// Validation rules
const emailConfigValidation = [
  body("smtpHost").notEmpty().withMessage("Servidor SMTP é obrigatório"),
  body("smtpPort")
    .isInt({ min: 1, max: 65535 })
    .withMessage("Porta SMTP deve ser um número válido"),
  body("smtpUser").notEmpty().withMessage("Usuário SMTP é obrigatório"),
  body("smtpPassword")
    .if((value, { req }) => !req.params.id) // Required only for new configs
    .notEmpty()
    .withMessage("Senha SMTP é obrigatória para novas configurações"),
  body("emailFromName")
    .notEmpty()
    .withMessage("Nome do remetente é obrigatório"),
  body("emailFromAddress")
    .notEmpty()
    .withMessage("Email do remetente é obrigatório")
    .isEmail()
    .withMessage("Email do remetente deve ser um email válido"),
  body("companyId").notEmpty().withMessage("ID da empresa é obrigatório"),
];

class EmailConfigController {
  /**
   * Create a new email configuration
   */
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        smtpHost,
        smtpPort,
        smtpSecure,
        smtpUser,
        smtpPassword,
        emailFromName,
        emailFromAddress,
        active,
        companyId,
      } = req.body;

      // Check if company exists
      const company = await prisma.company.findUnique({
        where: { id: companyId },
      });

      if (!company) {
        return res.status(404).json({ message: "Empresa não encontrada" });
      }

      // Check user permissions
      if (
        req.user.role !== "SYSTEM_ADMIN" &&
        req.user.companyId !== companyId
      ) {
        return res
          .status(403)
          .json({
            message:
              "Você não tem permissão para configurar emails para esta empresa",
          });
      }

      // Create email config
      const emailConfig = await prisma.emailConfig.create({
        data: {
          smtpHost,
          smtpPort,
          smtpSecure: smtpSecure || false,
          smtpUser,
          smtpPassword, // In a real app, you'd want to encrypt this
          emailFromName,
          emailFromAddress,
          active: active !== undefined ? active : true,
          company: {
            connect: { id: companyId },
          },
        },
      });

      // Log the action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: "CREATE",
          entityType: "EmailConfig",
          entityId: emailConfig.id,
          details: {
            smtpHost,
            smtpPort,
            smtpUser,
            emailFromName,
            emailFromAddress,
          },
          companyId,
          ipAddress: req.ip,
          userAgent: req.headers["user-agent"],
        },
      });

      // Remove sensitive data from response
      const { smtpPassword: _, ...configWithoutPassword } = emailConfig;

      res.status(201).json(configWithoutPassword);
    } catch (error) {
      console.error("Error creating email config:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Get list of email configurations
   */
  static async list(req, res) {
    try {
      const { companyId } = req.query;

      if (!companyId) {
        return res.status(400).json({ message: "ID da empresa é obrigatório" });
      }

      // Ensure companyId is a string, not an array
      const finalCompanyId = Array.isArray(companyId) ? companyId[0] : companyId;

      // Check permissions
      if (
        req.user.role !== "SYSTEM_ADMIN" &&
        req.user.companyId !== finalCompanyId
      ) {
        return res
          .status(403)
          .json({
            message:
              "Você não tem permissão para visualizar configurações desta empresa",
          });
      }

      const emailConfigs = await prisma.emailConfig.findMany({
        where: { companyId: finalCompanyId },
        orderBy: { createdAt: "desc" },
        select: {
          id: true,
          smtpHost: true,
          smtpPort: true,
          smtpSecure: true,
          smtpUser: true,
          emailFromName: true,
          emailFromAddress: true,
          active: true,
          createdAt: true,
          updatedAt: true,
          companyId: true,
        },
      });

      res.json({ emailConfigs });
    } catch (error) {
      console.error("Error listing email configs:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Get a single email configuration
   */
  static async get(req, res) {
    try {
      const { id } = req.params;

      const emailConfig = await prisma.emailConfig.findUnique({
        where: { id },
        select: {
          id: true,
          smtpHost: true,
          smtpPort: true,
          smtpSecure: true,
          smtpUser: true,
          emailFromName: true,
          emailFromAddress: true,
          active: true,
          createdAt: true,
          updatedAt: true,
          companyId: true,
        },
      });

      if (!emailConfig) {
        return res
          .status(404)
          .json({ message: "Configuração de email não encontrada" });
      }

      // Check permissions
      if (
        req.user.role !== "SYSTEM_ADMIN" &&
        req.user.companyId !== emailConfig.companyId
      ) {
        return res
          .status(403)
          .json({
            message: "Você não tem permissão para visualizar esta configuração",
          });
      }

      res.json(emailConfig);
    } catch (error) {
      console.error("Error getting email config:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Update an email configuration
   */
  static async update(req, res) {
    try {
      const { id } = req.params;

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      // Find existing config
      const existingConfig = await prisma.emailConfig.findUnique({
        where: { id },
      });

      if (!existingConfig) {
        return res
          .status(404)
          .json({ message: "Configuração de email não encontrada" });
      }

      // Check permissions
      if (
        req.user.role !== "SYSTEM_ADMIN" &&
        req.user.companyId !== existingConfig.companyId
      ) {
        return res
          .status(403)
          .json({
            message: "Você não tem permissão para editar esta configuração",
          });
      }

      const {
        smtpHost,
        smtpPort,
        smtpSecure,
        smtpUser,
        smtpPassword,
        emailFromName,
        emailFromAddress,
        active,
        companyId,
      } = req.body;

      // Verify company matches or user is system admin
      if (companyId !== existingConfig.companyId) {
        if (req.user.role !== "SYSTEM_ADMIN") {
          return res
            .status(403)
            .json({
              message: "Você não pode alterar a empresa desta configuração",
            });
        }

        // Check if new company exists
        const company = await prisma.company.findUnique({
          where: { id: companyId },
        });

        if (!company) {
          return res.status(404).json({ message: "Empresa não encontrada" });
        }
      }

      // Prepare update data
      const updateData = {
        smtpHost,
        smtpPort,
        smtpSecure: smtpSecure || false,
        smtpUser,
        emailFromName,
        emailFromAddress,
        active: active !== undefined ? active : existingConfig.active,
        companyId,
      };

      // Only update password if provided
      if (smtpPassword) {
        updateData.smtpPassword = smtpPassword;
      }

      // Update the config
      const emailConfig = await prisma.emailConfig.update({
        where: { id },
        data: updateData,
      });

      // Log the action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: "UPDATE",
          entityType: "EmailConfig",
          entityId: emailConfig.id,
          details: {
            smtpHost,
            smtpPort,
            smtpUser,
            emailFromName,
            emailFromAddress,
            active,
          },
          companyId: emailConfig.companyId,
          ipAddress: req.ip,
          userAgent: req.headers["user-agent"],
        },
      });

      // Remove sensitive data from response
      const { smtpPassword: _, ...configWithoutPassword } = emailConfig;

      res.json(configWithoutPassword);
    } catch (error) {
      console.error("Error updating email config:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Delete an email configuration
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Find existing config
      const existingConfig = await prisma.emailConfig.findUnique({
        where: { id },
      });

      if (!existingConfig) {
        return res
          .status(404)
          .json({ message: "Configuração de email não encontrada" });
      }

      // Check permissions
      if (
        req.user.role !== "SYSTEM_ADMIN" &&
        req.user.companyId !== existingConfig.companyId
      ) {
        return res
          .status(403)
          .json({
            message: "Você não tem permissão para excluir esta configuração",
          });
      }

      // Delete the config
      await prisma.emailConfig.delete({
        where: { id },
      });

      // Log the action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: "DELETE",
          entityType: "EmailConfig",
          entityId: id,
          details: { emailConfig: existingConfig.smtpHost },
          companyId: existingConfig.companyId,
          ipAddress: req.ip,
          userAgent: req.headers["user-agent"],
        },
      });

      res.status(204).send();
    } catch (error) {
      console.error("Error deleting email config:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Toggle email configuration active status
   */
  static async toggleStatus(req, res) {
    try {
      const { id } = req.params;

      // Find existing config
      const existingConfig = await prisma.emailConfig.findUnique({
        where: { id },
      });

      if (!existingConfig) {
        return res
          .status(404)
          .json({ message: "Configuração de email não encontrada" });
      }

      // Check permissions
      if (
        req.user.role !== "SYSTEM_ADMIN" &&
        req.user.companyId !== existingConfig.companyId
      ) {
        return res
          .status(403)
          .json({
            message: "Você não tem permissão para alterar esta configuração",
          });
      }

      // Toggle active status
      const updatedConfig = await prisma.emailConfig.update({
        where: { id },
        data: {
          active: !existingConfig.active,
        },
      });

      // Log the action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: "TOGGLE_STATUS",
          entityType: "EmailConfig",
          entityId: id,
          details: {
            smtpHost: existingConfig.smtpHost,
            newStatus: updatedConfig.active,
          },
          companyId: existingConfig.companyId,
          ipAddress: req.ip,
          userAgent: req.headers["user-agent"],
        },
      });

      // Remove sensitive data from response
      const { smtpPassword: _, ...configWithoutPassword } = updatedConfig;

      res.json(configWithoutPassword);
    } catch (error) {
      console.error("Error toggling email config status:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Test email configuration without saving (for modal validation)
   */
  static async testConnection(req, res) {
    try {
      const {
        smtpHost,
        smtpPort,
        smtpSecure,
        smtpUser,
        smtpPassword,
        emailFromName,
        emailFromAddress,
        testEmail
      } = req.body;

      // Validate required fields
      if (!smtpHost || !smtpPort || !smtpUser || !smtpPassword || !emailFromName || !emailFromAddress) {
        return res.status(400).json({
          message: "Todos os campos são obrigatórios para o teste"
        });
      }

      if (!testEmail) {
        return res.status(400).json({
          message: "Email de teste é obrigatório"
        });
      }

      // Create temporary transporter
      const transporter = nodemailer.createTransport({
        host: smtpHost,
        port: parseInt(smtpPort),
        secure: smtpSecure,
        auth: {
          user: smtpUser,
          pass: smtpPassword,
        },
        tls: {
          rejectUnauthorized: process.env.NODE_ENV === 'production',
          minVersion: 'TLSv1.2',
          servername: smtpHost,
        },
        debug: false,
        logger: false,
      });

      // Test connection first
      try {
        await transporter.verify();
      } catch (verifyError) {
        console.error("SMTP verification failed:", verifyError);
        return res.status(400).json({
          success: false,
          message: "Falha na conexão SMTP",
          details: verifyError.message,
        });
      }

      // Send test email
      const mailOptions = {
        from: `"${emailFromName}" <${emailFromAddress}>`,
        to: testEmail,
        subject: "✅ Teste de Configuração SMTP - High Tide Systems",
        html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e5e7eb; border-radius: 12px; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);">
          <div style="text-align: center; margin-bottom: 30px;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
              <span style="color: white; font-size: 32px;">✅</span>
            </div>
            <h1 style="color: #1e293b; margin: 0; font-size: 24px;">Configuração SMTP Testada!</h1>
          </div>
          
          <div style="background: white; padding: 25px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h2 style="color: #059669; margin-top: 0; font-size: 18px; display: flex; align-items: center;">
              <span style="margin-right: 8px;">🎉</span> Teste Realizado com Sucesso!
            </h2>
            <p style="color: #374151; line-height: 1.6; margin-bottom: 15px;">
              Parabéns! Sua configuração de email está funcionando perfeitamente. Todos os parâmetros foram validados e o sistema conseguiu enviar este email de teste.
            </p>
            
            <div style="background-color: #f3f4f6; padding: 15px; border-radius: 6px; border-left: 4px solid #667eea;">
              <h3 style="margin-top: 0; color: #374151; font-size: 14px; font-weight: 600;">📋 Detalhes da Configuração Testada:</h3>
              <ul style="color: #6b7280; font-size: 14px; margin: 10px 0; padding-left: 20px;">
                <li><strong>Servidor:</strong> ${smtpHost}:${smtpPort}</li>
                <li><strong>Segurança:</strong> ${smtpSecure ? 'SSL/TLS' : 'STARTTLS'}</li>
                <li><strong>Usuário:</strong> ${smtpUser}</li>
                <li><strong>Remetente:</strong> ${emailFromName} &lt;${emailFromAddress}&gt;</li>
              </ul>
            </div>
          </div>
          
          <div style="text-align: center; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h3 style="color: #1e293b; margin-top: 0;">🚀 Próximos Passos</h3>
            <p style="color: #6b7280; font-size: 14px; margin-bottom: 0;">
              Agora você pode salvar esta configuração com confiança. O sistema usará estes parâmetros para enviar emails de notificação, confirmações de agendamento e outros comunicados importantes.
            </p>
          </div>
          
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center;">
            <p style="color: #9ca3af; font-size: 12px; margin: 0;">
              Este email foi enviado automaticamente pelo sistema de teste de configuração SMTP.<br>
              © ${new Date().getFullYear()} High Tide Systems - Todos os direitos reservados.
            </p>
          </div>
        </div>
        `,
      };

      const info = await transporter.sendMail(mailOptions);

      res.json({
        success: true,
        message: "Email de teste enviado com sucesso!",
        messageId: info.messageId,
      });
    } catch (error) {
      console.error("Error testing email configuration:", error);
      res.status(500).json({
        success: false,
        message: "Erro ao testar configuração de email",
        details: error.message,
      });
    }
  }

  /**
   * Test an email configuration by sending a test email
   */
  static async testEmail(req, res) {
    try {
      const { id } = req.params;
      const { testEmail } = req.body;

      if (!testEmail) {
        return res
          .status(400)
          .json({ message: "Email de teste é obrigatório" });
      }

      // Find existing config
      const emailConfig = await prisma.emailConfig.findUnique({
        where: { id },
      });

      if (!emailConfig) {
        return res
          .status(404)
          .json({ message: "Configuração de email não encontrada" });
      }

      // Check permissions
      if (
        req.user.role !== "SYSTEM_ADMIN" &&
        req.user.companyId !== emailConfig.companyId
      ) {
        return res
          .status(403)
          .json({
            message: "Você não tem permissão para testar esta configuração",
          });
      }

      // Create transporter
      const transporter = nodemailer.createTransport({
        host: emailConfig.smtpHost,
        port: emailConfig.smtpPort,
        secure: emailConfig.smtpSecure,
        auth: {
          user: emailConfig.smtpUser,
          pass: emailConfig.smtpPassword,
        },
        tls: {
          // Para desenvolvimento, aceitar certificados auto-assinados
          rejectUnauthorized: process.env.NODE_ENV === 'production',
          // Forçar TLS 1.2 ou superior
          minVersion: 'TLSv1.2',
          // Para Gmail especificamente
          servername: emailConfig.smtpHost,
        },
        // For debug purposes
        debug: true,
        logger: true,
      });

      // Test connection first
      try {
        await transporter.verify();
      } catch (verifyError) {
        console.error("Email verification failed:", verifyError);
        return res.status(400).json({
          message: "Falha na verificação do servidor SMTP",
          details: verifyError.message,
        });
      }

      // Send test email
      const mailOptions = {
        from: `"${emailConfig.emailFromName}" <${emailConfig.emailFromAddress}>`,
        to: testEmail,
        subject: "Teste de Configuração de Email - Dentro das Casinhas",
        text: `Esta é uma mensagem de teste para verificar a configuração de email.
        
Detalhes da configuração:
- Servidor: ${emailConfig.smtpHost}:${emailConfig.smtpPort}
- Usuário: ${emailConfig.smtpUser}
- De: ${emailConfig.emailFromName} <${emailConfig.emailFromAddress}>

Se você está vendo esta mensagem, a configuração de email está funcionando corretamente!

Atenciosamente,
Equipe Dentro das Casinhas`,
        html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px;">
          <h2 style="color: #FF9933; margin-bottom: 20px;">Teste de Configuração de Email</h2>
          
          <p>Esta é uma mensagem de teste para verificar a configuração de email.</p>
          
          <div style="background-color: #f9fafb; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <h4 style="margin-top: 0;">Detalhes da configuração:</h4>
            <ul style="padding-left: 20px;">
              <li><strong>Servidor:</strong> ${emailConfig.smtpHost}:${emailConfig.smtpPort}</li>
              <li><strong>Usuário:</strong> ${emailConfig.smtpUser}</li>
              <li><strong>De:</strong> ${emailConfig.emailFromName} &lt;${emailConfig.emailFromAddress}&gt;</li>
            </ul>
          </div>
          
          <p style="color: #059669; font-weight: bold;">Se você está vendo esta mensagem, a configuração de email está funcionando corretamente!</p>
          
          <p style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 14px;">
            Atenciosamente,<br>
            Equipe Dentro das Casinhas
          </p>
        </div>
        `,
      };

      const info = await transporter.sendMail(mailOptions);

      // Log the action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: "TEST_EMAIL",
          entityType: "EmailConfig",
          entityId: id,
          details: {
            testEmail,
            messageId: info.messageId,
          },
          companyId: emailConfig.companyId,
          ipAddress: req.ip,
          userAgent: req.headers["user-agent"],
        },
      });

      res.json({
        message: "Email de teste enviado com sucesso",
        messageId: info.messageId,
      });
    } catch (error) {
      console.error("Error sending test email:", error);
      res.status(500).json({
        message: "Erro ao enviar email de teste",
        details: error.message,
      });
    }
  }
}

module.exports = {
  EmailConfigController,
  emailConfigValidation,
};
