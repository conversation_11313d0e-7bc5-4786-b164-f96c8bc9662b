import React from 'react';
import { Loader2, User, Shield, Lock, UserCog } from 'lucide-react';

const UserFormFooter = ({ 
  activeTab, 
  isLoading, 
  user, 
  savedUserId, 
  selectedRole, 
  onBack, 
  onClose, 
  onSubmit 
}) => {
  const getButtonText = () => {
    if (!user && !savedUserId) {
      if (activeTab === "permissions" || 
          (activeTab === "role" && (selectedRole === "SYSTEM_ADMIN" || selectedRole === "COMPANY_ADMIN"))) {
        return "Criar Usuário";
      }
      return "Continuar";
    }
    
    // Para edição de usuário existente, sempre mostrar "Salvar"
    return "Salvar";
  };

  const getButtonIcon = () => {
    const icons = {
      info: User,
      modules: Shield,
      permissions: Lock,
      role: UserCog
    };
    
    const IconComponent = icons[activeTab];
    return IconComponent ? <IconComponent size={16} /> : null;
  };

  return (
    <div className="px-6 py-4 flex items-center">
      <div className="flex gap-2 flex-1">
        {activeTab !== "info" && (
          <button
            type="button"
            onClick={onBack}
            className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
            disabled={isLoading}
          >
            Voltar
          </button>
        )}
        <button
          type="button"
          onClick={onClose}
          className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
          disabled={isLoading}
        >
          Cancelar
        </button>
      </div>
      <div className="flex-shrink-0 ml-4">
        <button
          type="button"
          onClick={onSubmit}
          className="px-4 py-2 bg-slate-500 dark:bg-slate-600 text-white rounded-lg hover:bg-slate-600 dark:hover:bg-slate-700 transition-colors flex items-center gap-2"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 size={16} className="animate-spin" />
              <span>Salvando...</span>
            </>
          ) : (
            <>
              {getButtonIcon()}
              <span>{getButtonText()}</span>
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default UserFormFooter;