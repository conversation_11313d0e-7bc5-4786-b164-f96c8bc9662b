// src/routes/admin/settingsRoutes.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { cacheMiddleware, clearCacheMiddleware } = require('../../middlewares/cache');
const securitySettingsController = require('../../controllers/securitySettingsController');

// Configurar TTL para cache de configurações (1 hora)
const SETTINGS_CACHE_TTL = 3600;

// Todas as rotas requerem autenticação
router.use(authenticate);

// Rotas de configurações de segurança
router.get('/security', cacheMiddleware('settings:security', SETTINGS_CACHE_TTL), securitySettingsController.getSecuritySettings);
router.put('/security', clearCacheMiddleware('settings:security*'), securitySettingsController.updateSecuritySettings);
router.get('/security/logs', securitySettingsController.getSecurityLogs);

// Assumindo que temos um controlador de configurações
// Se não existir, você precisará criar um
const SettingsController = {
  getAll: (req, res) => {
    // Implementação para obter todas as configurações
    res.json({ message: 'Todas as configurações' });
  },
  getByKey: (req, res) => {
    // Implementação para obter uma configuração específica
    res.json({ message: `Configuração: ${req.params.key}` });
  },
  update: (req, res) => {
    // Implementação para atualizar uma configuração
    res.json({ message: `Configuração ${req.params.key} atualizada` });
  },
  getGeneralSettings: (req, res) => {
    // Implementação para obter configurações gerais
    res.json({ message: 'Configurações gerais' });
  },
  getBackupSettings: (req, res) => {
    // Implementação para obter configurações de backup
    res.json({ message: 'Configurações de backup' });
  }
};

// Aplicar cache para configurações
router.get('/', cacheMiddleware('settings', SETTINGS_CACHE_TTL), SettingsController.getAll);
router.get('/general', cacheMiddleware('settings:general', SETTINGS_CACHE_TTL), SettingsController.getGeneralSettings);
router.get('/backup', cacheMiddleware('settings:backup', SETTINGS_CACHE_TTL), SettingsController.getBackupSettings);
router.get('/:key', cacheMiddleware('settings:detail', SETTINGS_CACHE_TTL), SettingsController.getByKey);

// Limpar cache quando configurações são modificadas
router.put('/:key', clearCacheMiddleware('settings:*'), SettingsController.update);

module.exports = router;
