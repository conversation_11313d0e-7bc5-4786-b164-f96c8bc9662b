import React, { useState, useEffect } from 'react';
import { useToast } from '@/contexts/ToastContext';
import { api } from '@/utils/api';
import { Shield, AlertTriangle, Users, Lock, Loader2 } from 'lucide-react';
import { ModuleFormGroup, ModuleLabel } from '@/components/ui';

const SecurityPreferences = ({ companyId, preferences, onSave, isLoading: parentLoading }) => {
  const { toast_success, toast_error } = useToast();
  const [securityPrefs, setSecurityPrefs] = useState({
    twoFactorAuth: {
      required: false,
      allowed: true,
      allowedMethods: ['email']
    }
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Load security preferences
  useEffect(() => {
    if (preferences) {
      console.log('SecurityPreferences - preferences received:', preferences);
      const twoFactorAuth = preferences.security?.twoFactorAuth || {};
      console.log('SecurityPreferences - twoFactorAuth config:', twoFactorAuth);
      
      const newSecurityPrefs = {
        twoFactorAuth: {
          required: twoFactorAuth.required || false,
          allowed: twoFactorAuth.allowed !== false, // Default to true
          allowedMethods: twoFactorAuth.allowedMethods || ['email']
        }
      };
      
      console.log('SecurityPreferences - setting state to:', newSecurityPrefs);
      setSecurityPrefs(newSecurityPrefs);
    }
  }, [preferences]);

  const handleSave = async () => {
    if (!companyId) {
      toast_error('Selecione uma empresa para salvar as configurações');
      return;
    }

    try {
      setIsSaving(true);
      
      const updatedPreferences = {
        ...preferences,
        security: {
          ...preferences?.security,
          ...securityPrefs
        }
      };

      await onSave(updatedPreferences);
      toast_success('Configurações de segurança salvas com sucesso!');
    } catch (error) {
      console.error('Error saving security preferences:', error);
      toast_error('Erro ao salvar configurações de segurança');
    } finally {
      setIsSaving(false);
    }
  };

  const handleChange = (key, value) => {
    setSecurityPrefs(prev => ({
      ...prev,
      twoFactorAuth: {
        ...prev.twoFactorAuth,
        [key]: value
      }
    }));
  };

  const isDisabled = parentLoading || isLoading || !companyId;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Configurações de Segurança
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Configure políticas de segurança para todos os usuários da empresa
          </p>
        </div>
        <Shield className="h-6 w-6 text-blue-500" />
      </div>

      {/* Warning when no company selected */}
      {!companyId && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Empresa não selecionada
              </h4>
              <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                Selecione uma empresa para configurar as preferências de segurança.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 2FA Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center">
            <Lock className="h-5 w-5 text-blue-500 mr-3" />
            <div>
              <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                Autenticação de Dois Fatores (2FA)
              </h4>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Configure políticas de 2FA para todos os usuários da empresa
              </p>
            </div>
          </div>

          {/* Allow 2FA Setting */}
          <ModuleFormGroup moduleColor="admin">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="allow2FA"
                checked={securityPrefs.twoFactorAuth.allowed}
                onChange={(e) => handleChange('allowed', e.target.checked)}
                disabled={isDisabled}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="allow2FA" className="ml-2 block text-sm text-gray-900 dark:text-white">
                Permitir autenticação de dois fatores
              </label>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Permite que os usuários ativem 2FA voluntariamente em suas contas
            </p>
          </ModuleFormGroup>

          {/* Require 2FA Setting */}
          <ModuleFormGroup moduleColor="admin">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="require2FA"
                checked={securityPrefs.twoFactorAuth.required}
                onChange={(e) => handleChange('required', e.target.checked)}
                disabled={isDisabled || !securityPrefs.twoFactorAuth.allowed}
                className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
              />
              <label htmlFor="require2FA" className="ml-2 block text-sm text-gray-900 dark:text-white">
                <span className="font-medium text-red-600 dark:text-red-400">
                  Exigir autenticação de dois fatores
                </span>
              </label>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Torna obrigatório o uso de 2FA para todos os usuários da empresa
            </p>
          </ModuleFormGroup>

          {/* Warning when 2FA is required */}
          {securityPrefs.twoFactorAuth.required && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-red-800 dark:text-red-200">
                    Importante: 2FA Obrigatório
                  </h4>
                  <ul className="text-sm text-red-700 dark:text-red-300 mt-1 space-y-1">
                    <li>• Todos os usuários serão obrigados a configurar 2FA</li>
                    <li>• Usuários sem 2FA não conseguirão fazer login</li>
                    <li>• Esta configuração afetará imediatamente todos os usuários</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* 2FA Methods */}
          {securityPrefs.twoFactorAuth.allowed && (
            <ModuleFormGroup moduleColor="admin">
              <ModuleLabel moduleColor="admin">
                Métodos de 2FA permitidos
              </ModuleLabel>
              <div className="space-y-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="method-email"
                    checked={securityPrefs.twoFactorAuth.allowedMethods.includes('email')}
                    onChange={(e) => {
                      const methods = [...securityPrefs.twoFactorAuth.allowedMethods];
                      if (e.target.checked) {
                        if (!methods.includes('email')) methods.push('email');
                      } else {
                        const index = methods.indexOf('email');
                        if (index > -1) methods.splice(index, 1);
                      }
                      handleChange('allowedMethods', methods);
                    }}
                    disabled={isDisabled}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="method-email" className="ml-2 text-sm text-gray-900 dark:text-white">
                    Email
                  </label>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Códigos enviados por email (atualmente o único método disponível)
                </p>
              </div>
            </ModuleFormGroup>
          )}
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={handleSave}
          disabled={isDisabled || isSaving}
          className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSaving ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Salvando...
            </>
          ) : (
            <>
              <Shield className="h-4 w-4 mr-2" />
              Salvar Configurações
            </>
          )}
        </button>
      </div>

      {/* Info Box */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
        <div className="flex items-start">
          <Users className="h-5 w-5 text-blue-500 mr-3 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
              Como funciona:
            </h4>
            <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <li>• <strong>Permitir 2FA:</strong> Usuários podem ativar 2FA opcionalmente</li>
              <li>• <strong>Exigir 2FA:</strong> Todos os usuários devem usar 2FA obrigatoriamente</li>
              <li>• <strong>Aplicação:</strong> As configurações se aplicam a todos os usuários da empresa</li>
              <li>• <strong>Flexibilidade:</strong> Admins podem sempre alterar essas configurações</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecurityPreferences;