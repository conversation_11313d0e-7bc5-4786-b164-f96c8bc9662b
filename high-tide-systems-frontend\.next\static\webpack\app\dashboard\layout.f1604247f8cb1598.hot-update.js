"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar/index.js":
/*!***************************************************!*\
  !*** ./src/components/dashboard/Sidebar/index.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/dashboard/components */ \"(app-pages-browser)/./src/app/dashboard/components.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useSchedulingPreferences */ \"(app-pages-browser)/./src/hooks/useSchedulingPreferences.js\");\n/* harmony import */ var _components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/CustomScrollArea */ \"(app-pages-browser)/./src/components/ui/CustomScrollArea.js\");\n/* harmony import */ var _hooks_useConstructionMessage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useConstructionMessage */ \"(app-pages-browser)/./src/hooks/useConstructionMessage.js\");\n/* harmony import */ var _components_construction__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/construction */ \"(app-pages-browser)/./src/components/construction/index.js\");\n/* harmony import */ var _utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/constructionUtils */ \"(app-pages-browser)/./src/utils/constructionUtils.js\");\n/* harmony import */ var _components_dashboard_CompanySelector__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/CompanySelector */ \"(app-pages-browser)/./src/components/dashboard/CompanySelector.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ModuleTitle = (param)=>{\n    let { moduleId, title, icon: Icon } = param;\n    // Exibir o logo no lugar do título do módulo, como botão para /dashboard\n    const handleLogoClick = ()=>{\n        if (true) {\n            window.location.href = '/dashboard';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6 flex justify-center items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleLogoClick,\n            style: {\n                background: 'none',\n                border: 'none',\n                padding: 0\n            },\n            className: \"focus:outline-none cursor-pointer\",\n            \"aria-label\": \"Ir para o dashboard\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/logo_horizontal_sem_fundo.png\",\n                    alt: \"High Tide Logo\",\n                    className: \"h-12 dark:invert dark:text-white transition-all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ModuleTitle;\n// Mapeamento de submenus para permissões\nconst submenuPermissionsMap = {\n    // Admin\n    'admin.introduction': 'admin.dashboard.view',\n    'admin.dashboard': 'admin.dashboard.view',\n    'admin.users': 'admin.users.view',\n    'admin.professions': [\n        'admin.professions.view',\n        'admin.profession-groups.view'\n    ],\n    'admin.bug-reports': 'admin.dashboard.view',\n    'admin.plans': 'admin.dashboard.view',\n    'admin.logs': 'admin.logs.view',\n    'admin.settings': 'admin.config.edit',\n    'admin.backup': 'admin.config.edit',\n    'admin.affiliates': 'admin.dashboard.view',\n    // ABA+\n    'abaplus.anamnese': 'abaplus.anamnese.view',\n    'abaplus.evolucoes-diarias': 'abaplus.evolucoes-diarias.view',\n    'abaplus.sessao': 'abaplus.sessao.view',\n    // Financeiro\n    'financial.invoices': 'financial.invoices.view',\n    'financial.payments': 'financial.payments.view',\n    'financial.expenses': 'financial.expenses.view',\n    'financial.reports': 'financial.reports.view',\n    'financial.cashflow': 'financial.reports.view',\n    // RH\n    'hr.employees': 'rh.employees.view',\n    'hr.payroll': 'rh.payroll.view',\n    'hr.documents': 'rh.employees.view',\n    'hr.departments': 'rh.employees.view',\n    'hr.attendance': 'rh.attendance.view',\n    'hr.benefits': 'rh.benefits.view',\n    'hr.training': 'rh.employees.view',\n    // Pessoas\n    'people.clients': 'people.clients.view',\n    'people.persons': 'people.persons.view',\n    'people.documents': 'people.documents.view',\n    'people.insurances': 'people.insurances.view',\n    'people.insurance-limits': 'people.insurance-limits.view',\n    // Agendamento\n    'scheduler.calendar': 'scheduling.calendar.view',\n    'scheduler.working-hours': 'scheduling.working-hours.view',\n    'scheduler.service-types': 'scheduling.service-types.view',\n    'scheduler.locations': 'scheduling.locations.view',\n    'scheduler.occupancy': 'scheduling.occupancy.view',\n    'scheduler.appointment-requests': 'scheduling.appointment-requests.view',\n    'scheduler.appointments-report': 'scheduling.appointments-report.view',\n    'scheduler.appointments-dashboard': 'scheduling.appointments-dashboard.view'\n};\nconst Sidebar = (param)=>{\n    let { activeModule, activeModuleTitle, isSubmenuActive, handleModuleSubmenuClick, handleBackToModules, isSidebarOpen } = param;\n    var _moduleSubmenus_activeModule;\n    _s();\n    const { can, hasModule, isAdmin } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { schedulingPreferences, isLoading: preferencesLoading } = (0,_hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__.useSchedulingPreferences)();\n    // Inicializar o estado de grupos expandidos a partir do localStorage\n    const [expandedGroups, setExpandedGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Sidebar.useState\": ()=>{\n            // Verificar se estamos no cliente (browser) antes de acessar localStorage\n            if (true) {\n                const savedState = localStorage.getItem('sidebarExpandedGroups');\n                return savedState ? JSON.parse(savedState) : {};\n            }\n            return {};\n        }\n    }[\"Sidebar.useState\"]);\n    // Encontrar o objeto do módulo ativo para acessar seu ícone\n    const activeModuleObject = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.modules.find((m)=>m.id === activeModule);\n    const ModuleIcon = activeModuleObject === null || activeModuleObject === void 0 ? void 0 : activeModuleObject.icon;\n    // Função para verificar se o usuário tem permissão para ver um submenu\n    const hasPermissionForSubmenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenu]\": (moduleId, submenuId)=>{\n            var _moduleSubmenus_moduleId;\n            // Ignorar verificação para grupos, pois a permissão é verificada para cada item\n            if (submenuId === 'cadastro' || submenuId === 'configuracoes' || submenuId === 'gestao' || submenuId === 'convenios' || submenuId === 'financeiro' || submenuId === 'relatorios') {\n                return true;\n            }\n            // Buscar o submenu específico para verificar systemAdminOnly\n            const submenuItem = (_moduleSubmenus_moduleId = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId]) === null || _moduleSubmenus_moduleId === void 0 ? void 0 : _moduleSubmenus_moduleId.find({\n                \"Sidebar.useCallback[hasPermissionForSubmenu]\": (item)=>item.id === submenuId\n            }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"]);\n            // Verificar se o submenu requer SYSTEM_ADMIN\n            if (submenuItem === null || submenuItem === void 0 ? void 0 : submenuItem.systemAdminOnly) {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            // Verificação especial adicional para afiliados - apenas SYSTEM_ADMIN\n            if (submenuId === 'affiliates') {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            // Verificação especial adicional para bug-reports - apenas SYSTEM_ADMIN\n            if (submenuId === 'bug-reports') {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            const permissionKey = \"\".concat(moduleId, \".\").concat(submenuId);\n            const requiredPermission = submenuPermissionsMap[permissionKey];\n            // Se não há mapeamento de permissão, permitir acesso\n            if (!requiredPermission) return true;\n            // Administradores têm acesso a tudo (exceto itens systemAdminOnly)\n            if (isAdmin()) return true;\n            // Verificar permissão específica\n            if (Array.isArray(requiredPermission)) {\n                // Se for um array de permissões, verificar se o usuário tem pelo menos uma delas\n                return requiredPermission.some({\n                    \"Sidebar.useCallback[hasPermissionForSubmenu]\": (perm)=>can(perm)\n                }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"]);\n            } else {\n                // Se for uma única permissão, verificar normalmente\n                return can(requiredPermission);\n            }\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"], [\n        can,\n        isAdmin,\n        user.role\n    ]);\n    // Função para verificar se um submenu deve ser exibido baseado nas preferências\n    const shouldShowSubmenuByPreferences = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[shouldShowSubmenuByPreferences]\": (moduleId, submenuId)=>{\n            // Apenas verificar preferências para o módulo de agendamento\n            if (moduleId !== 'scheduler') return true;\n            // Se as preferências ainda estão carregando, não mostrar os itens que podem ser escondidos\n            if (preferencesLoading) {\n                return false;\n            }\n            // Verificar se as preferências estão carregadas\n            if (!schedulingPreferences) {\n                return false;\n            }\n            switch(submenuId){\n                case 'locations':\n                    const showLocations = schedulingPreferences.showLocations !== false;\n                    return showLocations;\n                case 'service-types':\n                    const showServiceTypes = schedulingPreferences.showServiceTypes !== false;\n                    return showServiceTypes;\n                case 'insurances':\n                    const showInsurance = schedulingPreferences.showInsurance !== false;\n                    return showInsurance;\n                case 'working-hours':\n                    const showWorkingHours = schedulingPreferences.showWorkingHours !== false;\n                    return showWorkingHours;\n                default:\n                    return true;\n            }\n        }\n    }[\"Sidebar.useCallback[shouldShowSubmenuByPreferences]\"], [\n        schedulingPreferences,\n        preferencesLoading\n    ]);\n    // Função para alternar a expansão de um grupo\n    const toggleGroup = (groupId)=>{\n        setExpandedGroups((prev)=>({\n                ...prev,\n                [groupId]: !prev[groupId]\n            }));\n    };\n    // Verificar se algum item dentro de um grupo está ativo\n    const isGroupActive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[isGroupActive]\": (moduleId, groupItems)=>{\n            return groupItems.some({\n                \"Sidebar.useCallback[isGroupActive]\": (item)=>isSubmenuActive(moduleId, item.id)\n            }[\"Sidebar.useCallback[isGroupActive]\"]);\n        }\n    }[\"Sidebar.useCallback[isGroupActive]\"], [\n        isSubmenuActive\n    ]);\n    // Expandir automaticamente grupos que contêm o item ativo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            if (activeModule && _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) {\n                const newExpandedGroups = {\n                    ...expandedGroups\n                };\n                _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule].forEach({\n                    \"Sidebar.useEffect\": (submenu)=>{\n                        if (submenu.type === 'group' && isGroupActive(activeModule, submenu.items)) {\n                            newExpandedGroups[submenu.id] = true;\n                        }\n                    }\n                }[\"Sidebar.useEffect\"]);\n                setExpandedGroups(newExpandedGroups);\n            }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"Sidebar.useEffect\"], [\n        activeModule,\n        pathname,\n        isGroupActive\n    ]);\n    // Verificar se um item de submenu tem permissão para ser exibido\n    const hasPermissionForSubmenuItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (moduleId, submenuId)=>{\n            var _moduleSubmenus_moduleId;\n            // Buscar o item dentro dos grupos do módulo\n            let submenuItem = null;\n            // Primeiro buscar nos itens diretos\n            submenuItem = (_moduleSubmenus_moduleId = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId]) === null || _moduleSubmenus_moduleId === void 0 ? void 0 : _moduleSubmenus_moduleId.find({\n                \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (item)=>item.id === submenuId\n            }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"]);\n            // Se não encontrou, buscar dentro dos grupos\n            if (!submenuItem) {\n                for (const item of _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId] || []){\n                    if (item.type === 'group' && item.items) {\n                        submenuItem = item.items.find({\n                            \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (subItem)=>subItem.id === submenuId\n                        }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"]);\n                        if (submenuItem) break;\n                    }\n                }\n            }\n            // Verificar se o item requer SYSTEM_ADMIN\n            if (submenuItem === null || submenuItem === void 0 ? void 0 : submenuItem.systemAdminOnly) {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            return hasPermissionForSubmenu(moduleId, submenuId);\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"], [\n        hasPermissionForSubmenu,\n        user === null || user === void 0 ? void 0 : user.role\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-72 bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col \".concat(isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'),\n        \"aria-label\": \"Navega\\xe7\\xe3o lateral\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"flex-1 p-5\",\n                moduleColor: activeModule,\n                children: [\n                    activeModuleObject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModuleTitle, {\n                        moduleId: activeModule,\n                        title: activeModuleTitle,\n                        icon: ModuleIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_CompanySelector__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        activeModule: activeModule\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, undefined),\n                    preferencesLoading && activeModule === 'scheduler' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-gray-500 dark:text-gray-400\",\n                                children: \"Carregando...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2 flex flex-col justify-center items-center mt-8\",\n                        \"aria-labelledby\": \"sidebar-heading\",\n                        children: !(preferencesLoading && activeModule === 'scheduler') && activeModule && ((_moduleSubmenus_activeModule = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) === null || _moduleSubmenus_activeModule === void 0 ? void 0 : _moduleSubmenus_activeModule.map((submenu)=>{\n                            // Verificar se é um grupo ou um item individual\n                            if (submenu.type === 'group') {\n                                // Verificar se algum item do grupo tem permissão para ser exibido\n                                const hasAnyPermission = submenu.items.some((item)=>hasPermissionForSubmenuItem(activeModule, item.id));\n                                if (!hasAnyPermission) {\n                                    return null; // Não renderizar o grupo se nenhum item tiver permissão\n                                }\n                                const isGroupExpanded = expandedGroups[submenu.id] || false;\n                                const isAnyItemActive = isGroupActive(activeModule, submenu.items);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleGroup(submenu.id),\n                                            className: \"\\n                      group w-full flex items-center justify-between px-4 py-2.5 rounded-lg transition-all duration-300\\n                      \".concat(isAnyItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark font-medium\") : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700', \"\\n                    \"),\n                                            \"aria-expanded\": isGroupExpanded,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-left\",\n                                                    children: submenu.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-500 dark:text-gray-400\",\n                                                    children: isGroupExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 25\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isGroupExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4 pl-2 border-l border-gray-200 dark:border-gray-700 space-y-1\",\n                                            children: submenu.items.map((item)=>{\n                                                const isItemActive = isSubmenuActive(activeModule, item.id);\n                                                // Verificar permissão antes de renderizar o item\n                                                if (!hasPermissionForSubmenuItem(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não tiver permissão\n                                                }\n                                                // Verificar se o item deve ser exibido baseado nas preferências\n                                                if (!shouldShowSubmenuByPreferences(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não estiver habilitado nas preferências\n                                                }\n                                                // Verificar se o item está em construção\n                                                const itemKey = \"\".concat(activeModule, \".\").concat(item.id);\n                                                const isItemUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.isUnderConstruction)(activeModule, item.id);\n                                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.getConstructionMessage)(activeModule, item.id);\n                                                // Estilo comum para os itens\n                                                const itemClassName = \"\\n                          group w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-300\\n                          \".concat(isItemActive ? \"rounded-xl border border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                               bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                               shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n                        \");\n                                                // Se estiver em construção, usar o ConstructionButton\n                                                if (isItemUnderConstruction) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_9__.ConstructionButton, {\n                                                        className: itemClassName,\n                                                        \"aria-current\": isItemActive ? 'page' : undefined,\n                                                        title: constructionMessage.title,\n                                                        content: constructionMessage.content,\n                                                        icon: constructionMessage.icon,\n                                                        position: \"right\",\n                                                        children: [\n                                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\\n                                  \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                                \"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                    size: 18,\n                                                                    \"aria-hidden\": \"true\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 35\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 33\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                                children: item.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 31\n                                                            }, undefined)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 29\n                                                    }, undefined);\n                                                }\n                                                // Se não estiver em construção, usar o botão normal\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleModuleSubmenuClick(activeModule, item.id),\n                                                    className: itemClassName,\n                                                    \"aria-current\": isItemActive ? 'page' : undefined,\n                                                    children: [\n                                                        item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"\\n                                \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                              \"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                size: 18,\n                                                                \"aria-hidden\": \"true\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 31\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 27\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 334,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 309,\n                                    columnNumber: 17\n                                }, undefined);\n                            } else {\n                                // Renderização de itens individuais (não agrupados)\n                                const isActive = isSubmenuActive(activeModule, submenu.id);\n                                // Verificar permissão antes de renderizar o item\n                                const hasPermission = hasPermissionForSubmenu(activeModule, submenu.id);\n                                if (!hasPermission) {\n                                    return null; // Não renderizar se não tiver permissão\n                                }\n                                // Verificar se o submenu deve ser exibido baseado nas preferências\n                                const shouldShowByPreferences = shouldShowSubmenuByPreferences(activeModule, submenu.id);\n                                if (!shouldShowByPreferences) {\n                                    return null; // Não renderizar se não estiver habilitado nas preferências\n                                }\n                                // Verificar se o submenu está em construção\n                                const submenuKey = \"\".concat(activeModule, \".\").concat(submenu.id);\n                                const isSubmenuUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.isUnderConstruction)(activeModule, submenu.id);\n                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.getConstructionMessage)(activeModule, submenu.id);\n                                // Estilo comum para ambos os tipos de botões\n                                const buttonClassName = \"\\n                group w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-300\\n                \".concat(isActive ? \"rounded-xl border border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                     bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                     shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n              \");\n                                // Se estiver em construção, usar o ConstructionButton\n                                if (isSubmenuUnderConstruction) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_9__.ConstructionButton, {\n                                        className: buttonClassName,\n                                        \"aria-current\": isActive ? 'page' : undefined,\n                                        title: constructionMessage.title,\n                                        content: constructionMessage.content,\n                                        icon: constructionMessage.icon,\n                                        position: \"right\",\n                                        children: [\n                                            submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                    size: 20,\n                                                    \"aria-hidden\": \"true\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 470,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                                children: submenu.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 482,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, submenu.id, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                        lineNumber: 460,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }\n                                // Se não estiver em construção, usar o botão normal\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleModuleSubmenuClick(activeModule, submenu.id),\n                                    className: buttonClassName,\n                                    \"aria-current\": isActive ? 'page' : undefined,\n                                    children: [\n                                        submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                size: 20,\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 502,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 496,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                            children: submenu.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 508,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 489,\n                                    columnNumber: 17\n                                }, undefined);\n                            }\n                        }))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-5 border-t border-gray-100 dark:border-gray-700 mt-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToModules,\n                    className: \"\\n            group w-full py-3 px-4 rounded-lg flex items-center gap-3\\n            border-2 border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n            bg-transparent dark:bg-gray-800 hover:bg-module-\").concat(activeModule, \"-bg/10 dark:hover:bg-module-\").concat(activeModule, \"-bg-dark/10\\n            transition-all duration-300\\n          \"),\n                    \"aria-label\": \"Voltar para o dashboard principal\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            flex items-center justify-center w-8 h-8 rounded-full\\n            bg-module-\".concat(activeModule, \"-bg dark:bg-module-\").concat(activeModule, \"-bg-dark/70\\n            text-module-\").concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\\n            shadow-sm dark:shadow-md dark:shadow-black/20\\n            group-hover:scale-110 transition-transform duration-200\\n          \"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 535,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 528,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-gray-800 dark:text-gray-200\",\n                            children: \"Voltar a Tela Inicial\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 537,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 518,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 517,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 260,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Sidebar, \"n2nPwalu72ypjb7vu/RAHHj5oME=\", false, function() {\n    return [\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__.useSchedulingPreferences\n    ];\n});\n_c1 = Sidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\nvar _c, _c1;\n$RefreshReg$(_c, \"ModuleTitle\");\n$RefreshReg$(_c1, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar/index.js\n"));

/***/ })

});