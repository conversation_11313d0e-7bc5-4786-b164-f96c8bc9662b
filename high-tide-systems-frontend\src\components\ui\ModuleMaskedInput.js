"use client";

import React, { useEffect, useState } from "react";
import { InputMask, format } from "@react-input/mask";
import { cn } from "@/lib/utils";

/**
 * Componente de input com máscara que segue o design system dos módulos
 * @param {string} props.moduleColor - Cor do módulo (default, people, scheduler, admin, financial)
 * @param {string} props.mask - Máscara a ser aplicada (ex: "999.999.999-99")
 * @param {object} props.replacement - Objeto de substituição para a máscara (ex: { 9: /[0-9]/ })
 * @param {boolean} props.error - Se o input está em estado de erro
 * @param {string} props.className - Classes adicionais
 */
const ModuleMaskedInput = ({
  moduleColor = "default",
  mask,
  replacement,
  error,
  className,
  value,
  onChange,
  ...props
}) => {
  // Estado interno para armazenar o valor formatado
  const [formattedValue, setFormattedValue] = useState("");

  // Efeito para formatar o valor inicial
  useEffect(() => {
    const safeValue = value || '';
    try {
      // Verificar se replacement é válido
      if (replacement && typeof replacement === 'object') {
        const formatted = format(safeValue, mask, replacement);
        setFormattedValue(formatted);
      } else {
        setFormattedValue(safeValue);
      }
    } catch (error) {
      // Se falhar, usar o valor original
      console.warn("Erro ao formatar valor inicial:", error);
      setFormattedValue(safeValue);
    }
  }, [value, mask, replacement]);

  // Função para lidar com mudanças no input
  const handleChange = (e) => {
    setFormattedValue(e.target.value);
    if (onChange) {
      onChange(e);
    }
  };

  // Mapeamento de cores por módulo
  const moduleColors = {
    default: {
      focusRing: 'focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-primary-500 focus-visible:!border-primary-500 dark:focus-visible:!ring-primary-400 dark:focus-visible:!border-primary-400',
      errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
    },
    people: {
      focusRing: 'focus:outline-none focus:ring-1 focus:ring-module-people-border focus:border-module-people-border dark:focus:ring-module-people-border-dark dark:focus:border-module-people-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-people-border focus-visible:!border-module-people-border dark:focus-visible:!ring-module-people-border-dark dark:focus-visible:!border-module-people-border-dark',
      errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
    },
    scheduler: {
      focusRing: 'focus:outline-none focus:ring-1 focus:ring-module-scheduler-border focus:border-module-scheduler-border dark:focus:ring-module-scheduler-border-dark dark:focus:border-module-scheduler-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-scheduler-border focus-visible:!border-module-scheduler-border dark:focus-visible:!ring-module-scheduler-border-dark dark:focus-visible:!border-module-scheduler-border-dark',
      errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
    },
    admin: {
      focusRing: 'focus:outline-none focus:ring-1 focus:ring-module-admin-border focus:border-module-admin-border dark:focus:ring-module-admin-border-dark dark:focus:border-module-admin-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-admin-border focus-visible:!border-module-admin-border dark:focus-visible:!ring-module-admin-border-dark dark:focus-visible:!border-module-admin-border-dark',
      errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
    },
    financial: {
      focusRing: 'focus:outline-none focus:ring-1 focus:ring-module-financial-border focus:border-module-financial-border dark:focus:ring-module-financial-border-dark dark:focus:border-module-financial-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-financial-border focus-visible:!border-module-financial-border dark:focus-visible:!ring-module-financial-border-dark dark:focus-visible:!border-module-financial-border-dark',
      errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
    },
  };

  // Classes base para todos os inputs
  const baseClasses = 'w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 text-sm transition-colors duration-200 bg-white dark:bg-gray-700 dark:text-gray-200 outline-none';

  // Selecionar as classes de cor com base no módulo
  const colorClasses = moduleColors[moduleColor] || moduleColors.default;

  // Construir as classes finais
  const finalClasses = cn(
    baseClasses,
    error ? colorClasses.errorBorder : '',
    error ? colorClasses.errorRing : colorClasses.focusRing,
    className
  );

  return (
    <InputMask
      mask={mask}
      replacement={replacement}
      className={finalClasses}
      value={formattedValue}
      onChange={handleChange}
      {...props}
    />
  );
};

export default ModuleMaskedInput;
