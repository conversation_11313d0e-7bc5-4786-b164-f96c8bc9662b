import React, { useState } from 'react';
import { User, Mail, Lock, Calendar, Phone, MapPin, CreditCard, Building, Briefcase, Info, Eye, EyeOff } from 'lucide-react';
import UserProfileImageUpload from '../forms/UserProfileImageUpload';
import AddressForm from '../common/AddressForm';
import MaskedInput from '../common/MaskedInput';
import { ModuleFormGroup, ModuleSelect, ModuleRadioGroup } from '@/components/ui';

const UserInfoTab = ({
  formData,
  errors,
  isLoading,
  user,
  savedUserId,
  companies,
  loadingCompanies,
  branches,
  loadingBranches,
  professions,
  loadingProfessions,
  selectedProfession,
  professionDefaultModules,
  professionDefaultPermissions,
  isUsingProfessionDefaults,
  isSystemAdmin,
  profileImageUploadRef,
  onChange,
  onDocumentTypeChange,
  onImageUploaded,
  onProfessionDefaultsToggle
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const inputClasses = "block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-slate-500 dark:focus:ring-slate-400 focus:border-slate-500 dark:focus:border-slate-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100";
  const passwordInputClasses = "block w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-slate-500 dark:focus:ring-slate-400 focus:border-slate-500 dark:focus:border-slate-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100";
  const iconContainerClasses = "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none";
  const labelClasses = "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1";
  const errorClasses = "mt-1 text-xs text-red-600 dark:text-red-400";


  return (
    <form>
      {/* Imagem de perfil */}
      <div className="flex flex-col items-center mb-6">
        <UserProfileImageUpload
          userId={user?.id || savedUserId}
          initialImageUrl={formData.profileImageUrl}
          onImageUploaded={onImageUploaded}
          deferUpload={!user?.id && !savedUserId}
          uploadRef={profileImageUploadRef}
          size="large"
          disabled={isLoading}
        />
        {errors.profilePhoto && (
          <p className="mt-2 text-xs text-red-600 dark:text-red-400 text-center">
            {errors.profilePhoto}
          </p>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Coluna Esquerda - Informações Básicas */}
        <div className="space-y-6">
          <div className="flex items-center gap-2 mb-4 pb-2 border-b border-gray-300 dark:border-gray-600">
            <User className="h-4 w-4 text-slate-600" />
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Informações Básicas</h3>
          </div>

          {/* Login */}
          <div className="space-y-2">
            <label className={labelClasses} htmlFor="login">
              <span className="w-2 h-2 bg-slate-500 rounded-full inline-block mr-2"></span>
              Login *
            </label>
            <div className="relative">
              <div className={iconContainerClasses}>
                <User className="h-5 w-5 text-gray-400 dark:text-gray-500" />
              </div>
              <input
                id="login"
                name="login"
                type="text"
                value={formData.login}
                onChange={onChange}
                className={`${inputClasses} ${errors.login ? "border-red-500 dark:border-red-700" : ""}`}
                placeholder="nome.sobrenome"
                disabled={!!user || isLoading}
              />
            </div>
            {errors.login && <p className={errorClasses}>{errors.login}</p>}
          </div>

          {/* Nome completo */}
          <div className="space-y-2">
            <label className={labelClasses} htmlFor="fullName">
              <span className="w-2 h-2 bg-slate-500 rounded-full inline-block mr-2"></span>
              Nome completo *
            </label>
            <div className="relative">
              <div className={iconContainerClasses}>
                <User className="h-5 w-5 text-gray-400 dark:text-gray-500" />
              </div>
              <input
                id="fullName"
                name="fullName"
                type="text"
                value={formData.fullName}
                onChange={onChange}
                className={`${inputClasses} ${errors.fullName ? "border-red-500 dark:border-red-700" : ""}`}
                placeholder="Nome completo"
                disabled={isLoading}
              />
            </div>
            {errors.fullName && <p className={errorClasses}>{errors.fullName}</p>}
          </div>

          {/* Email */}
          <div className="space-y-2">
            <label className={labelClasses} htmlFor="email">
              <span className="w-2 h-2 bg-slate-500 rounded-full inline-block mr-2"></span>
              Email *
            </label>
            <div className="relative">
              <div className={iconContainerClasses}>
                <Mail className="h-5 w-5 text-gray-400 dark:text-gray-500" />
              </div>
              <input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={onChange}
                className={`${inputClasses} ${errors.email ? "border-red-500 dark:border-red-700" : ""}`}
                placeholder="<EMAIL>"
                disabled={isLoading}
              />
            </div>
            {errors.email && <p className={errorClasses}>{errors.email}</p>}
          </div>

          {/* Profissão */}
          <div className="space-y-2">
            <ModuleFormGroup
              moduleColor="admin"
              label={
                <span className="text-sm font-medium flex items-center gap-2">
                  <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                  Profissão
                </span>
              }
              htmlFor="professionId"
              icon={<Briefcase size={16} />}
              error={!!errors.professions}
              errorMessage={errors.professions}
            >
              <ModuleSelect
                moduleColor="admin"
                id="professionId"
                name="professionId"
                value={formData.professionId ?? ''}
                onChange={onChange}
                disabled={isLoading || loadingProfessions}
                placeholder="Selecione uma profissão"
                error={!!errors.professionId}
              >
                <option value="">Selecione uma profissão</option>
                {professions.map((profession) => (
                  <option key={profession.id} value={profession.id}>
                    {profession.name} {profession.group ? `(${profession.group.name})` : ''}
                  </option>
                ))}
              </ModuleSelect>
              {loadingProfessions && (
                <p className="mt-1 text-xs text-neutral-500 dark:text-gray-400">
                  Carregando profissões...
                </p>
              )}
              {errors.professionId && (
                <p className="mt-1 text-xs text-red-600 dark:text-red-400">
                  {errors.professionId}
                </p>
              )}
            </ModuleFormGroup>

            {/* Informações sobre permissões padrão do grupo de profissão */}
            {selectedProfession?.group && (
              <div className="mt-3 p-3 bg-slate-50 dark:bg-slate-900/20 border border-slate-200 dark:border-slate-800 rounded-md">
                <div className="flex items-start">
                  <Info className="h-5 w-5 text-slate-500 dark:text-slate-400 mr-2 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-slate-800 dark:text-slate-300">
                      Permissões padrão do grupo de profissão
                    </h4>
                    <p className="text-xs text-slate-700 dark:text-slate-400 mt-1">
                      {isUsingProfessionDefaults
                        ? "Este usuário receberá automaticamente os módulos e permissões padrão definidos para o grupo"
                        : "Este grupo de profissão tem módulos e permissões padrão definidos"} "{selectedProfession.group.name}".
                    </p>

                    {/* Toggle para usar permissões padrão */}
                    <div className="mt-2 flex items-center">
                      <input
                        type="checkbox"
                        id="useDefaultPermissions"
                        checked={isUsingProfessionDefaults}
                        onChange={onProfessionDefaultsToggle}
                        className="h-4 w-4 rounded border-slate-300 dark:border-slate-600 text-slate-600 focus:ring-slate-500 dark:bg-slate-900"
                      />
                      <label htmlFor="useDefaultPermissions" className="ml-2 text-xs text-slate-700 dark:text-slate-400">
                        Usar permissões padrão do grupo
                      </label>
                    </div>

                    {professionDefaultModules.length > 0 && (
                      <div className="mt-2">
                        <p className="text-xs font-medium text-slate-800 dark:text-slate-300">Módulos padrão:</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {professionDefaultModules.map(moduleId => (
                            <span key={moduleId} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-slate-100 dark:bg-slate-800 text-slate-800 dark:text-slate-300">
                              {moduleId}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {professionDefaultPermissions.length > 0 && (
                      <div className="mt-2">
                        <p className="text-xs font-medium text-slate-800 dark:text-slate-300">Permissões padrão: {professionDefaultPermissions.length}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Coluna Direita - Configurações Avançadas */}
        <div className="space-y-6">
          <div className="flex items-center gap-2 mb-4 pb-2 border-b border-gray-300 dark:border-gray-600">
            <CreditCard className="h-4 w-4 text-slate-600" />
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Configurações Avançadas</h3>
          </div>

          {/* Empresa - Apenas para SYSTEM_ADMIN */}
          {isSystemAdmin && (
            <div className="space-y-4 p-4 bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-900/20 dark:to-slate-800/20 rounded-lg border border-slate-200 dark:border-slate-700">
              <div className="flex items-center gap-2 mb-4">
                <Building className="h-4 w-4 text-slate-600" />
                <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Configurações de Empresa</h3>
                <div className="flex items-center gap-1 px-2 py-1 bg-slate-100 dark:bg-slate-900/30 text-slate-700 dark:text-slate-300 rounded-full text-xs">
                  <Info className="h-3 w-3" />
                  System Admin
                </div>
              </div>
              
              <ModuleFormGroup
                moduleColor="admin"
                label={
                  <span className="text-sm font-medium flex items-center gap-2">
                    <span className="w-2 h-2 bg-slate-500 rounded-full"></span>
                    Empresa *
                  </span>
                }
                htmlFor="companyId"
                icon={<Building size={16} />}
                error={!!errors.companyId}
                errorMessage={errors.companyId}
              >
                <ModuleSelect
                  moduleColor="admin"
                  id="companyId"
                  name="companyId"
                  value={formData.companyId || ''}
                  onChange={onChange}
                  disabled={isLoading || loadingCompanies}
                  required
                  placeholder="Selecione uma empresa"
                  error={!!errors.companyId}
                >
                  <option value="">Selecione uma empresa</option>
                  {companies.map((company) => (
                    <option key={company.id} value={company.id}>
                      {company.name} {company.tradingName ? `(${company.tradingName})` : ''}
                    </option>
                  ))}
                </ModuleSelect>
              </ModuleFormGroup>
            </div>
          )}

          {/* Unidade */}
          <div className="space-y-2">
            <ModuleFormGroup
              moduleColor="admin"
              label={
                <span className="text-sm font-medium flex items-center gap-2">
                  <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                  Unidade
                </span>
              }
              htmlFor="branchId"
              icon={<MapPin size={16} />}
              error={!!errors.branchId}
              errorMessage={errors.branchId}
            >
              <ModuleSelect
                moduleColor="admin"
                id="branchId"
                name="branchId"
                value={formData.branchId || ''}
                onChange={onChange}
                disabled={isLoading || loadingBranches || !formData.companyId}
                placeholder="Selecione uma unidade"
                error={!!errors.branchId}
              >
                <option value="">Selecione uma unidade</option>
                {branches.map((branch) => (
                  <option key={branch.id} value={branch.id}>
                    {branch.name} {branch.code ? `(${branch.code})` : ''}
                  </option>
                ))}
              </ModuleSelect>
              {formData.companyId && branches.length === 0 && !loadingBranches && (
                <p className="mt-1 text-xs text-amber-600 dark:text-amber-400">
                  Nenhuma unidade encontrada para esta empresa
                </p>
              )}
            </ModuleFormGroup>
          </div>

          {/* Tipo de documento */}
          <div className="space-y-2">
            <label className={labelClasses}>
              <span className="w-2 h-2 bg-gray-400 rounded-full inline-block mr-2"></span>
              Tipo de Documento
            </label>
            <ModuleRadioGroup
              name="documentType"
              value={formData.documentType}
              onChange={(e) => onDocumentTypeChange(e.target.value)}
              disabled={isLoading}
              options={[
                { value: 'cpf', label: 'CPF' },
                { value: 'cnpj', label: 'CNPJ' }
              ]}
              moduleColor="admin"
              layout="horizontal"
              size="md"
              className="mt-2"
            />
          </div>

          {/* CPF ou CNPJ */}
          <div className="space-y-2">
            <label className={labelClasses} htmlFor={formData.documentType === "cpf" ? "input_cpf" : "input_cnpj"}>
              <span className="w-2 h-2 bg-gray-400 rounded-full inline-block mr-2"></span>
              {String(formData.documentType || 'cpf').toUpperCase()}
            </label>
            <div className="relative">
              <div className={iconContainerClasses}>
                <CreditCard className="h-5 w-5 text-gray-400 dark:text-gray-500" />
              </div>
              {formData.documentType === "cpf" ? (
                <MaskedInput
                  type="cpf"
                  id="input_cpf"
                  name="cpf"
                  value={formData.cpf || ''}
                  onChange={onChange}
                  className={`${inputClasses} ${errors.cpf ? "border-red-500 dark:border-red-700" : ""}`}
                  placeholder="000.000.000-00"
                  disabled={isLoading}
                />
              ) : (
                <MaskedInput
                  type="cnpj"
                  id="input_cnpj"
                  name="cnpj"
                  value={formData.cnpj || ''}
                  onChange={onChange}
                  className={`${inputClasses} ${errors.cnpj ? "border-red-500 dark:border-red-700" : ""}`}
                  placeholder="00.000.000/0000-00"
                  disabled={isLoading}
                />
              )}
            </div>
            {errors.cpf && formData.documentType === "cpf" && (
              <p className={errorClasses}>{errors.cpf}</p>
            )}
            {errors.cnpj && formData.documentType === "cnpj" && (
              <p className={errorClasses}>{errors.cnpj}</p>
            )}
          </div>

          {/* Data de nascimento */}
          <div className="space-y-2">
            <label className={labelClasses} htmlFor="birthDate">
              <span className="w-2 h-2 bg-gray-400 rounded-full inline-block mr-2"></span>
              Data de Nascimento
            </label>
            <div className="relative">
              <div className={iconContainerClasses}>
                <Calendar className="h-5 w-5 text-gray-400 dark:text-gray-500" />
              </div>
              <input
                id="birthDate"
                name="birthDate"
                type="date"
                value={formData.birthDate || ''}
                onChange={onChange}
                min="1900-01-01"
                max={new Date().toISOString().split('T')[0]}
                className={`${inputClasses} ${errors.birthDate ? "border-red-500 dark:border-red-700" : ""}`}
                disabled={isLoading}
              />
            </div>
            {errors.birthDate && (
              <p className={errorClasses}>{errors.birthDate}</p>
            )}
          </div>

          {/* Telefone */}
          <div className="space-y-2">
            <label className={labelClasses} htmlFor="phone">
              <span className="w-2 h-2 bg-gray-400 rounded-full inline-block mr-2"></span>
              Telefone
            </label>
            <div className="relative">
              <div className={iconContainerClasses}>
                <Phone className="h-5 w-5 text-gray-400 dark:text-gray-500" />
              </div>
              <MaskedInput
                type="phone"
                id="phone"
                name="phone"
                value={formData.phone || ''}
                onChange={onChange}
                className={`${inputClasses} ${errors.phone ? "border-red-500 dark:border-red-700" : ""}`}
                placeholder="(00) 00000-0000"
                disabled={isLoading}
              />
            </div>
            {errors.phone && (
              <p className={errorClasses}>{errors.phone}</p>
            )}
          </div>
        </div>
      </div>

      {/* Endereço */}
      <div className="mt-16">
        <div className="flex items-center gap-2 mb-6 pb-2 border-b border-gray-300 dark:border-gray-600">
          <MapPin className="h-4 w-4 text-slate-600" />
          <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Endereço</h3>
        </div>
        <AddressForm
          formData={formData}
          setFormData={(updateFunction) => {
            if (typeof updateFunction === 'function') {
              const updatedData = updateFunction(formData);
              // Atualizar cada campo individualmente
              Object.keys(updatedData).forEach(key => {
                if (updatedData[key] !== formData[key]) {
                  onChange({ target: { name: key, value: updatedData[key] } });
                }
              });
            }
          }}
          errors={errors}
          isLoading={isLoading}
          moduleColor="admin"
          classes={{
            input: inputClasses,
            label: labelClasses,
            error: errorClasses,
            iconContainer: iconContainerClasses
          }}
        />
      </div>

      {/* Senha */}
      <div className="mt-16">
        <div className="flex items-center gap-2 mb-6 pb-2 border-b border-gray-300 dark:border-gray-600">
          <Lock className="h-4 w-4 text-slate-600" />
          <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Senha</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className={labelClasses} htmlFor="password">
            <span className="w-2 h-2 bg-slate-500 rounded-full inline-block mr-2"></span>
            Senha {!user && "*"}
          </label>
          <div className="relative">
            <div className={iconContainerClasses}>
              <Lock className="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </div>
            <input
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              value={formData.password || ''}
              onChange={onChange}
              className={`${passwordInputClasses} ${errors.password ? "border-red-500 dark:border-red-700" : ""}`}
              placeholder={user ? "••••••••" : "Senha"}
              required={!user}
              disabled={isLoading}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
            >
              {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            </button>
          </div>
          {user ? (
            <p className="mt-1 text-xs text-neutral-500 dark:text-gray-400">
              Deixe em branco para manter a senha atual
            </p>
          ) : (
            <p className="mt-1 text-xs text-neutral-500 dark:text-gray-400">
              Mínimo de 6 caracteres
            </p>
          )}
          {errors.password && <p className={errorClasses}>{errors.password}</p>}
        </div>

        <div className="space-y-2">
          <label className={labelClasses} htmlFor="confirmPassword">
            <span className="w-2 h-2 bg-slate-500 rounded-full inline-block mr-2"></span>
            Confirmar Senha {!user && "*"}
          </label>
          <div className="relative">
            <div className={iconContainerClasses}>
              <Lock className="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </div>
            <input
              id="confirmPassword"
              name="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              value={formData.confirmPassword || ''}
              onChange={onChange}
              className={`${passwordInputClasses} ${errors.confirmPassword ? "border-red-500 dark:border-red-700" : ""}`}
              placeholder="Confirme a senha"
              required={!user}
              disabled={isLoading}
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
            >
              {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            </button>
          </div>
          {errors.confirmPassword && (
            <p className={errorClasses}>{errors.confirmPassword}</p>
          )}
        </div>
        </div>
      </div>
    </form>
  );
};

export default UserInfoTab;