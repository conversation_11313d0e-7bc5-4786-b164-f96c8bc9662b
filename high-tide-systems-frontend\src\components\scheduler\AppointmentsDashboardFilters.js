'use client';

import React, { useState, useEffect } from 'react';
import { Calendar, Filter, RefreshCw, Briefcase, ChevronDown } from 'lucide-react';
import { FilterButton } from '@/components/ui/ModuleHeader';
import { ModuleFormGroup, ModuleDatePicker } from '@/components/ui';
import { useAuth } from '@/contexts/AuthContext';

export const AppointmentsDashboardFilters = ({
    filters = {},
    onFiltersChange,
    onSearch,
    isLoading = false,
    companies = []
}) => {
    const { user } = useAuth();
    const isSystemAdmin = user?.role === 'SYSTEM_ADMIN';
    const [isFilterExpanded, setIsFilterExpanded] = useState(false);

    const handleFilterChange = (newFilters) => {
        onFiltersChange(newFilters);
    };

    const handleSearch = (e) => {
        if (e) {
            e.preventDefault();
        }
        onSearch(filters);
    };

    const handleClearFilters = () => {
        const clearedFilters = {
            period: "30dias",
            selectedCompany: "",
            startDate: new Date(new Date().setDate(new Date().getDate() - 30)),
            endDate: new Date()
        };
        onFiltersChange(clearedFilters);
        onSearch(clearedFilters);
    };

    const getActiveFiltersCount = () => {
        return Object.entries(filters).filter(([key, value]) => {
            if (key === 'period') return value !== '30dias';
            if (key === 'selectedCompany') return value !== '';
            return false;
        }).length;
    };

    return (
        <div className="space-y-4">
            {/* Barra de filtros e botões */}
            <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-3 md:items-center">
                <div className="flex-1">
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                        {isSystemAdmin ? 'Selecione o período e empresa para visualizar os dados do dashboard' : 'Selecione o período para visualizar os dados do dashboard'}
                    </div>
                </div>

                <div className="flex gap-2">
                    <FilterButton
                        type="button"
                        onClick={() => setIsFilterExpanded(!isFilterExpanded)}
                        moduleColor="scheduler"
                        variant="secondary"
                    >
                        <div className="flex items-center gap-2">
                            <Filter size={16} className="text-gray-600 dark:text-gray-400" />
                            <span>Filtros</span>
                            <span className="bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-2 py-0.5 rounded-full text-xs">
                                {getActiveFiltersCount()}
                            </span>
                        </div>
                    </FilterButton>

                    <FilterButton
                        type="submit"
                        moduleColor="scheduler"
                        variant="primary"
                        disabled={isLoading}
                    >
                        <div className="flex items-center gap-2">
                            <Calendar size={16} />
                            <span>{isLoading ? "Carregando..." : "Atualizar"}</span>
                        </div>
                    </FilterButton>
                </div>
            </form>

            {/* Filtros avançados (expansíveis) */}
            {isFilterExpanded && (
                <div className="pt-4 border-t border-gray-200 dark:border-gray-700 space-y-4">
                    {filters.period !== 'custom' ? (
                        /* Layout para períodos padrão */
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                            <ModuleFormGroup
                                moduleColor="scheduler"
                                label="Período"
                                htmlFor="period"
                                icon={<Calendar size={16} />}
                            >
                                <div className="relative">
                                    <select
                                        className="w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 text-sm transition-colors duration-200 bg-white dark:bg-gray-700 dark:text-gray-200 appearance-none pr-10 focus:outline-none focus-visible:outline-none focus:ring-2 focus:ring-scheduler-500 dark:focus:ring-scheduler-400"
                                        id="period"
                                        name="period"
                                        value={filters.period || "30dias"}
                                        onChange={(e) => {
                                            const newPeriod = e.target.value;
                                            
                                            if (newPeriod === 'custom') {
                                                // Inicializar com o mês atual
                                                const today = new Date();
                                                const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
                                                const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                                                
                                                handleFilterChange({
                                                    ...filters,
                                                    period: newPeriod,
                                                    startDate: firstDay,
                                                    endDate: lastDay
                                                });
                                            } else {
                                                handleFilterChange({
                                                    ...filters,
                                                    period: newPeriod
                                                });
                                            }
                                        }}
                                    >
                                        <optgroup label="Períodos passados">
                                            <option value="7dias">Últimos 7 dias</option>
                                            <option value="30dias">Últimos 30 dias</option>
                                            <option value="3meses">Últimos 3 meses</option>
                                            <option value="1ano">Último ano</option>
                                        </optgroup>
                                        <optgroup label="Períodos futuros">
                                            <option value="next7dias">Próximos 7 dias</option>
                                            <option value="next30dias">Próximos 30 dias</option>
                                        </optgroup>
                                        <option value="custom">Personalizado</option>
                                    </select>
                                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                        <ChevronDown className="h-4 w-4 text-scheduler-500 dark:text-scheduler-400" />
                                    </div>
                                </div>
                            </ModuleFormGroup>

                            {isSystemAdmin && (
                                <ModuleFormGroup
                                    moduleColor="scheduler"
                                    label="Empresa"
                                    htmlFor="company"
                                    icon={<Briefcase size={16} />}
                                >
                                    <div className="relative">
                                        <select
                                            className="w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 text-sm transition-colors duration-200 bg-white dark:bg-gray-700 dark:text-gray-200 appearance-none pr-10 focus:outline-none focus-visible:outline-none focus:ring-2 focus:ring-scheduler-500 dark:focus:ring-scheduler-400"
                                            id="company"
                                            name="company"
                                            value={filters.selectedCompany || ""}
                                            onChange={(e) => handleFilterChange({
                                                ...filters,
                                                selectedCompany: e.target.value
                                            })}
                                        >
                                            <option value="">Todas as Empresas</option>
                                            {companies.map((company) => (
                                                <option key={company.id} value={company.id}>
                                                    {company.name}
                                                </option>
                                            ))}
                                        </select>
                                        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                            <ChevronDown className="h-4 w-4 text-scheduler-500 dark:text-scheduler-400" />
                                        </div>
                                    </div>
                                </ModuleFormGroup>
                            )}
                        </div>
                    ) : (
                        /* Layout para período personalizado */
                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                            <ModuleFormGroup
                                moduleColor="scheduler"
                                label="Período"
                                htmlFor="period"
                                icon={<Calendar size={16} />}
                            >
                                <div className="relative">
                                    <select
                                        className="w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 text-sm transition-colors duration-200 bg-white dark:bg-gray-700 dark:text-gray-200 appearance-none pr-10 focus:outline-none focus-visible:outline-none focus:ring-2 focus:ring-scheduler-500 dark:focus:ring-scheduler-400"
                                        id="period"
                                        name="period"
                                        value={filters.period || "30dias"}
                                        onChange={(e) => {
                                            const newPeriod = e.target.value;
                                            handleFilterChange({
                                                ...filters,
                                                period: newPeriod
                                            });
                                        }}
                                    >
                                        <optgroup label="Períodos passados">
                                            <option value="7dias">Últimos 7 dias</option>
                                            <option value="30dias">Últimos 30 dias</option>
                                            <option value="3meses">Últimos 3 meses</option>
                                            <option value="1ano">Último ano</option>
                                        </optgroup>
                                        <optgroup label="Períodos futuros">
                                            <option value="next7dias">Próximos 7 dias</option>
                                            <option value="next30dias">Próximos 30 dias</option>
                                        </optgroup>
                                        <option value="custom">Personalizado</option>
                                    </select>
                                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                        <ChevronDown className="h-4 w-4 text-scheduler-500 dark:text-scheduler-400" />
                                    </div>
                                </div>
                            </ModuleFormGroup>

                            <ModuleFormGroup
                                moduleColor="scheduler"
                                label="Data Início"
                                htmlFor="startDate"
                                icon={<Calendar size={16} />}
                            >
                                <ModuleDatePicker
                                    moduleColor="scheduler"
                                    id="startDate"
                                    name="startDate"
                                    value={filters.startDate}
                                    onChange={(date) => handleFilterChange({
                                        ...filters,
                                        startDate: date
                                    })}
                                />
                            </ModuleFormGroup>

                            <ModuleFormGroup
                                moduleColor="scheduler"
                                label="Data Fim"
                                htmlFor="endDate"
                                icon={<Calendar size={16} />}
                            >
                                <ModuleDatePicker
                                    moduleColor="scheduler"
                                    id="endDate"
                                    name="endDate"
                                    value={filters.endDate}
                                    onChange={(date) => handleFilterChange({
                                        ...filters,
                                        endDate: date
                                    })}
                                />
                            </ModuleFormGroup>
                        </div>
                    )}

                    <div>
                        <FilterButton
                            type="button"
                            onClick={handleClearFilters}
                            moduleColor="scheduler"
                            variant="secondary"
                        >
                            <div className="flex items-center gap-2">
                                <RefreshCw size={16} />
                                <span>Limpar Filtros</span>
                            </div>
                        </FilterButton>
                    </div>
                </div>
            )}
        </div>
    );
};