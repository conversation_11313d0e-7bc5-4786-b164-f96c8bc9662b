"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar/index.js":
/*!***************************************************!*\
  !*** ./src/components/dashboard/Sidebar/index.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Menu,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Menu,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Menu,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/dashboard/components */ \"(app-pages-browser)/./src/app/dashboard/components.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useSchedulingPreferences */ \"(app-pages-browser)/./src/hooks/useSchedulingPreferences.js\");\n/* harmony import */ var _components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/CustomScrollArea */ \"(app-pages-browser)/./src/components/ui/CustomScrollArea.js\");\n/* harmony import */ var _hooks_useConstructionMessage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useConstructionMessage */ \"(app-pages-browser)/./src/hooks/useConstructionMessage.js\");\n/* harmony import */ var _components_construction__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/construction */ \"(app-pages-browser)/./src/components/construction/index.js\");\n/* harmony import */ var _utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/constructionUtils */ \"(app-pages-browser)/./src/utils/constructionUtils.js\");\n/* harmony import */ var _components_dashboard_CompanySelector__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/CompanySelector */ \"(app-pages-browser)/./src/components/dashboard/CompanySelector.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ModuleTitle = (param)=>{\n    let { moduleId, title, icon: Icon, isCollapsed } = param;\n    // Exibir o logo no lugar do título do módulo, como botão para /dashboard\n    const handleLogoClick = ()=>{\n        if (true) {\n            window.location.href = '/dashboard';\n        }\n    };\n    if (isCollapsed) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-6 flex justify-center items-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleLogoClick,\n                className: \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 focus:outline-none cursor-pointer\",\n                \"aria-label\": \"Ir para o dashboard\",\n                title: \"High Tide - Ir para dashboard\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/logo_icon_sem_fundo.png\",\n                    alt: \"High Tide\",\n                    className: \"w-8 h-8 dark:invert transition-all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 33,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6 flex justify-center items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleLogoClick,\n            style: {\n                background: 'none',\n                border: 'none',\n                padding: 0\n            },\n            className: \"focus:outline-none cursor-pointer\",\n            \"aria-label\": \"Ir para o dashboard\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/logo_horizontal_sem_fundo.png\",\n                    alt: \"High Tide Logo\",\n                    className: \"h-12 dark:invert dark:text-white transition-all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ModuleTitle;\n// Mapeamento de submenus para permissões\nconst submenuPermissionsMap = {\n    // Admin\n    'admin.introduction': 'admin.dashboard.view',\n    'admin.dashboard': 'admin.dashboard.view',\n    'admin.users': 'admin.users.view',\n    'admin.professions': [\n        'admin.professions.view',\n        'admin.profession-groups.view'\n    ],\n    'admin.bug-reports': 'admin.dashboard.view',\n    'admin.plans': 'admin.dashboard.view',\n    'admin.logs': 'admin.logs.view',\n    'admin.settings': 'admin.config.edit',\n    'admin.backup': 'admin.config.edit',\n    'admin.affiliates': 'admin.dashboard.view',\n    // ABA+\n    'abaplus.anamnese': 'abaplus.anamnese.view',\n    'abaplus.evolucoes-diarias': 'abaplus.evolucoes-diarias.view',\n    'abaplus.sessao': 'abaplus.sessao.view',\n    // Financeiro\n    'financial.invoices': 'financial.invoices.view',\n    'financial.payments': 'financial.payments.view',\n    'financial.expenses': 'financial.expenses.view',\n    'financial.reports': 'financial.reports.view',\n    'financial.cashflow': 'financial.reports.view',\n    // RH\n    'hr.employees': 'rh.employees.view',\n    'hr.payroll': 'rh.payroll.view',\n    'hr.documents': 'rh.employees.view',\n    'hr.departments': 'rh.employees.view',\n    'hr.attendance': 'rh.attendance.view',\n    'hr.benefits': 'rh.benefits.view',\n    'hr.training': 'rh.employees.view',\n    // Pessoas\n    'people.clients': 'people.clients.view',\n    'people.persons': 'people.persons.view',\n    'people.documents': 'people.documents.view',\n    'people.insurances': 'people.insurances.view',\n    'people.insurance-limits': 'people.insurance-limits.view',\n    // Agendamento\n    'scheduler.calendar': 'scheduling.calendar.view',\n    'scheduler.working-hours': 'scheduling.working-hours.view',\n    'scheduler.service-types': 'scheduling.service-types.view',\n    'scheduler.locations': 'scheduling.locations.view',\n    'scheduler.occupancy': 'scheduling.occupancy.view',\n    'scheduler.appointment-requests': 'scheduling.appointment-requests.view',\n    'scheduler.appointments-report': 'scheduling.appointments-report.view',\n    'scheduler.appointments-dashboard': 'scheduling.appointments-dashboard.view'\n};\nconst Sidebar = (param)=>{\n    let { activeModule, activeModuleTitle, isSubmenuActive, handleModuleSubmenuClick, handleBackToModules, isSidebarOpen } = param;\n    var _moduleSubmenus_activeModule;\n    _s();\n    const { can, hasModule, isAdmin } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { schedulingPreferences, isLoading: preferencesLoading } = (0,_hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__.useSchedulingPreferences)();\n    // Estado para controlar se a sidebar está colapsada\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Sidebar.useState\": ()=>{\n            if (true) {\n                const savedCollapsed = localStorage.getItem('sidebarCollapsed');\n                return savedCollapsed === 'true';\n            }\n            return false;\n        }\n    }[\"Sidebar.useState\"]);\n    // Inicializar o estado de grupos expandidos a partir do localStorage\n    const [expandedGroups, setExpandedGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Sidebar.useState\": ()=>{\n            // Verificar se estamos no cliente (browser) antes de acessar localStorage\n            if (true) {\n                const savedState = localStorage.getItem('sidebarExpandedGroups');\n                return savedState ? JSON.parse(savedState) : {};\n            }\n            return {};\n        }\n    }[\"Sidebar.useState\"]);\n    // Encontrar o objeto do módulo ativo para acessar seu ícone\n    const activeModuleObject = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.modules.find((m)=>m.id === activeModule);\n    const ModuleIcon = activeModuleObject === null || activeModuleObject === void 0 ? void 0 : activeModuleObject.icon;\n    // Função para verificar se o usuário tem permissão para ver um submenu\n    const hasPermissionForSubmenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenu]\": (moduleId, submenuId)=>{\n            var _moduleSubmenus_moduleId;\n            // Ignorar verificação para grupos, pois a permissão é verificada para cada item\n            if (submenuId === 'cadastro' || submenuId === 'configuracoes' || submenuId === 'gestao' || submenuId === 'convenios' || submenuId === 'financeiro' || submenuId === 'relatorios') {\n                return true;\n            }\n            // Buscar o submenu específico para verificar systemAdminOnly\n            const submenuItem = (_moduleSubmenus_moduleId = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId]) === null || _moduleSubmenus_moduleId === void 0 ? void 0 : _moduleSubmenus_moduleId.find({\n                \"Sidebar.useCallback[hasPermissionForSubmenu]\": (item)=>item.id === submenuId\n            }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"]);\n            // Verificar se o submenu requer SYSTEM_ADMIN\n            if (submenuItem === null || submenuItem === void 0 ? void 0 : submenuItem.systemAdminOnly) {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            // Verificação especial adicional para afiliados - apenas SYSTEM_ADMIN\n            if (submenuId === 'affiliates') {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            // Verificação especial adicional para bug-reports - apenas SYSTEM_ADMIN\n            if (submenuId === 'bug-reports') {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            const permissionKey = \"\".concat(moduleId, \".\").concat(submenuId);\n            const requiredPermission = submenuPermissionsMap[permissionKey];\n            // Se não há mapeamento de permissão, permitir acesso\n            if (!requiredPermission) return true;\n            // Administradores têm acesso a tudo (exceto itens systemAdminOnly)\n            if (isAdmin()) return true;\n            // Verificar permissão específica\n            if (Array.isArray(requiredPermission)) {\n                // Se for um array de permissões, verificar se o usuário tem pelo menos uma delas\n                return requiredPermission.some({\n                    \"Sidebar.useCallback[hasPermissionForSubmenu]\": (perm)=>can(perm)\n                }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"]);\n            } else {\n                // Se for uma única permissão, verificar normalmente\n                return can(requiredPermission);\n            }\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"], [\n        can,\n        isAdmin,\n        user.role\n    ]);\n    // Função para verificar se um submenu deve ser exibido baseado nas preferências\n    const shouldShowSubmenuByPreferences = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[shouldShowSubmenuByPreferences]\": (moduleId, submenuId)=>{\n            // Apenas verificar preferências para o módulo de agendamento\n            if (moduleId !== 'scheduler') return true;\n            // Se as preferências ainda estão carregando, não mostrar os itens que podem ser escondidos\n            if (preferencesLoading) {\n                return false;\n            }\n            // Verificar se as preferências estão carregadas\n            if (!schedulingPreferences) {\n                return false;\n            }\n            switch(submenuId){\n                case 'locations':\n                    const showLocations = schedulingPreferences.showLocations !== false;\n                    return showLocations;\n                case 'service-types':\n                    const showServiceTypes = schedulingPreferences.showServiceTypes !== false;\n                    return showServiceTypes;\n                case 'insurances':\n                    const showInsurance = schedulingPreferences.showInsurance !== false;\n                    return showInsurance;\n                case 'working-hours':\n                    const showWorkingHours = schedulingPreferences.showWorkingHours !== false;\n                    return showWorkingHours;\n                default:\n                    return true;\n            }\n        }\n    }[\"Sidebar.useCallback[shouldShowSubmenuByPreferences]\"], [\n        schedulingPreferences,\n        preferencesLoading\n    ]);\n    // Função para alternar o colapso da sidebar\n    const toggleCollapse = ()=>{\n        const newCollapsedState = !isCollapsed;\n        setIsCollapsed(newCollapsedState);\n        if (true) {\n            localStorage.setItem('sidebarCollapsed', newCollapsedState.toString());\n        }\n    };\n    // Função para alternar a expansão de um grupo\n    const toggleGroup = (groupId)=>{\n        setExpandedGroups((prev)=>({\n                ...prev,\n                [groupId]: !prev[groupId]\n            }));\n    };\n    // Verificar se algum item dentro de um grupo está ativo\n    const isGroupActive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[isGroupActive]\": (moduleId, groupItems)=>{\n            return groupItems.some({\n                \"Sidebar.useCallback[isGroupActive]\": (item)=>isSubmenuActive(moduleId, item.id)\n            }[\"Sidebar.useCallback[isGroupActive]\"]);\n        }\n    }[\"Sidebar.useCallback[isGroupActive]\"], [\n        isSubmenuActive\n    ]);\n    // Expandir automaticamente grupos que contêm o item ativo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            if (activeModule && _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) {\n                const newExpandedGroups = {\n                    ...expandedGroups\n                };\n                _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule].forEach({\n                    \"Sidebar.useEffect\": (submenu)=>{\n                        if (submenu.type === 'group' && isGroupActive(activeModule, submenu.items)) {\n                            newExpandedGroups[submenu.id] = true;\n                        }\n                    }\n                }[\"Sidebar.useEffect\"]);\n                setExpandedGroups(newExpandedGroups);\n            }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"Sidebar.useEffect\"], [\n        activeModule,\n        pathname,\n        isGroupActive\n    ]);\n    // Verificar se um item de submenu tem permissão para ser exibido\n    const hasPermissionForSubmenuItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (moduleId, submenuId)=>{\n            var _moduleSubmenus_moduleId;\n            // Buscar o item dentro dos grupos do módulo\n            let submenuItem = null;\n            // Primeiro buscar nos itens diretos\n            submenuItem = (_moduleSubmenus_moduleId = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId]) === null || _moduleSubmenus_moduleId === void 0 ? void 0 : _moduleSubmenus_moduleId.find({\n                \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (item)=>item.id === submenuId\n            }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"]);\n            // Se não encontrou, buscar dentro dos grupos\n            if (!submenuItem) {\n                for (const item of _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId] || []){\n                    if (item.type === 'group' && item.items) {\n                        submenuItem = item.items.find({\n                            \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (subItem)=>subItem.id === submenuId\n                        }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"]);\n                        if (submenuItem) break;\n                    }\n                }\n            }\n            // Verificar se o item requer SYSTEM_ADMIN\n            if (submenuItem === null || submenuItem === void 0 ? void 0 : submenuItem.systemAdminOnly) {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            return hasPermissionForSubmenu(moduleId, submenuId);\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"], [\n        hasPermissionForSubmenu,\n        user === null || user === void 0 ? void 0 : user.role\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"\".concat(isCollapsed ? 'w-16' : 'w-72', \" bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col \").concat(isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'),\n        \"aria-label\": \"Navega\\xe7\\xe3o lateral\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"flex-1 p-5\",\n                moduleColor: activeModule,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toggleCollapse,\n                            className: \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200\",\n                            \"aria-label\": isCollapsed ? 'Expandir sidebar' : 'Minimizar sidebar',\n                            title: isCollapsed ? 'Expandir sidebar' : 'Minimizar sidebar',\n                            children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 313,\n                                columnNumber: 28\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 313,\n                                columnNumber: 57\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, undefined),\n                    activeModuleObject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModuleTitle, {\n                        moduleId: activeModule,\n                        title: activeModuleTitle,\n                        icon: ModuleIcon,\n                        isCollapsed: isCollapsed\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, undefined),\n                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_CompanySelector__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        activeModule: activeModule\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 328,\n                        columnNumber: 26\n                    }, undefined),\n                    preferencesLoading && activeModule === 'scheduler' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, undefined),\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-gray-500 dark:text-gray-400\",\n                                children: \"Carregando...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 334,\n                                columnNumber: 30\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2 flex flex-col justify-center items-center mt-8 \".concat(isCollapsed ? 'px-1' : ''),\n                        \"aria-labelledby\": \"sidebar-heading\",\n                        children: !(preferencesLoading && activeModule === 'scheduler') && activeModule && ((_moduleSubmenus_activeModule = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) === null || _moduleSubmenus_activeModule === void 0 ? void 0 : _moduleSubmenus_activeModule.map((submenu)=>{\n                            // Verificar se é um grupo ou um item individual\n                            if (submenu.type === 'group') {\n                                // Verificar se algum item do grupo tem permissão para ser exibido\n                                const hasAnyPermission = submenu.items.some((item)=>hasPermissionForSubmenuItem(activeModule, item.id));\n                                if (!hasAnyPermission) {\n                                    return null; // Não renderizar o grupo se nenhum item tiver permissão\n                                }\n                                const isGroupExpanded = expandedGroups[submenu.id] || false;\n                                const isAnyItemActive = isGroupActive(activeModule, submenu.items);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleGroup(submenu.id),\n                                            className: \"\\n                        group w-full flex items-center justify-between px-4 py-2.5 rounded-lg transition-all duration-300\\n                        \".concat(isAnyItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark font-medium\") : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700', \"\\n                      \"),\n                                            \"aria-expanded\": isGroupExpanded,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-left\",\n                                                    children: submenu.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-500 dark:text-gray-400\",\n                                                    children: isGroupExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 27\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 363,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (isGroupExpanded || isCollapsed) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(isCollapsed ? '' : 'ml-4 pl-2 border-l border-gray-200 dark:border-gray-700', \" space-y-1\"),\n                                            children: submenu.items.map((item)=>{\n                                                const isItemActive = isSubmenuActive(activeModule, item.id);\n                                                // Verificar permissão antes de renderizar o item\n                                                if (!hasPermissionForSubmenuItem(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não tiver permissão\n                                                }\n                                                // Verificar se o item deve ser exibido baseado nas preferências\n                                                if (!shouldShowSubmenuByPreferences(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não estiver habilitado nas preferências\n                                                }\n                                                // Verificar se o item está em construção\n                                                const itemKey = \"\".concat(activeModule, \".\").concat(item.id);\n                                                const isItemUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.isUnderConstruction)(activeModule, item.id);\n                                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.getConstructionMessage)(activeModule, item.id);\n                                                // Estilo comum para os itens\n                                                const itemClassName = \"\\n                          group w-full flex items-center \".concat(isCollapsed ? 'justify-center px-2 py-2' : 'gap-3 px-3 py-2', \" rounded-lg transition-all duration-300\\n                          \").concat(isItemActive ? \"rounded-xl border border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                               bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                               shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n                        \");\n                                                // Se estiver em construção, usar o ConstructionButton\n                                                if (isItemUnderConstruction) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_9__.ConstructionButton, {\n                                                        className: itemClassName,\n                                                        \"aria-current\": isItemActive ? 'page' : undefined,\n                                                        title: constructionMessage.title,\n                                                        content: constructionMessage.content,\n                                                        icon: constructionMessage.icon,\n                                                        position: \"right\",\n                                                        children: [\n                                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\\n                                  \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                                \"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                    size: 18,\n                                                                    \"aria-hidden\": \"true\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 35\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 33\n                                                            }, undefined),\n                                                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                                children: item.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 48\n                                                            }, undefined)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 29\n                                                    }, undefined);\n                                                }\n                                                // Se não estiver em construção, usar o botão normal\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleModuleSubmenuClick(activeModule, item.id),\n                                                    className: itemClassName,\n                                                    \"aria-current\": isItemActive ? 'page' : undefined,\n                                                    children: [\n                                                        item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"\\n                                \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                              \"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                size: 18,\n                                                                \"aria-hidden\": \"true\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 31\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 27\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 387,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 360,\n                                    columnNumber: 17\n                                }, undefined);\n                            } else {\n                                // Renderização de itens individuais (não agrupados)\n                                const isActive = isSubmenuActive(activeModule, submenu.id);\n                                // Verificar permissão antes de renderizar o item\n                                const hasPermission = hasPermissionForSubmenu(activeModule, submenu.id);\n                                if (!hasPermission) {\n                                    return null; // Não renderizar se não tiver permissão\n                                }\n                                // Verificar se o submenu deve ser exibido baseado nas preferências\n                                const shouldShowByPreferences = shouldShowSubmenuByPreferences(activeModule, submenu.id);\n                                if (!shouldShowByPreferences) {\n                                    return null; // Não renderizar se não estiver habilitado nas preferências\n                                }\n                                // Verificar se o submenu está em construção\n                                const submenuKey = \"\".concat(activeModule, \".\").concat(submenu.id);\n                                const isSubmenuUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.isUnderConstruction)(activeModule, submenu.id);\n                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.getConstructionMessage)(activeModule, submenu.id);\n                                // Estilo comum para ambos os tipos de botões\n                                const buttonClassName = \"\\n                group w-full flex items-center \".concat(isCollapsed ? 'justify-center px-2 py-3' : 'gap-3 px-4 py-3', \" rounded-lg transition-all duration-300\\n                \").concat(isActive ? \"rounded-xl border border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                     bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                     shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n              \");\n                                // Se estiver em construção, usar o ConstructionButton\n                                if (isSubmenuUnderConstruction) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_9__.ConstructionButton, {\n                                        className: buttonClassName,\n                                        \"aria-current\": isActive ? 'page' : undefined,\n                                        title: constructionMessage.title,\n                                        content: constructionMessage.content,\n                                        icon: constructionMessage.icon,\n                                        position: \"right\",\n                                        children: [\n                                            submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                    size: 20,\n                                                    \"aria-hidden\": \"true\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 523,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                                children: submenu.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 535,\n                                                columnNumber: 38\n                                            }, undefined)\n                                        ]\n                                    }, submenu.id, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                        lineNumber: 513,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }\n                                // Se não estiver em construção, usar o botão normal\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleModuleSubmenuClick(activeModule, submenu.id),\n                                    className: buttonClassName,\n                                    \"aria-current\": isActive ? 'page' : undefined,\n                                    children: [\n                                        submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                size: 20,\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 555,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 549,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                            children: submenu.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 561,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 542,\n                                    columnNumber: 17\n                                }, undefined);\n                            }\n                        }))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(isCollapsed ? 'p-2' : 'p-5', \" border-t border-gray-100 dark:border-gray-700 mt-auto\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToModules,\n                    className: \"\\n            group w-full \".concat(isCollapsed ? 'py-3 px-2 justify-center' : 'py-3 px-4 gap-3', \" rounded-lg flex items-center\\n            border-2 border-module-\").concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n            bg-transparent dark:bg-gray-800 hover:bg-module-\").concat(activeModule, \"-bg/10 dark:hover:bg-module-\").concat(activeModule, \"-bg-dark/10\\n            transition-all duration-300\\n          \"),\n                    \"aria-label\": \"Voltar para o dashboard principal\",\n                    title: isCollapsed ? 'Voltar a Tela Inicial' : undefined,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            flex items-center justify-center w-8 h-8 rounded-full\\n            bg-module-\".concat(activeModule, \"-bg dark:bg-module-\").concat(activeModule, \"-bg-dark/70\\n            text-module-\").concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\\n            shadow-sm dark:shadow-md dark:shadow-black/20\\n            group-hover:scale-110 transition-transform duration-200\\n          \"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 589,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 582,\n                            columnNumber: 11\n                        }, undefined),\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-gray-800 dark:text-gray-200\",\n                            children: \"Voltar a Tela Inicial\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 591,\n                            columnNumber: 28\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 571,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 570,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 298,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Sidebar, \"VAGN2v+pOWDhD1NheeJLNqum7jI=\", false, function() {\n    return [\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__.useSchedulingPreferences\n    ];\n});\n_c1 = Sidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\nvar _c, _c1;\n$RefreshReg$(_c, \"ModuleTitle\");\n$RefreshReg$(_c1, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar/index.js\n"));

/***/ })

});