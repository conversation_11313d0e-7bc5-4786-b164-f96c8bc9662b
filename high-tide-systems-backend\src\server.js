// src/server.js
require('dotenv').config();
const express = require('express');
const cors = require('cors');
const http = require('http');
const rateLimit = require('express-rate-limit');
const { xss } = require('express-xss-sanitizer');
const swaggerUi = require('swagger-ui-express');
const swagger = require('./config/swagger');
const socketService = require('./socket/socketService');
const { authenticate } = require('./middlewares/auth');

//Admin module
const userRoutes = require('./routes/admin/userRoutes');
const emailConfigRoutes = require('./routes/admin/emailConfigRoutes');
const companyRoutes = require('./routes/admin/companyRoutes');
const auditLogRoutes = require('./routes/admin/auditLogRoutes');
const branchRoutes = require('./routes/admin/branchRoutes');
const adminDashboardRoutes = require('./routes/admin/adminDashboardRoutes');
const settingsRoutes = require('./routes/admin/settingsRoutes');
const preferencesRoutes = require('./routes/admin/preferencesRoutes');

//Person module
const documentRoutes = require('./routes/person/documentRoutes');
const locationRoutes = require('./routes/person/locationRoutes');
const clientRoutes = require('./routes/person/clientRoutes');
const insuranceRoutes = require('./routes/person/insuranceRoutes');
const insuranceServiceLimitRoutes = require('./routes/person/insuranceServiceLimitRoutes');
const serviceTypeRoutes = require('./routes/scheduling/serviceTypeRoutes');
const personRoutes = require('./routes/person/personRoutes');
const contactRoutes = require('./routes/person/contactRoutes');

//Scheduling Module
const schedulingRoutes = require('./routes/scheduling/schedulingRoutes');
const recurrenceRoutes = require('./routes/scheduling/recurrenceRoutes');
const workingHoursRoutes = require('./routes/scheduling/workingHoursRoutes');
const notificationRoutes = require('./routes/notificationRoutes');
const notificationPermissionRoutes = require('./routes/notificationPermissionRoutes');
const occupancyRoutes = require('./routes/person/occupancyRoutes');

//Reports Module
const reportRoutes = require('./routes/reports/reportRoutes');

//Chat Module
const chatRoutes = require('./routes/chat');

//ABA+ Module
const abaRoutes = require('./routes/aba');

//Subscription Module (Stripe Integration)
const subscriptionRoutes = require('./routes/subscriptionRoutes');

//Health Check Routes
const healthRoutes = require('./routes/healthRoutes');

const authRoutes = require('./routes/authRoutes');
const twoFactorRoutes = require('./routes/twoFactorRoutes');
const cepRoutes = require('./routes/cepRoutes');
const professionRoutes = require('./routes/professionRoutes');
const modulePreferencesRoutes = require('./routes/modulePreferencesRoutes');
const affiliateRoutes = require('./routes/affiliateRoutes');
const couponRoutes = require('./routes/couponRoutes');
const bugReportRoutes = require('./routes/bugReportRoutes');

// Public Routes
const publicRoutes = require('./routes/publicRoutes');
const appointmentConfirmationRoutes = require('./routes/appointmentConfirmationRoutes');

// Appointment Request Module
const appointmentRequestRoutes = require('./routes/appointmentRequest/appointmentRequestRoutes');

// Serviços
const emailService = require('./services/emailService');
const reminderService = require('./services/reminderService');
const appointmentStatusService = require('./services/appointmentStatusService');
const cacheService = require('./services/cacheService');
const loggerService = require('./services/loggerService');
const healthCheckService = require('./services/healthCheckService');

// Inicializa aplicação Express
const app = express();

const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000,
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply rate limiting to all requests
app.use(apiLimiter);

// More restrictive rate limiting for auth routes
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 30,
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many login attempts, please try again after 15 minutes'
});

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = (process.env.CORS_ORIGIN || 'http://localhost:3000').split(',');

    // Permitir requisições sem origin (como apps mobile ou Postman)
    if (!origin) return callback(null, true);

    // Verificar se é um domínio do GitHub Codespaces
    if (origin.endsWith('.app.github.dev')) {
      return callback(null, true);
    }

    if (allowedOrigins.indexOf(origin) !== -1 || allowedOrigins.includes('*')) {
      callback(null, true);
    } else {
      console.log(`CORS bloqueado para origem: ${origin}`);
      callback(new Error('Not allowed by CORS'), false);
    }
  },
  methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
  preflightContinue: false,
  optionsSuccessStatus: 204,
  credentials: true,
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  maxAge: 86400 // Preflight results cached for 24 hours
};

app.use(cors(corsOptions));

app.options('*', cors(corsOptions));

// Special middleware for Stripe webhooks - needs raw body
app.use((req, res, next) => {
  if (req.originalUrl === '/subscription/webhook') {
    next();
  } else {
    express.json({ limit: '10mb' })(req, res, next);
  }
});

app.use(xss()); // Sanitize inputs against XSS

// Request logging middleware
const { requestLoggerMiddleware, errorLoggerMiddleware } = require('./middlewares/requestLogger');
app.use(requestLoggerMiddleware);

// Servir arquivos estáticos da pasta uploads
const path = require('path');
const uploadsPath = path.join(__dirname, '../uploads');
app.use('/uploads', express.static(uploadsPath));
console.log('Servindo arquivos estáticos da pasta:', uploadsPath);

// Verificar se o diretório de uploads existe
const fs = require('fs');
if (!fs.existsSync(uploadsPath)) {
  console.log('Diretório de uploads não existe, criando...');
  fs.mkdirSync(uploadsPath, { recursive: true });
  console.log('Diretório de uploads criado com sucesso');
} else {
  console.log('Diretório de uploads já existe');
}

// Verificar se o diretório de imagens de perfil existe
const profileImagesPath = path.join(uploadsPath, 'profile-images');
if (!fs.existsSync(profileImagesPath)) {
  console.log('Diretório de imagens de perfil não existe, criando...');
  fs.mkdirSync(profileImagesPath, { recursive: true });
  console.log('Diretório de imagens de perfil criado com sucesso');
} else {
  console.log('Diretório de imagens de perfil já existe');
}

app.set('trust proxy', 'loopback, linklocal, uniquelocal');

// Route for Swagger documentation
app.use('/api-docs', swagger.serve, swagger.setup);

// JSON export of Swagger specs
app.get('/api-docs.json', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.send(swagger.specs);
});

// Apply auth rate limiter to login/register routes
app.use('/auth/login', authLimiter);
app.use('/auth/register', authLimiter);

// Routes
app.use('/auth', authRoutes);
app.use('/2fa', twoFactorRoutes);
app.use('/users', userRoutes);
app.use('/documents', documentRoutes);
app.use('/schedulings', schedulingRoutes);
app.use('/locations', locationRoutes);
app.use('/service-types', serviceTypeRoutes);
app.use('/insurances', insuranceRoutes);
app.use('/insurance-service-limits', insuranceServiceLimitRoutes);
app.use('/clients', clientRoutes);
app.use('/recurrences', recurrenceRoutes);
app.use('/working-hours', workingHoursRoutes);
app.use('/notifications', notificationRoutes);
app.use('/notification-permissions', notificationPermissionRoutes);
app.use('/email-configs', emailConfigRoutes);
app.use('/branches', branchRoutes);
app.use('/companies', companyRoutes);
app.use('/audit-logs', auditLogRoutes);
app.use('/persons', personRoutes);
app.use('/contacts', contactRoutes);
app.use('/adminDashboard', adminDashboardRoutes);
app.use('/occupancy', occupancyRoutes);
app.use('/insurance-service-limits', insuranceServiceLimitRoutes);
app.use('/cep', cepRoutes);
app.use('/settings', settingsRoutes);
app.use('/reports', reportRoutes);
app.use('/professions', professionRoutes);
app.use('/module-preferences', modulePreferencesRoutes);
app.use('/chat', chatRoutes);
app.use('/aba', abaRoutes);
app.use('/subscription', subscriptionRoutes);
app.use('/health', healthRoutes);
app.use('/admin/preferences', authenticate, preferencesRoutes);
app.use('/affiliates', affiliateRoutes);
app.use('/coupons', couponRoutes);
app.use('/bug-reports', bugReportRoutes);
app.use('/public', publicRoutes);
app.use('/appointments', appointmentConfirmationRoutes);
app.use('/appointment-requests', appointmentRequestRoutes);
const documentPermissionRoutes = require('./routes/documentPermissionRoutes');
app.use('/document-permissions', documentPermissionRoutes);
const locationAvailabilityRoutes = require('./routes/locationAvailabilityRoutes');
app.use('/locations', locationAvailabilityRoutes);

// Status route
app.get('/', (req, res) => {
  res.json({
    status: 'API running',
    version: process.env.npm_package_version || '1.0.0',
    documentation: `${req.protocol}://${req.get('host')}/api-docs`
  });
});

// Error handling middleware
app.use(errorLoggerMiddleware);
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);

  // Tratamento específico para erro de CORS
  if (err.message === 'Not allowed by CORS') {
    return res.status(403).json({ message: 'CORS not allowed for this origin' });
  }

  res.status(500).json({
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ message: 'Endpoint not found' });
});

const PORT = process.env.PORT || 5000;

// Criar servidor HTTP
const server = http.createServer(app);

// Iniciar o servidor
server.listen(PORT, '0.0.0.0', async () => {
  try {
    // Create logs directory if it doesn't exist
    const fs = require('fs');
    const logsPath = path.join(__dirname, '../logs');
    if (!fs.existsSync(logsPath)) {
      fs.mkdirSync(logsPath, { recursive: true });
      console.log('Logs directory created');
    }

    // Initialize logger service
    loggerService.info('SERVER_STARTING', {
      port: PORT,
      environment: process.env.NODE_ENV || 'development',
      nodeVersion: process.version
    });

    // Initialize email service
    await emailService.initialize();

    // Initialize cache service
    await cacheService.initialize();

    // Initialize Socket.IO service
    await socketService.initialize(server, cacheService.getClient());

    // Start services in production/staging
    if (process.env.NODE_ENV !== 'test') {
      reminderService.start();
      appointmentStatusService.start();
      loggerService.info('SERVICES_STARTED', {
        services: ['reminderService', 'appointmentStatusService']
      });
    }

    loggerService.info('SERVER_STARTED', {
      port: PORT,
      environment: process.env.NODE_ENV || 'development',
      swaggerDocs: `http://localhost:${PORT}/api-docs`,
      healthCheck: `http://localhost:${PORT}/health`
    });

    console.log(`Server running on port ${PORT}`);
    console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`Swagger docs available at http://localhost:${PORT}/api-docs`);
    console.log(`Health check available at http://localhost:${PORT}/health`);
    console.log(`WebSocket server initialized`);
  } catch (error) {
    loggerService.error('SERVER_STARTUP_ERROR', {
      error: error.message,
      stack: error.stack
    });
    console.error('Error during server startup:', error);
  }
});

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down server...');
  reminderService.stop();
  appointmentStatusService.stop();
  await socketService.shutdown();
  await cacheService.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  reminderService.stop();
  appointmentStatusService.stop();
  await socketService.shutdown();
  await cacheService.close();
  process.exit(0);
});

// Unhandled rejections and exceptions
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  // Exit with error in production to allow process manager to restart
  if (process.env.NODE_ENV === 'production') {
    process.exit(1);
  }
});