"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar/index.js":
/*!***************************************************!*\
  !*** ./src/components/dashboard/Sidebar/index.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,PanelLeftClose,PanelLeftOpen,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panel-left-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,PanelLeftClose,PanelLeftOpen,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panel-left-close.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,PanelLeftClose,PanelLeftOpen,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,PanelLeftClose,PanelLeftOpen,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,PanelLeftClose,PanelLeftOpen,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/dashboard/components */ \"(app-pages-browser)/./src/app/dashboard/components.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useSchedulingPreferences */ \"(app-pages-browser)/./src/hooks/useSchedulingPreferences.js\");\n/* harmony import */ var _components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/CustomScrollArea */ \"(app-pages-browser)/./src/components/ui/CustomScrollArea.js\");\n/* harmony import */ var _hooks_useConstructionMessage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useConstructionMessage */ \"(app-pages-browser)/./src/hooks/useConstructionMessage.js\");\n/* harmony import */ var _components_construction__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/construction */ \"(app-pages-browser)/./src/components/construction/index.js\");\n/* harmony import */ var _utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/constructionUtils */ \"(app-pages-browser)/./src/utils/constructionUtils.js\");\n/* harmony import */ var _components_dashboard_CompanySelector__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/CompanySelector */ \"(app-pages-browser)/./src/components/dashboard/CompanySelector.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ModuleTitle = (param)=>{\n    let { moduleId, title, icon: Icon } = param;\n    // Exibir o logo no lugar do título do módulo, como botão para /dashboard\n    const handleLogoClick = ()=>{\n        if (true) {\n            window.location.href = '/dashboard';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6 flex justify-center items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleLogoClick,\n            style: {\n                background: 'none',\n                border: 'none',\n                padding: 0\n            },\n            className: \"focus:outline-none cursor-pointer\",\n            \"aria-label\": \"Ir para o dashboard\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/logo_horizontal_sem_fundo.png\",\n                    alt: \"High Tide Logo\",\n                    className: \"h-12 dark:invert dark:text-white transition-all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ModuleTitle;\n// Mapeamento de submenus para permissões\nconst submenuPermissionsMap = {\n    // Admin\n    'admin.introduction': 'admin.dashboard.view',\n    'admin.dashboard': 'admin.dashboard.view',\n    'admin.users': 'admin.users.view',\n    'admin.professions': [\n        'admin.professions.view',\n        'admin.profession-groups.view'\n    ],\n    'admin.bug-reports': 'admin.dashboard.view',\n    'admin.plans': 'admin.dashboard.view',\n    'admin.logs': 'admin.logs.view',\n    'admin.settings': 'admin.config.edit',\n    'admin.backup': 'admin.config.edit',\n    'admin.affiliates': 'admin.dashboard.view',\n    // ABA+\n    'abaplus.anamnese': 'abaplus.anamnese.view',\n    'abaplus.evolucoes-diarias': 'abaplus.evolucoes-diarias.view',\n    'abaplus.sessao': 'abaplus.sessao.view',\n    // Financeiro\n    'financial.invoices': 'financial.invoices.view',\n    'financial.payments': 'financial.payments.view',\n    'financial.expenses': 'financial.expenses.view',\n    'financial.reports': 'financial.reports.view',\n    'financial.cashflow': 'financial.reports.view',\n    // RH\n    'hr.employees': 'rh.employees.view',\n    'hr.payroll': 'rh.payroll.view',\n    'hr.documents': 'rh.employees.view',\n    'hr.departments': 'rh.employees.view',\n    'hr.attendance': 'rh.attendance.view',\n    'hr.benefits': 'rh.benefits.view',\n    'hr.training': 'rh.employees.view',\n    // Pessoas\n    'people.clients': 'people.clients.view',\n    'people.persons': 'people.persons.view',\n    'people.documents': 'people.documents.view',\n    'people.insurances': 'people.insurances.view',\n    'people.insurance-limits': 'people.insurance-limits.view',\n    // Agendamento\n    'scheduler.calendar': 'scheduling.calendar.view',\n    'scheduler.working-hours': 'scheduling.working-hours.view',\n    'scheduler.service-types': 'scheduling.service-types.view',\n    'scheduler.locations': 'scheduling.locations.view',\n    'scheduler.occupancy': 'scheduling.occupancy.view',\n    'scheduler.appointment-requests': 'scheduling.appointment-requests.view',\n    'scheduler.appointments-report': 'scheduling.appointments-report.view',\n    'scheduler.appointments-dashboard': 'scheduling.appointments-dashboard.view'\n};\nconst Sidebar = (param)=>{\n    let { activeModule, activeModuleTitle, isSubmenuActive, handleModuleSubmenuClick, handleBackToModules, isSidebarOpen } = param;\n    var _moduleSubmenus_activeModule;\n    _s();\n    const { can, hasModule, isAdmin } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { schedulingPreferences, isLoading: preferencesLoading } = (0,_hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__.useSchedulingPreferences)();\n    // Inicializar o estado de grupos expandidos a partir do localStorage\n    const [expandedGroups, setExpandedGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Sidebar.useState\": ()=>{\n            // Verificar se estamos no cliente (browser) antes de acessar localStorage\n            if (true) {\n                const savedState = localStorage.getItem('sidebarExpandedGroups');\n                return savedState ? JSON.parse(savedState) : {};\n            }\n            return {};\n        }\n    }[\"Sidebar.useState\"]);\n    // Estado para controlar se a sidebar está minimizada\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Sidebar.useState\": ()=>{\n            if (true) {\n                const savedState = localStorage.getItem('sidebarMinimized');\n                return savedState ? JSON.parse(savedState) : false;\n            }\n            return false;\n        }\n    }[\"Sidebar.useState\"]);\n    // Encontrar o objeto do módulo ativo para acessar seu ícone\n    const activeModuleObject = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.modules.find((m)=>m.id === activeModule);\n    const ModuleIcon = activeModuleObject === null || activeModuleObject === void 0 ? void 0 : activeModuleObject.icon;\n    // Função para verificar se o usuário tem permissão para ver um submenu\n    const hasPermissionForSubmenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenu]\": (moduleId, submenuId)=>{\n            var _moduleSubmenus_moduleId;\n            // Ignorar verificação para grupos, pois a permissão é verificada para cada item\n            if (submenuId === 'cadastro' || submenuId === 'configuracoes' || submenuId === 'gestao' || submenuId === 'convenios' || submenuId === 'financeiro' || submenuId === 'relatorios') {\n                return true;\n            }\n            // Buscar o submenu específico para verificar systemAdminOnly\n            const submenuItem = (_moduleSubmenus_moduleId = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId]) === null || _moduleSubmenus_moduleId === void 0 ? void 0 : _moduleSubmenus_moduleId.find({\n                \"Sidebar.useCallback[hasPermissionForSubmenu]\": (item)=>item.id === submenuId\n            }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"]);\n            // Verificar se o submenu requer SYSTEM_ADMIN\n            if (submenuItem === null || submenuItem === void 0 ? void 0 : submenuItem.systemAdminOnly) {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            // Verificação especial adicional para afiliados - apenas SYSTEM_ADMIN\n            if (submenuId === 'affiliates') {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            // Verificação especial adicional para bug-reports - apenas SYSTEM_ADMIN\n            if (submenuId === 'bug-reports') {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            const permissionKey = \"\".concat(moduleId, \".\").concat(submenuId);\n            const requiredPermission = submenuPermissionsMap[permissionKey];\n            // Se não há mapeamento de permissão, permitir acesso\n            if (!requiredPermission) return true;\n            // Administradores têm acesso a tudo (exceto itens systemAdminOnly)\n            if (isAdmin()) return true;\n            // Verificar permissão específica\n            if (Array.isArray(requiredPermission)) {\n                // Se for um array de permissões, verificar se o usuário tem pelo menos uma delas\n                return requiredPermission.some({\n                    \"Sidebar.useCallback[hasPermissionForSubmenu]\": (perm)=>can(perm)\n                }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"]);\n            } else {\n                // Se for uma única permissão, verificar normalmente\n                return can(requiredPermission);\n            }\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"], [\n        can,\n        isAdmin,\n        user.role\n    ]);\n    // Função para verificar se um submenu deve ser exibido baseado nas preferências\n    const shouldShowSubmenuByPreferences = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[shouldShowSubmenuByPreferences]\": (moduleId, submenuId)=>{\n            // Apenas verificar preferências para o módulo de agendamento\n            if (moduleId !== 'scheduler') return true;\n            // Se as preferências ainda estão carregando, não mostrar os itens que podem ser escondidos\n            if (preferencesLoading) {\n                return false;\n            }\n            // Verificar se as preferências estão carregadas\n            if (!schedulingPreferences) {\n                return false;\n            }\n            switch(submenuId){\n                case 'locations':\n                    const showLocations = schedulingPreferences.showLocations !== false;\n                    return showLocations;\n                case 'service-types':\n                    const showServiceTypes = schedulingPreferences.showServiceTypes !== false;\n                    return showServiceTypes;\n                case 'insurances':\n                    const showInsurance = schedulingPreferences.showInsurance !== false;\n                    return showInsurance;\n                case 'working-hours':\n                    const showWorkingHours = schedulingPreferences.showWorkingHours !== false;\n                    return showWorkingHours;\n                default:\n                    return true;\n            }\n        }\n    }[\"Sidebar.useCallback[shouldShowSubmenuByPreferences]\"], [\n        schedulingPreferences,\n        preferencesLoading\n    ]);\n    // Função para alternar a expansão de um grupo\n    const toggleGroup = (groupId)=>{\n        setExpandedGroups((prev)=>({\n                ...prev,\n                [groupId]: !prev[groupId]\n            }));\n    };\n    // Função para alternar o estado de minimização da sidebar\n    const toggleMinimized = ()=>{\n        setIsMinimized((prev)=>{\n            const newState = !prev;\n            if (true) {\n                localStorage.setItem('sidebarMinimized', JSON.stringify(newState));\n            }\n            return newState;\n        });\n    };\n    // Verificar se algum item dentro de um grupo está ativo\n    const isGroupActive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[isGroupActive]\": (moduleId, groupItems)=>{\n            return groupItems.some({\n                \"Sidebar.useCallback[isGroupActive]\": (item)=>isSubmenuActive(moduleId, item.id)\n            }[\"Sidebar.useCallback[isGroupActive]\"]);\n        }\n    }[\"Sidebar.useCallback[isGroupActive]\"], [\n        isSubmenuActive\n    ]);\n    // Expandir automaticamente grupos que contêm o item ativo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            if (activeModule && _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) {\n                const newExpandedGroups = {\n                    ...expandedGroups\n                };\n                _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule].forEach({\n                    \"Sidebar.useEffect\": (submenu)=>{\n                        if (submenu.type === 'group' && isGroupActive(activeModule, submenu.items)) {\n                            newExpandedGroups[submenu.id] = true;\n                        }\n                    }\n                }[\"Sidebar.useEffect\"]);\n                setExpandedGroups(newExpandedGroups);\n            }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"Sidebar.useEffect\"], [\n        activeModule,\n        pathname,\n        isGroupActive\n    ]);\n    // Salvar estado dos grupos expandidos no localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            if (true) {\n                localStorage.setItem('sidebarExpandedGroups', JSON.stringify(expandedGroups));\n            }\n        }\n    }[\"Sidebar.useEffect\"], [\n        expandedGroups\n    ]);\n    // Verificar se um item de submenu tem permissão para ser exibido\n    const hasPermissionForSubmenuItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (moduleId, submenuId)=>{\n            var _moduleSubmenus_moduleId;\n            // Buscar o item dentro dos grupos do módulo\n            let submenuItem = null;\n            // Primeiro buscar nos itens diretos\n            submenuItem = (_moduleSubmenus_moduleId = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId]) === null || _moduleSubmenus_moduleId === void 0 ? void 0 : _moduleSubmenus_moduleId.find({\n                \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (item)=>item.id === submenuId\n            }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"]);\n            // Se não encontrou, buscar dentro dos grupos\n            if (!submenuItem) {\n                for (const item of _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId] || []){\n                    if (item.type === 'group' && item.items) {\n                        submenuItem = item.items.find({\n                            \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (subItem)=>subItem.id === submenuId\n                        }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"]);\n                        if (submenuItem) break;\n                    }\n                }\n            }\n            // Verificar se o item requer SYSTEM_ADMIN\n            if (submenuItem === null || submenuItem === void 0 ? void 0 : submenuItem.systemAdminOnly) {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            return hasPermissionForSubmenu(moduleId, submenuId);\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"], [\n        hasPermissionForSubmenu,\n        user === null || user === void 0 ? void 0 : user.role\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"\".concat(isMinimized ? 'w-16' : 'w-72', \" bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col \").concat(isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'),\n        \"aria-label\": \"Navega\\xe7\\xe3o lateral\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"flex-1 p-5\",\n                moduleColor: activeModule,\n                children: [\n                    activeModuleObject && !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModuleTitle, {\n                        moduleId: activeModule,\n                        title: activeModuleTitle,\n                        icon: ModuleIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex \".concat(isMinimized ? 'justify-center mb-6' : 'justify-end mb-4'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toggleMinimized,\n                            className: \"p-2 rounded-lg transition-all duration-300 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 \".concat(isMinimized ? 'mt-4' : ''),\n                            \"aria-label\": isMinimized ? \"Expandir sidebar\" : \"Minimizar sidebar\",\n                            title: isMinimized ? \"Expandir sidebar\" : \"Minimizar sidebar\",\n                            children: isMinimized ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 314,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 316,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, undefined),\n                    !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_CompanySelector__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        activeModule: activeModule\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 322,\n                        columnNumber: 26\n                    }, undefined),\n                    preferencesLoading && activeModule === 'scheduler' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-gray-500 dark:text-gray-400\",\n                                children: \"Carregando...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2 flex flex-col \".concat(isMinimized ? 'items-center' : 'justify-center items-center', \" mt-8\"),\n                        \"aria-labelledby\": \"sidebar-heading\",\n                        children: !(preferencesLoading && activeModule === 'scheduler') && activeModule && ((_moduleSubmenus_activeModule = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) === null || _moduleSubmenus_activeModule === void 0 ? void 0 : _moduleSubmenus_activeModule.map((submenu)=>{\n                            // Verificar se é um grupo ou um item individual\n                            if (submenu.type === 'group') {\n                                var // Mostrar ícone do primeiro item do grupo quando minimizado\n                                _submenu_items_;\n                                // Verificar se algum item do grupo tem permissão para ser exibido\n                                const hasAnyPermission = submenu.items.some((item)=>hasPermissionForSubmenuItem(activeModule, item.id));\n                                if (!hasAnyPermission) {\n                                    return null; // Não renderizar o grupo se nenhum item tiver permissão\n                                }\n                                const isGroupExpanded = expandedGroups[submenu.id] || false;\n                                const isAnyItemActive = isGroupActive(activeModule, submenu.items);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>!isMinimized && toggleGroup(submenu.id),\n                                            className: \"\\n                      group \".concat(isMinimized ? 'w-10 h-10 flex items-center justify-center rounded-full mx-auto' : 'w-full flex items-center justify-between px-4 py-2.5 rounded-lg', \" transition-all duration-300\\n                      \").concat(isAnyItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark font-medium\") : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700', \"\\n                    \"),\n                                            \"aria-expanded\": isGroupExpanded,\n                                            title: isMinimized ? submenu.title : undefined,\n                                            children: [\n                                                !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-left\",\n                                                    children: submenu.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 38\n                                                }, undefined),\n                                                isMinimized ? ((_submenu_items_ = submenu.items[0]) === null || _submenu_items_ === void 0 ? void 0 : _submenu_items_.icon) && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(submenu.items[0].icon, {\n                                                    size: 18,\n                                                    'aria-hidden': true\n                                                }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-500 dark:text-gray-400\",\n                                                    children: isGroupExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 27\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 356,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isGroupExpanded && !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4 pl-2 border-l border-gray-200 dark:border-gray-700 space-y-1\",\n                                            children: submenu.items.map((item)=>{\n                                                const isItemActive = isSubmenuActive(activeModule, item.id);\n                                                // Verificar permissão antes de renderizar o item\n                                                if (!hasPermissionForSubmenuItem(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não tiver permissão\n                                                }\n                                                // Verificar se o item deve ser exibido baseado nas preferências\n                                                if (!shouldShowSubmenuByPreferences(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não estiver habilitado nas preferências\n                                                }\n                                                // Verificar se o item está em construção\n                                                const isItemUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.isUnderConstruction)(activeModule, item.id);\n                                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.getConstructionMessage)(activeModule, item.id);\n                                                // Estilo comum para os itens\n                                                const itemClassName = \"\\n                          group w-full flex items-center \".concat(isMinimized ? 'justify-center px-2 py-2' : 'gap-3 px-3 py-2', \" rounded-lg transition-all duration-300\\n                          \").concat(isItemActive ? \"rounded-xl border border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                               bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                               shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n                        \");\n                                                // Se estiver em construção, usar o ConstructionButton\n                                                if (isItemUnderConstruction) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_9__.ConstructionButton, {\n                                                        className: itemClassName,\n                                                        \"aria-current\": isItemActive ? 'page' : undefined,\n                                                        title: isMinimized ? item.title : constructionMessage.title,\n                                                        content: constructionMessage.content,\n                                                        icon: constructionMessage.icon,\n                                                        position: \"right\",\n                                                        children: [\n                                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\\n                                  \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                                \"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                    size: 18,\n                                                                    \"aria-hidden\": \"true\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 35\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 33\n                                                            }, undefined),\n                                                            !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                                children: item.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 48\n                                                            }, undefined)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 29\n                                                    }, undefined);\n                                                }\n                                                // Se não estiver em construção, usar o botão normal\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleModuleSubmenuClick(activeModule, item.id),\n                                                    className: itemClassName,\n                                                    \"aria-current\": isItemActive ? 'page' : undefined,\n                                                    title: isMinimized ? item.title : undefined,\n                                                    children: [\n                                                        item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"\\n                                \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                              \"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                size: 18,\n                                                                \"aria-hidden\": \"true\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 31\n                                                        }, undefined),\n                                                        !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 46\n                                                        }, undefined)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 27\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 385,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 354,\n                                    columnNumber: 17\n                                }, undefined);\n                            } else {\n                                // Renderização de itens individuais (não agrupados)\n                                const isActive = isSubmenuActive(activeModule, submenu.id);\n                                // Verificar permissão antes de renderizar o item\n                                const hasPermission = hasPermissionForSubmenu(activeModule, submenu.id);\n                                if (!hasPermission) {\n                                    return null; // Não renderizar se não tiver permissão\n                                }\n                                // Verificar se o submenu deve ser exibido baseado nas preferências\n                                const shouldShowByPreferences = shouldShowSubmenuByPreferences(activeModule, submenu.id);\n                                if (!shouldShowByPreferences) {\n                                    return null; // Não renderizar se não estiver habilitado nas preferências\n                                }\n                                // Verificar se o submenu está em construção\n                                const isSubmenuUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.isUnderConstruction)(activeModule, submenu.id);\n                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.getConstructionMessage)(activeModule, submenu.id);\n                                // Estilo comum para ambos os tipos de botões\n                                const buttonClassName = \"\\n                group w-full flex items-center \".concat(isMinimized ? 'justify-center px-2 py-3' : 'gap-3 px-4 py-3', \" rounded-lg transition-all duration-300\\n                \").concat(isActive ? \"rounded-xl border border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                     bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                     shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n              \");\n                                // Se estiver em construção, usar o ConstructionButton\n                                if (isSubmenuUnderConstruction) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_9__.ConstructionButton, {\n                                        className: buttonClassName,\n                                        \"aria-current\": isActive ? 'page' : undefined,\n                                        title: isMinimized ? submenu.title : constructionMessage.title,\n                                        content: constructionMessage.content,\n                                        icon: constructionMessage.icon,\n                                        position: \"right\",\n                                        children: [\n                                            submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                    size: 20,\n                                                    \"aria-hidden\": \"true\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 520,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                                children: submenu.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 532,\n                                                columnNumber: 38\n                                            }, undefined)\n                                        ]\n                                    }, submenu.id, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                        lineNumber: 510,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }\n                                // Se não estiver em construção, usar o botão normal\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleModuleSubmenuClick(activeModule, submenu.id),\n                                    className: buttonClassName,\n                                    \"aria-current\": isActive ? 'page' : undefined,\n                                    title: isMinimized ? submenu.title : undefined,\n                                    children: [\n                                        submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                size: 20,\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 553,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 547,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                            children: submenu.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 559,\n                                            columnNumber: 36\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 539,\n                                    columnNumber: 17\n                                }, undefined);\n                            }\n                        }))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(isMinimized ? 'p-2' : 'p-5', \" border-t border-gray-100 dark:border-gray-700 mt-auto\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToModules,\n                    className: \"\\n            group w-full py-3 \".concat(isMinimized ? 'px-2 justify-center' : 'px-4 gap-3', \" rounded-lg flex items-center\\n            border-2 border-module-\").concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n            bg-transparent dark:bg-gray-800 hover:bg-module-\").concat(activeModule, \"-bg/10 dark:hover:bg-module-\").concat(activeModule, \"-bg-dark/10\\n            transition-all duration-300\\n          \"),\n                    \"aria-label\": \"Voltar para o dashboard principal\",\n                    title: isMinimized ? \"Voltar a Tela Inicial\" : undefined,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            flex items-center justify-center w-8 h-8 rounded-full\\n            bg-module-\".concat(activeModule, \"-bg dark:bg-module-\").concat(activeModule, \"-bg-dark/70\\n            text-module-\").concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\\n            shadow-sm dark:shadow-md dark:shadow-black/20\\n            group-hover:scale-110 transition-transform duration-200\\n          \"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 587,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 580,\n                            columnNumber: 11\n                        }, undefined),\n                        !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-gray-800 dark:text-gray-200\",\n                            children: \"Voltar a Tela Inicial\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 589,\n                            columnNumber: 28\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 569,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 568,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Sidebar, \"wNosui995CZ6nPqQw2gDp/n7fSs=\", false, function() {\n    return [\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__.useSchedulingPreferences\n    ];\n});\n_c1 = Sidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\nvar _c, _c1;\n$RefreshReg$(_c, \"ModuleTitle\");\n$RefreshReg$(_c1, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar/index.js\n"));

/***/ })

});