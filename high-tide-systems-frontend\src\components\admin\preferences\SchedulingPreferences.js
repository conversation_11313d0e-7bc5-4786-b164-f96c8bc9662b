import { useState, useRef, useEffect } from "react";
import { Card } from "../../ui/Card";
import Button from "../../ui/Button";
import ModuleCheckbox from "../../ui/ModuleCheckbox";
import ModuleInput from "../../ui/ModuleInput";
import PreferencesSection from "./PreferencesSection";
import { preferencesService } from "@/services/preferencesService";
import { useToast } from "@/contexts/ToastContext";

const CAMPOS = [
  { key: "description", label: "Descrição" },
  { key: "insurance", label: "Convênio" },
  { key: "location", label: "Local" },
  { key: "notes", label: "Observações" },
  { key: "professional", label: "Profissional" },
  { key: "serviceType", label: "Tipos de Serviço" },
  { key: "specialty", label: "Especialidade" },
  { key: "title", label: "<PERSON><PERSON><PERSON><PERSON> do Agendamento" },
];

const WEEK_DAYS = [
  { value: 0, label: 'Domingo' },
  { value: 1, label: 'Segunda' },
  { value: 2, label: '<PERSON>r<PERSON>' },
  { value: 3, label: 'Quarta' },
  { value: 4, label: 'Quinta' },
  { value: 5, label: 'Sexta' },
  { value: 6, label: 'Sábado' },
];

const HOURS_24 = Array.from({ length: 24 }, (_, i) => i);

export default function SchedulingPreferences({ 
  search = "", 
  searchMode = false, 
  preferences = null,
  selectedCompanyId = null,
  onSave = null 
}) {
  // Estados para preferências de agendamento
  const [allowPastScheduling, setAllowPastScheduling] = useState(false);
  const [startHour, setStartHour] = useState("08:00");
  const [endHour, setEndHour] = useState("18:00");
  const [requiredFields, setRequiredFields] = useState({
    insurance: true,
    specialty: true,
    serviceType: true,
    location: true,
    professional: true,
    notes: false,
    title: false,
    description: false,
  });
  const [allowMultipleServices, setAllowMultipleServices] = useState(false);
  const [requireClientConfirmation, setRequireClientConfirmation] = useState(false);
  const [selectedWeekDays, setSelectedWeekDays] = useState([1,2,3,4,5]); // seg a sex
  const [requireRecurrence, setRequireRecurrence] = useState(true);
  const [requireSequential, setRequireSequential] = useState(true);
  const [allowedHours, setAllowedHours] = useState(Array.from({ length: 24 }, (_, i) => i >= 8 && i <= 18)); // 08h-18h
  const [isDragging, setIsDragging] = useState(false);
  const [dragValue, setDragValue] = useState(null);
  const dragRef = useRef();
  const [isDraggingDay, setIsDraggingDay] = useState(false);
  const [dragDayValue, setDragDayValue] = useState(null);
  const [requireServiceValue, setRequireServiceValue] = useState(false);
  const [requireLocationAddress, setRequireLocationAddress] = useState(false);
  const [requireLocationPhone, setRequireLocationPhone] = useState(false);
  const [showWorkingHours, setShowWorkingHours] = useState(true);
  const [showServiceTypes, setShowServiceTypes] = useState(true);
  const [showLocations, setShowLocations] = useState(true);
  const [showInsurance, setShowInsurance] = useState(true);
  const [showInsuranceLimit, setShowInsuranceLimit] = useState(true);
  
  // Estados para configurações de localização
  const [useLocationAvailability, setUseLocationAvailability] = useState(false);
  const [locationSelectedWeekDays, setLocationSelectedWeekDays] = useState([1,2,3,4,5]);

  const { toast_success, toast_error } = useToast();
  const [isSaving, setIsSaving] = useState(false);

  // Carregar preferências quando o componente receber os dados
  useEffect(() => {
    if (preferences && preferences.scheduling) {
      const schedulingPrefs = preferences.scheduling;
      
      setAllowPastScheduling(schedulingPrefs.allowPastScheduling ?? false);
      setStartHour(schedulingPrefs.startHour ?? "08:00");
      setEndHour(schedulingPrefs.endHour ?? "18:00");
      setRequiredFields({
        insurance: schedulingPrefs.requiredFields?.insurance ?? true,
        specialty: schedulingPrefs.requiredFields?.specialty ?? true,
        serviceType: schedulingPrefs.requiredFields?.serviceType ?? true,
        location: schedulingPrefs.requiredFields?.location ?? true,
        professional: schedulingPrefs.requiredFields?.professional ?? true,
        notes: schedulingPrefs.requiredFields?.notes ?? false,
        title: schedulingPrefs.requiredFields?.title ?? false,
        description: schedulingPrefs.requiredFields?.description ?? false,
      });
      setAllowMultipleServices(schedulingPrefs.allowMultipleServices ?? false);
      setRequireClientConfirmation(schedulingPrefs.requireClientConfirmation ?? false);
      setSelectedWeekDays(schedulingPrefs.selectedWeekDays ?? [1,2,3,4,5]);
      setRequireRecurrence(schedulingPrefs.requireRecurrence ?? true);
      setRequireSequential(schedulingPrefs.requireSequential ?? true);
      setAllowedHours(schedulingPrefs.allowedHours ?? Array.from({ length: 24 }, (_, i) => i >= 8 && i <= 18));
      setRequireServiceValue(schedulingPrefs.requireServiceValue ?? false);
      setRequireLocationAddress(schedulingPrefs.requireLocationAddress ?? false);
      setRequireLocationPhone(schedulingPrefs.requireLocationPhone ?? false);
      setShowWorkingHours(schedulingPrefs.showWorkingHours ?? true);
      setShowServiceTypes(schedulingPrefs.showServiceTypes ?? true);
      setShowLocations(schedulingPrefs.showLocations ?? true);
      setShowInsurance(schedulingPrefs.showInsurance ?? true);
      setShowInsuranceLimit(schedulingPrefs.showInsuranceLimit ?? true);
    }
    
    // Carregar configurações de localização
    if (preferences && preferences.locations) {
      const locationPrefs = preferences.locations;
      setUseLocationAvailability(locationPrefs.useAvailability ?? false);
      setLocationSelectedWeekDays(locationPrefs.selectedWeekDays ?? [1,2,3,4,5]);
    }
  }, [preferences]);

  useEffect(() => {
    if (showLocations) {
      setRequiredFields(prev => ({ ...prev, location: true }));
    } else {
      setRequiredFields(prev => ({ ...prev, location: false }));
    }
  }, [showLocations]);

  useEffect(() => {
    if (showInsurance) {
      setRequiredFields(prev => ({ ...prev, insurance: true }));
    } else {
      setRequiredFields(prev => ({ ...prev, insurance: false }));
    }
  }, [showInsurance]);

  useEffect(() => {
    if (showServiceTypes) {
      setRequiredFields(prev => ({ ...prev, serviceType: true }));
    } else {
      setRequiredFields(prev => ({ ...prev, serviceType: false }));
    }
  }, [showServiceTypes]);

  function handleFieldChange(key) {
    // Impedir desmarcação de campos obrigatórios baseados em configurações de visibilidade
    if (key === 'location' && showLocations && requiredFields[key]) {
      return; // Não permite desmarcar location se showLocations for true
    }
    if (key === 'insurance' && showInsurance && requiredFields[key]) {
      return; // Não permite desmarcar insurance se showInsurance for true
    }
    if (key === 'serviceType' && showServiceTypes && requiredFields[key]) {
      return; // Não permite desmarcar serviceType se showServiceTypes for true
    }
    
    setRequiredFields(fields => ({ ...fields, [key]: !fields[key] }));
  }

  function toggleWeekDay(day) {
    setSelectedWeekDays((prev) =>
      prev.includes(day)
        ? prev.filter((d) => d !== day)
        : [...prev, day].sort((a, b) => a - b)
    );
  }

  function toggleHour(hour, value = null) {
    setAllowedHours(prev => prev.map((v, idx) => idx === hour ? (value !== null ? value : !v) : v));
  }

  function handleMouseDown(hour) {
    setIsDragging(true);
    setDragValue(!allowedHours[hour]);
    toggleHour(hour);
  }

  function handleMouseOver(hour) {
    if (isDragging) {
      toggleHour(hour, dragValue);
    }
  }

  function handleMouseUp() {
    setIsDragging(false);
    setDragValue(null);
  }

  function handleDayMouseDown(day) {
    setIsDraggingDay(true);
    setDragDayValue(!selectedWeekDays.includes(day));
    toggleWeekDay(day);
  }

  function handleDayMouseOver(day) {
    if (isDraggingDay) {
      setSelectedWeekDays(prev =>
        dragDayValue
          ? [...new Set([...prev, day])].sort((a, b) => a - b)
          : prev.filter(d => d !== day)
      );
    }
  }

  function handleDayMouseUp() {
    setIsDraggingDay(false);
    setDragDayValue(null);
  }

  async function handleSave() {
    setIsSaving(true);
    
    // Validações básicas
    if (selectedWeekDays.length === 0) {
      toast_error("Selecione pelo menos um dia da semana.");
      setIsSaving(false);
      return;
    }
    
    if (!allowedHours.some(hour => hour)) {
      toast_error("Selecione pelo menos um horário permitido.");
      setIsSaving(false);
      return;
    }
    
    const schedulingPreferences = {
      allowPastScheduling,
      startHour,
      endHour,
      requiredFields,
      allowMultipleServices,
      requireClientConfirmation,
      selectedWeekDays,
      requireRecurrence,
      requireSequential,
      allowedHours,
      requireServiceValue,
      requireLocationAddress,
      requireLocationPhone,
      showWorkingHours,
      showServiceTypes,
      showLocations,
      showInsurance,
      showInsuranceLimit,
    };
    
    const locationPreferences = {
      useAvailability: useLocationAvailability,
      selectedWeekDays: locationSelectedWeekDays
    };

    try {
      // Manter as preferências existentes e adicionar as de agendamento
      const currentPreferences = preferences || {};
      const updatedPreferences = {
        ...currentPreferences,
        scheduling: schedulingPreferences,
        locations: locationPreferences
      };
      
      if (onSave) {
        // Usar função de salvamento fornecida pelo pai (suporta empresas específicas)
        const success = await onSave(updatedPreferences);
        if (!success) {
          // Se o onSave retornar false, não continuar
          return;
        }
      } else {
        // Fallback para o serviço tradicional
        await preferencesService.save(updatedPreferences);
        // Usar setTimeout para evitar conflitos
        setTimeout(() => {
          toast_success("Preferências de agendamento salvas com sucesso!");
        }, 100);
      }
    } catch (err) {
      console.error('Erro ao salvar preferências de agendamento:', err);
      toast_error("Erro ao salvar preferências de agendamento.");
    } finally {
      setIsSaving(false);
    }
  }

  // Função para filtrar campos/seções pelo termo de busca
  const filter = (label) => label.toLowerCase().includes(search.toLowerCase());

  // Filtragem de campos obrigatórios
  const camposFiltrados = CAMPOS.filter(campo => {
    if (campo.key === 'location' && !showLocations) return false;
    if (campo.key === 'insurance' && !showInsurance) return false;
    if (campo.key === 'serviceType' && !showServiceTypes) return false;
    return filter(campo.label);
  }).sort((a, b) => a.label.localeCompare(b.label));
  const showCampos = !searchMode || camposFiltrados.length > 0;
  const showPermissoes = !searchMode || filter("agendamento") || filter("retroativo") || filter("permissão");
  const showHorarios = !searchMode || filter("horário") || filter("expediente") || filter("início") || filter("fim");
  const showOutras = !searchMode || filter("múltiplos serviços") || filter("confirmação do cliente") || filter("serviços") || filter("confirmação");

  const multiServMatch = filter("múltiplos serviços") || filter("serviços");
  const clientConfMatch = filter("confirmação do cliente") || filter("confirmação");
  const retroativoMatch = filter("retroativo") || filter("agendamento");
  const inicioMatch = filter("início") || filter("expediente") || filter("horário");
  const fimMatch = filter("fim") || filter("expediente") || filter("horário");

  if (searchMode && !showCampos && !showPermissoes && !showHorarios && !showOutras) {
    return null;
  }

  return (
    <div className="space-y-8">
      {showPermissoes && (
        <Card className="p-6 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <h4 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4">Preferências de Agendamento</h4>
          {/* Dias da Semana */}
          <div className="mb-6">
            <span className="block text-sm text-neutral-600 dark:text-neutral-300 mb-3">Selecione os dias da semana que devem aparecer no calendário:</span>
            <div
              className="flex gap-3 flex-wrap"
              onMouseLeave={handleDayMouseUp}
              onMouseUp={handleDayMouseUp}
            >
              {WEEK_DAYS.map((day) => (
                <button
                  key={day.value}
                  type="button"
                  onMouseDown={() => handleDayMouseDown(day.value)}
                  onMouseOver={() => handleDayMouseOver(day.value)}
                  onMouseUp={handleDayMouseUp}
                  className={`w-14 h-14 rounded-xl flex items-center justify-center font-semibold text-sm transition-all duration-200 border-2 shadow-sm
                    ${selectedWeekDays.includes(day.value)
                      ? 'bg-gradient-to-br from-blue-500 to-blue-600 border-blue-400 text-white shadow-lg shadow-blue-500/25'
                      : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:border-gray-300 dark:hover:border-gray-500'}
                    hover:scale-105 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 dark:focus:ring-offset-gray-900`}
                  style={{ userSelect: 'none' }}
                >
                  {day.label.slice(0, 3)}
                </button>
              ))}
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {(!searchMode || retroativoMatch) && (
              <ModuleCheckbox
                checked={allowPastScheduling}
                onChange={() => setAllowPastScheduling(v => !v)}
                label="Permitir agendamento retroativo (datas passadas)"
                moduleColor="admin"
              />
            )}
          </div>
        </Card>
      )}
      {showHorarios && (
        <Card className="p-6 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <h4 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4">Horários Permitidos</h4>
          <span className="block text-sm text-neutral-600 dark:text-neutral-300 mb-3">Selecione os horários do dia que devem aparecer no calendário:</span>
          <div
            className="flex flex-wrap gap-2 mb-6 select-none"
            onMouseLeave={handleMouseUp}
            onMouseUp={handleMouseUp}
          >
            {HOURS_24.map(hour => (
              <button
                key={hour}
                type="button"
                onMouseDown={() => handleMouseDown(hour)}
                onMouseOver={() => handleMouseOver(hour)}
                onMouseUp={handleMouseUp}
                className={`w-12 h-12 rounded-lg flex items-center justify-center font-semibold text-xs transition-all duration-200 border-2 shadow-sm
                  ${allowedHours[hour]
                    ? 'bg-gradient-to-br from-green-500 to-green-600 border-green-400 text-white shadow-lg shadow-green-500/25'
                    : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:border-gray-300 dark:hover:border-gray-500'}
                  hover:scale-105 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-green-500/50 focus:ring-offset-2 dark:focus:ring-offset-gray-900`}
                style={{ userSelect: 'none' }}
              >
                {String(hour).padStart(2, '0')}:00
              </button>
            ))}
          </div>
        </Card>
      )}
      <Card className="p-6 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
        <h4 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4">Remover Utilidade</h4>
        <p className="text-sm text-neutral-600 dark:text-neutral-300 mb-6">Caso sua empresa não queira utilizar nossas seguintes páginas de controle, você tem a opção de remove-lá.</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <ModuleCheckbox
            checked={showWorkingHours}
            onChange={() => setShowWorkingHours(v => !v)}
            label="Horários de Trabalho (desmarcar o campo significa retirar as restrições de horários dos profissionais, podendo agendar consultas a eles em qualquer horário)"
            moduleColor="admin"
          />
          <ModuleCheckbox
            checked={showServiceTypes}
            onChange={() => setShowServiceTypes(v => !v)}
            label="Tipos de Serviço (desmarcar o campo significa não registrar qual serviço foi feito naquele agendamento)"
            moduleColor="admin"
          />
          <ModuleCheckbox
            checked={showLocations}
            onChange={() => setShowLocations(v => !v)}
            label="Localizações (desmarcar o campo significa não registrar qual local onde aquela consulta ocorreu)"
            moduleColor="admin"
          />
          <div>
            <ModuleCheckbox
              checked={showInsurance}
              onChange={() => setShowInsurance(v => !v)}
              label="Convênio (desmarcar o campo significa não registrar o convênio que autorizou a consulta)"
              moduleColor="admin"
            />
            {showInsurance && (
              <div className="ml-8 mt-2">
                <ModuleCheckbox
                  checked={showInsuranceLimit}
                  onChange={() => setShowInsuranceLimit(v => !v)}
                  label="Limite de Convênio (desmarcar o campo significa não ter limite de consultas para o paciente)"
                  moduleColor="admin"
                />
              </div>
            )}
          </div>
        </div>
      </Card>
      {showCampos && (
        <Card className="p-6 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <h4 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4">Obrigatoriedade de Campos no Agendamento</h4>
          <p className="text-sm text-neutral-600 dark:text-neutral-300 mb-6">Ao marcar uma opção abaixo, o campo se tornará <span className="font-semibold text-gray-600 dark:text-gray-400">obrigatório</span> para o agendamento. Os campos <b>Paciente</b>, <b>Data</b> e <b>Hora Inicial e Final</b> são sempre obrigatórios.</p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {camposFiltrados.map(campo => {
              const isForced = (campo.key === 'location' && showLocations) || 
                              (campo.key === 'insurance' && showInsurance) || 
                              (campo.key === 'serviceType' && showServiceTypes);
              return (
                <ModuleCheckbox
                  key={campo.key}
                  checked={requiredFields[campo.key]}
                  onChange={() => handleFieldChange(campo.key)}
                  label={`${campo.label}${isForced ? ' (obrigatório)' : ''}`}
                  moduleColor="admin"
                  disabled={isForced}
                />
              );
            })}
            <ModuleCheckbox
              checked={requireRecurrence}
              onChange={() => setRequireRecurrence(v => !v)}
              label="Recorrência de Agendamento"
              moduleColor="admin"
            />
            <ModuleCheckbox
              checked={requireSequential}
              onChange={() => setRequireSequential(v => !v)}
              label="Agendamento Sequencial"
              moduleColor="admin"
            />
          </div>
        </Card>
      )}
      {showOutras && (
        <Card className="p-6 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <h4 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4">Outras Preferências</h4>
          <p className="text-sm text-neutral-600 dark:text-neutral-300 mb-6">O <b>Nome do serviço</b> e o <b>Nome da localização</b> são sempre obrigatórios.</p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[
              {
                key: 'allowMultipleServices',
                checked: allowMultipleServices,
                onChange: () => setAllowMultipleServices(v => !v),
                label: 'Permitir agendamento de múltiplos serviços por vez',
              },
              {
                key: 'requireClientConfirmation',
                checked: requireClientConfirmation,
                onChange: () => setRequireClientConfirmation(v => !v),
                label: 'Exigir confirmação do cliente para agendamento',
              },
              {
                key: 'requireServiceValue',
                checked: requireServiceValue,
                onChange: () => setRequireServiceValue(v => !v),
                label: 'Valor do tipo de serviço é obrigatório',
              },
              {
                key: 'requireLocationAddress',
                checked: requireLocationAddress,
                onChange: () => setRequireLocationAddress(v => !v),
                label: 'Endereço da localização é obrigatório',
              },
              {
                key: 'requireLocationPhone',
                checked: requireLocationPhone,
                onChange: () => setRequireLocationPhone(v => !v),
                label: 'Telefone da localização é obrigatório',
              },
            ].sort((a, b) => a.label.localeCompare(b.label)).map(opt => (
              <ModuleCheckbox
                key={opt.key}
                checked={opt.checked}
                onChange={opt.onChange}
                label={opt.label}
                moduleColor="admin"
              />
            ))}
          </div>
        </Card>
      )}
      
      {/* Configurações de Localização */}
      <Card className="p-6 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
        <h4 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4">Configurações de Localização</h4>
        <p className="text-sm text-neutral-600 dark:text-neutral-300 mb-6">Configure como o sistema deve lidar com a disponibilidade das localizações.</p>
        
        <div className="space-y-6">
          <ModuleCheckbox
            checked={useLocationAvailability}
            onChange={() => setUseLocationAvailability(v => !v)}
            label="Usar sistema de disponibilidade de localizações"
            moduleColor="admin"
          />
          
          {useLocationAvailability && (
            <div className="ml-6 space-y-4">
              <div>
                <span className="block text-sm text-neutral-600 dark:text-neutral-300 mb-3">Dias da semana padrão para novas localizações:</span>
                <div className="flex gap-2 flex-wrap">
                  {WEEK_DAYS.map((day) => (
                    <button
                      key={day.value}
                      type="button"
                      onClick={() => {
                        setLocationSelectedWeekDays(prev =>
                          prev.includes(day.value)
                            ? prev.filter(d => d !== day.value)
                            : [...prev, day.value].sort((a, b) => a - b)
                        );
                      }}
                      className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                        locationSelectedWeekDays.includes(day.value)
                          ? 'bg-purple-500 text-white'
                          : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      {day.label}
                    </button>
                  ))}
                </div>
              </div>
              
              <div className="text-sm text-gray-500 dark:text-gray-400">
                <p>Quando ativado:</p>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Cada localização poderá ter horários de disponibilidade configurados</li>
                  <li>Agendamentos só poderão ser feitos nos horários disponíveis</li>
                  <li>Os dias selecionados acima serão o padrão para novas localizações</li>
                </ul>
              </div>
            </div>
          )}
        </div>
      </Card>
      
      <div className="flex justify-end mt-8">
        <button
          className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg"
          onClick={handleSave}
          disabled={isSaving}
        >
          <span>Salvar Preferências</span>
          <span className="text-lg">✔</span>
        </button>
      </div>
    </div>
  );
} 