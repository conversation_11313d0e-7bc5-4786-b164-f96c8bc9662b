"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar/index.js":
/*!***************************************************!*\
  !*** ./src/components/dashboard/Sidebar/index.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,PanelLeftClose,PanelLeftOpen,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panel-left-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,PanelLeftClose,PanelLeftOpen,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panel-left-close.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,PanelLeftClose,PanelLeftOpen,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,PanelLeftClose,PanelLeftOpen,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,PanelLeftClose,PanelLeftOpen,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/dashboard/components */ \"(app-pages-browser)/./src/app/dashboard/components.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useSchedulingPreferences */ \"(app-pages-browser)/./src/hooks/useSchedulingPreferences.js\");\n/* harmony import */ var _components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/CustomScrollArea */ \"(app-pages-browser)/./src/components/ui/CustomScrollArea.js\");\n/* harmony import */ var _hooks_useConstructionMessage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useConstructionMessage */ \"(app-pages-browser)/./src/hooks/useConstructionMessage.js\");\n/* harmony import */ var _components_construction__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/construction */ \"(app-pages-browser)/./src/components/construction/index.js\");\n/* harmony import */ var _utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/constructionUtils */ \"(app-pages-browser)/./src/utils/constructionUtils.js\");\n/* harmony import */ var _components_dashboard_CompanySelector__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/CompanySelector */ \"(app-pages-browser)/./src/components/dashboard/CompanySelector.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ModuleTitle = (param)=>{\n    let { moduleId, title, icon: Icon } = param;\n    // Exibir o logo no lugar do título do módulo, como botão para /dashboard\n    const handleLogoClick = ()=>{\n        if (true) {\n            window.location.href = '/dashboard';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6 flex justify-center items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleLogoClick,\n            style: {\n                background: 'none',\n                border: 'none',\n                padding: 0\n            },\n            className: \"focus:outline-none cursor-pointer\",\n            \"aria-label\": \"Ir para o dashboard\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/logo_horizontal_sem_fundo.png\",\n                    alt: \"High Tide Logo\",\n                    className: \"h-12 dark:invert dark:text-white transition-all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ModuleTitle;\n// Mapeamento de submenus para permissões\nconst submenuPermissionsMap = {\n    // Admin\n    'admin.introduction': 'admin.dashboard.view',\n    'admin.dashboard': 'admin.dashboard.view',\n    'admin.users': 'admin.users.view',\n    'admin.professions': [\n        'admin.professions.view',\n        'admin.profession-groups.view'\n    ],\n    'admin.bug-reports': 'admin.dashboard.view',\n    'admin.plans': 'admin.dashboard.view',\n    'admin.logs': 'admin.logs.view',\n    'admin.settings': 'admin.config.edit',\n    'admin.backup': 'admin.config.edit',\n    'admin.affiliates': 'admin.dashboard.view',\n    // ABA+\n    'abaplus.anamnese': 'abaplus.anamnese.view',\n    'abaplus.evolucoes-diarias': 'abaplus.evolucoes-diarias.view',\n    'abaplus.sessao': 'abaplus.sessao.view',\n    // Financeiro\n    'financial.invoices': 'financial.invoices.view',\n    'financial.payments': 'financial.payments.view',\n    'financial.expenses': 'financial.expenses.view',\n    'financial.reports': 'financial.reports.view',\n    'financial.cashflow': 'financial.reports.view',\n    // RH\n    'hr.employees': 'rh.employees.view',\n    'hr.payroll': 'rh.payroll.view',\n    'hr.documents': 'rh.employees.view',\n    'hr.departments': 'rh.employees.view',\n    'hr.attendance': 'rh.attendance.view',\n    'hr.benefits': 'rh.benefits.view',\n    'hr.training': 'rh.employees.view',\n    // Pessoas\n    'people.clients': 'people.clients.view',\n    'people.persons': 'people.persons.view',\n    'people.documents': 'people.documents.view',\n    'people.insurances': 'people.insurances.view',\n    'people.insurance-limits': 'people.insurance-limits.view',\n    // Agendamento\n    'scheduler.calendar': 'scheduling.calendar.view',\n    'scheduler.working-hours': 'scheduling.working-hours.view',\n    'scheduler.service-types': 'scheduling.service-types.view',\n    'scheduler.locations': 'scheduling.locations.view',\n    'scheduler.occupancy': 'scheduling.occupancy.view',\n    'scheduler.appointment-requests': 'scheduling.appointment-requests.view',\n    'scheduler.appointments-report': 'scheduling.appointments-report.view',\n    'scheduler.appointments-dashboard': 'scheduling.appointments-dashboard.view'\n};\nconst Sidebar = (param)=>{\n    let { activeModule, activeModuleTitle, isSubmenuActive, handleModuleSubmenuClick, handleBackToModules, isSidebarOpen } = param;\n    var _moduleSubmenus_activeModule;\n    _s();\n    const { can, hasModule, isAdmin } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { schedulingPreferences, isLoading: preferencesLoading } = (0,_hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__.useSchedulingPreferences)();\n    // Inicializar o estado de grupos expandidos a partir do localStorage\n    const [expandedGroups, setExpandedGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Sidebar.useState\": ()=>{\n            // Verificar se estamos no cliente (browser) antes de acessar localStorage\n            if (true) {\n                const savedState = localStorage.getItem('sidebarExpandedGroups');\n                return savedState ? JSON.parse(savedState) : {};\n            }\n            return {};\n        }\n    }[\"Sidebar.useState\"]);\n    // Estado para controlar se a sidebar está minimizada\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Sidebar.useState\": ()=>{\n            if (true) {\n                const savedState = localStorage.getItem('sidebarMinimized');\n                return savedState ? JSON.parse(savedState) : false;\n            }\n            return false;\n        }\n    }[\"Sidebar.useState\"]);\n    // Encontrar o objeto do módulo ativo para acessar seu ícone\n    const activeModuleObject = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.modules.find((m)=>m.id === activeModule);\n    const ModuleIcon = activeModuleObject === null || activeModuleObject === void 0 ? void 0 : activeModuleObject.icon;\n    // Função para verificar se o usuário tem permissão para ver um submenu\n    const hasPermissionForSubmenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenu]\": (moduleId, submenuId)=>{\n            var _moduleSubmenus_moduleId;\n            // Ignorar verificação para grupos, pois a permissão é verificada para cada item\n            if (submenuId === 'cadastro' || submenuId === 'configuracoes' || submenuId === 'gestao' || submenuId === 'convenios' || submenuId === 'financeiro' || submenuId === 'relatorios') {\n                return true;\n            }\n            // Buscar o submenu específico para verificar systemAdminOnly\n            const submenuItem = (_moduleSubmenus_moduleId = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId]) === null || _moduleSubmenus_moduleId === void 0 ? void 0 : _moduleSubmenus_moduleId.find({\n                \"Sidebar.useCallback[hasPermissionForSubmenu]\": (item)=>item.id === submenuId\n            }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"]);\n            // Verificar se o submenu requer SYSTEM_ADMIN\n            if (submenuItem === null || submenuItem === void 0 ? void 0 : submenuItem.systemAdminOnly) {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            // Verificação especial adicional para afiliados - apenas SYSTEM_ADMIN\n            if (submenuId === 'affiliates') {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            // Verificação especial adicional para bug-reports - apenas SYSTEM_ADMIN\n            if (submenuId === 'bug-reports') {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            const permissionKey = \"\".concat(moduleId, \".\").concat(submenuId);\n            const requiredPermission = submenuPermissionsMap[permissionKey];\n            // Se não há mapeamento de permissão, permitir acesso\n            if (!requiredPermission) return true;\n            // Administradores têm acesso a tudo (exceto itens systemAdminOnly)\n            if (isAdmin()) return true;\n            // Verificar permissão específica\n            if (Array.isArray(requiredPermission)) {\n                // Se for um array de permissões, verificar se o usuário tem pelo menos uma delas\n                return requiredPermission.some({\n                    \"Sidebar.useCallback[hasPermissionForSubmenu]\": (perm)=>can(perm)\n                }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"]);\n            } else {\n                // Se for uma única permissão, verificar normalmente\n                return can(requiredPermission);\n            }\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"], [\n        can,\n        isAdmin,\n        user.role\n    ]);\n    // Função para verificar se um submenu deve ser exibido baseado nas preferências\n    const shouldShowSubmenuByPreferences = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[shouldShowSubmenuByPreferences]\": (moduleId, submenuId)=>{\n            // Apenas verificar preferências para o módulo de agendamento\n            if (moduleId !== 'scheduler') return true;\n            // Se as preferências ainda estão carregando, não mostrar os itens que podem ser escondidos\n            if (preferencesLoading) {\n                return false;\n            }\n            // Verificar se as preferências estão carregadas\n            if (!schedulingPreferences) {\n                return false;\n            }\n            switch(submenuId){\n                case 'locations':\n                    const showLocations = schedulingPreferences.showLocations !== false;\n                    return showLocations;\n                case 'service-types':\n                    const showServiceTypes = schedulingPreferences.showServiceTypes !== false;\n                    return showServiceTypes;\n                case 'insurances':\n                    const showInsurance = schedulingPreferences.showInsurance !== false;\n                    return showInsurance;\n                case 'working-hours':\n                    const showWorkingHours = schedulingPreferences.showWorkingHours !== false;\n                    return showWorkingHours;\n                default:\n                    return true;\n            }\n        }\n    }[\"Sidebar.useCallback[shouldShowSubmenuByPreferences]\"], [\n        schedulingPreferences,\n        preferencesLoading\n    ]);\n    // Função para alternar a expansão de um grupo\n    const toggleGroup = (groupId)=>{\n        setExpandedGroups((prev)=>({\n                ...prev,\n                [groupId]: !prev[groupId]\n            }));\n    };\n    // Função para alternar o estado de minimização da sidebar\n    const toggleMinimized = ()=>{\n        setIsMinimized((prev)=>{\n            const newState = !prev;\n            if (true) {\n                localStorage.setItem('sidebarMinimized', JSON.stringify(newState));\n            }\n            return newState;\n        });\n    };\n    // Verificar se algum item dentro de um grupo está ativo\n    const isGroupActive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[isGroupActive]\": (moduleId, groupItems)=>{\n            return groupItems.some({\n                \"Sidebar.useCallback[isGroupActive]\": (item)=>isSubmenuActive(moduleId, item.id)\n            }[\"Sidebar.useCallback[isGroupActive]\"]);\n        }\n    }[\"Sidebar.useCallback[isGroupActive]\"], [\n        isSubmenuActive\n    ]);\n    // Expandir automaticamente grupos que contêm o item ativo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            if (activeModule && _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) {\n                const newExpandedGroups = {\n                    ...expandedGroups\n                };\n                _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule].forEach({\n                    \"Sidebar.useEffect\": (submenu)=>{\n                        if (submenu.type === 'group' && isGroupActive(activeModule, submenu.items)) {\n                            newExpandedGroups[submenu.id] = true;\n                        }\n                    }\n                }[\"Sidebar.useEffect\"]);\n                setExpandedGroups(newExpandedGroups);\n            }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"Sidebar.useEffect\"], [\n        activeModule,\n        pathname,\n        isGroupActive\n    ]);\n    // Salvar estado dos grupos expandidos no localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            if (true) {\n                localStorage.setItem('sidebarExpandedGroups', JSON.stringify(expandedGroups));\n            }\n        }\n    }[\"Sidebar.useEffect\"], [\n        expandedGroups\n    ]);\n    // Verificar se um item de submenu tem permissão para ser exibido\n    const hasPermissionForSubmenuItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (moduleId, submenuId)=>{\n            var _moduleSubmenus_moduleId;\n            // Buscar o item dentro dos grupos do módulo\n            let submenuItem = null;\n            // Primeiro buscar nos itens diretos\n            submenuItem = (_moduleSubmenus_moduleId = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId]) === null || _moduleSubmenus_moduleId === void 0 ? void 0 : _moduleSubmenus_moduleId.find({\n                \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (item)=>item.id === submenuId\n            }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"]);\n            // Se não encontrou, buscar dentro dos grupos\n            if (!submenuItem) {\n                for (const item of _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId] || []){\n                    if (item.type === 'group' && item.items) {\n                        submenuItem = item.items.find({\n                            \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (subItem)=>subItem.id === submenuId\n                        }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"]);\n                        if (submenuItem) break;\n                    }\n                }\n            }\n            // Verificar se o item requer SYSTEM_ADMIN\n            if (submenuItem === null || submenuItem === void 0 ? void 0 : submenuItem.systemAdminOnly) {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            return hasPermissionForSubmenu(moduleId, submenuId);\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"], [\n        hasPermissionForSubmenu,\n        user === null || user === void 0 ? void 0 : user.role\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"\".concat(isMinimized ? 'w-16' : 'w-72', \" bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col \").concat(isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'),\n        \"aria-label\": \"Navega\\xe7\\xe3o lateral\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"flex-1 p-5\",\n                moduleColor: activeModule,\n                children: [\n                    activeModuleObject && !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModuleTitle, {\n                        moduleId: activeModule,\n                        title: activeModuleTitle,\n                        icon: ModuleIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex \".concat(isMinimized ? 'justify-center mb-6' : 'justify-end mb-4'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toggleMinimized,\n                            className: \"p-2 rounded-lg transition-all duration-300 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 \".concat(isMinimized ? 'mt-4' : ''),\n                            \"aria-label\": isMinimized ? \"Expandir sidebar\" : \"Minimizar sidebar\",\n                            title: isMinimized ? \"Expandir sidebar\" : \"Minimizar sidebar\",\n                            children: isMinimized ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 314,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 316,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, undefined),\n                    !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_CompanySelector__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        activeModule: activeModule\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 322,\n                        columnNumber: 26\n                    }, undefined),\n                    preferencesLoading && activeModule === 'scheduler' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-gray-500 dark:text-gray-400\",\n                                children: \"Carregando...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2 flex flex-col \".concat(isMinimized ? 'items-center' : 'justify-center items-center', \" mt-8\"),\n                        \"aria-labelledby\": \"sidebar-heading\",\n                        children: !(preferencesLoading && activeModule === 'scheduler') && activeModule && ((_moduleSubmenus_activeModule = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) === null || _moduleSubmenus_activeModule === void 0 ? void 0 : _moduleSubmenus_activeModule.map((submenu)=>{\n                            // Verificar se é um grupo ou um item individual\n                            if (submenu.type === 'group') {\n                                var // Mostrar ícone do primeiro item do grupo quando minimizado\n                                _submenu_items_;\n                                // Verificar se algum item do grupo tem permissão para ser exibido\n                                const hasAnyPermission = submenu.items.some((item)=>hasPermissionForSubmenuItem(activeModule, item.id));\n                                if (!hasAnyPermission) {\n                                    return null; // Não renderizar o grupo se nenhum item tiver permissão\n                                }\n                                const isGroupExpanded = expandedGroups[submenu.id] || false;\n                                const isAnyItemActive = isGroupActive(activeModule, submenu.items);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>!isMinimized && toggleGroup(submenu.id),\n                                            className: \"\\n                      group \".concat(isMinimized ? 'w-10 h-10 flex items-center justify-center rounded-full mx-auto' : 'w-full flex items-center justify-between px-4 py-2.5 rounded-lg', \" transition-all duration-300\\n                      \").concat(isAnyItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark font-medium\") : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700', \"\\n                    \"),\n                                            \"aria-expanded\": isGroupExpanded,\n                                            title: isMinimized ? submenu.title : undefined,\n                                            children: [\n                                                !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-left\",\n                                                    children: submenu.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 38\n                                                }, undefined),\n                                                isMinimized ? ((_submenu_items_ = submenu.items[0]) === null || _submenu_items_ === void 0 ? void 0 : _submenu_items_.icon) && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(submenu.items[0].icon, {\n                                                    size: 18,\n                                                    'aria-hidden': true\n                                                }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-500 dark:text-gray-400\",\n                                                    children: isGroupExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 27\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 356,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isGroupExpanded && !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4 pl-2 border-l border-gray-200 dark:border-gray-700 space-y-1\",\n                                            children: submenu.items.map((item)=>{\n                                                const isItemActive = isSubmenuActive(activeModule, item.id);\n                                                // Verificar permissão antes de renderizar o item\n                                                if (!hasPermissionForSubmenuItem(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não tiver permissão\n                                                }\n                                                // Verificar se o item deve ser exibido baseado nas preferências\n                                                if (!shouldShowSubmenuByPreferences(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não estiver habilitado nas preferências\n                                                }\n                                                // Verificar se o item está em construção\n                                                const isItemUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.isUnderConstruction)(activeModule, item.id);\n                                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.getConstructionMessage)(activeModule, item.id);\n                                                // Estilo comum para os itens\n                                                const itemClassName = \"\\n                          group \".concat(isMinimized ? 'w-10 h-10 flex items-center justify-center rounded-full mx-auto' : 'w-full flex items-center gap-3 px-3 py-2 rounded-lg', \" transition-all duration-300\\n                          \").concat(isItemActive ? \"\".concat(isMinimized ? 'rounded-full' : 'rounded-xl', \" border border-module-\").concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                               bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                               shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n                        \");\n                                                // Se estiver em construção, usar o ConstructionButton\n                                                if (isItemUnderConstruction) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_9__.ConstructionButton, {\n                                                        className: itemClassName,\n                                                        \"aria-current\": isItemActive ? 'page' : undefined,\n                                                        title: isMinimized ? item.title : constructionMessage.title,\n                                                        content: constructionMessage.content,\n                                                        icon: constructionMessage.icon,\n                                                        position: \"right\",\n                                                        children: [\n                                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\\n                                  \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                                \"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                    size: 18,\n                                                                    \"aria-hidden\": \"true\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 35\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 33\n                                                            }, undefined),\n                                                            !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                                children: item.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 48\n                                                            }, undefined)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 29\n                                                    }, undefined);\n                                                }\n                                                // Se não estiver em construção, usar o botão normal\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleModuleSubmenuClick(activeModule, item.id),\n                                                    className: itemClassName,\n                                                    \"aria-current\": isItemActive ? 'page' : undefined,\n                                                    title: isMinimized ? item.title : undefined,\n                                                    children: [\n                                                        item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"\\n                                \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                              \"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                size: 18,\n                                                                \"aria-hidden\": \"true\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 31\n                                                        }, undefined),\n                                                        !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 46\n                                                        }, undefined)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 27\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 385,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 354,\n                                    columnNumber: 17\n                                }, undefined);\n                            } else {\n                                // Renderização de itens individuais (não agrupados)\n                                const isActive = isSubmenuActive(activeModule, submenu.id);\n                                // Verificar permissão antes de renderizar o item\n                                const hasPermission = hasPermissionForSubmenu(activeModule, submenu.id);\n                                if (!hasPermission) {\n                                    return null; // Não renderizar se não tiver permissão\n                                }\n                                // Verificar se o submenu deve ser exibido baseado nas preferências\n                                const shouldShowByPreferences = shouldShowSubmenuByPreferences(activeModule, submenu.id);\n                                if (!shouldShowByPreferences) {\n                                    return null; // Não renderizar se não estiver habilitado nas preferências\n                                }\n                                // Verificar se o submenu está em construção\n                                const isSubmenuUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.isUnderConstruction)(activeModule, submenu.id);\n                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.getConstructionMessage)(activeModule, submenu.id);\n                                // Estilo comum para ambos os tipos de botões\n                                const buttonClassName = \"\\n                group \".concat(isMinimized ? 'w-12 h-12 flex items-center justify-center rounded-full mx-auto' : 'w-full flex items-center gap-3 px-4 py-3 rounded-lg', \" transition-all duration-300\\n                \").concat(isActive ? \"\".concat(isMinimized ? 'rounded-full' : 'rounded-xl', \" border border-module-\").concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                     bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                     shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n              \");\n                                // Se estiver em construção, usar o ConstructionButton\n                                if (isSubmenuUnderConstruction) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_9__.ConstructionButton, {\n                                        className: buttonClassName,\n                                        \"aria-current\": isActive ? 'page' : undefined,\n                                        title: isMinimized ? submenu.title : constructionMessage.title,\n                                        content: constructionMessage.content,\n                                        icon: constructionMessage.icon,\n                                        position: \"right\",\n                                        children: [\n                                            submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                    size: 20,\n                                                    \"aria-hidden\": \"true\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 520,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                                children: submenu.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 532,\n                                                columnNumber: 38\n                                            }, undefined)\n                                        ]\n                                    }, submenu.id, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                        lineNumber: 510,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }\n                                // Se não estiver em construção, usar o botão normal\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleModuleSubmenuClick(activeModule, submenu.id),\n                                    className: buttonClassName,\n                                    \"aria-current\": isActive ? 'page' : undefined,\n                                    title: isMinimized ? submenu.title : undefined,\n                                    children: [\n                                        submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                size: 20,\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 553,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 547,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                            children: submenu.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 559,\n                                            columnNumber: 36\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 539,\n                                    columnNumber: 17\n                                }, undefined);\n                            }\n                        }))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(isMinimized ? 'p-2' : 'p-5', \" border-t border-gray-100 dark:border-gray-700 mt-auto\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToModules,\n                    className: \"\\n            group w-full py-3 \".concat(isMinimized ? 'px-2 justify-center' : 'px-4 gap-3', \" rounded-lg flex items-center\\n            border-2 border-module-\").concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n            bg-transparent dark:bg-gray-800 hover:bg-module-\").concat(activeModule, \"-bg/10 dark:hover:bg-module-\").concat(activeModule, \"-bg-dark/10\\n            transition-all duration-300\\n          \"),\n                    \"aria-label\": \"Voltar para o dashboard principal\",\n                    title: isMinimized ? \"Voltar a Tela Inicial\" : undefined,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            flex items-center justify-center w-8 h-8 rounded-full\\n            bg-module-\".concat(activeModule, \"-bg dark:bg-module-\").concat(activeModule, \"-bg-dark/70\\n            text-module-\").concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\\n            shadow-sm dark:shadow-md dark:shadow-black/20\\n            group-hover:scale-110 transition-transform duration-200\\n          \"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 587,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 580,\n                            columnNumber: 11\n                        }, undefined),\n                        !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-gray-800 dark:text-gray-200\",\n                            children: \"Voltar a Tela Inicial\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 589,\n                            columnNumber: 28\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 569,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 568,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Sidebar, \"wNosui995CZ6nPqQw2gDp/n7fSs=\", false, function() {\n    return [\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__.useSchedulingPreferences\n    ];\n});\n_c1 = Sidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\nvar _c, _c1;\n$RefreshReg$(_c, \"ModuleTitle\");\n$RefreshReg$(_c1, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar/index.js\n"));

/***/ })

});