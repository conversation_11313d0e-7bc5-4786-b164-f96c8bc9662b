import React, { useState, useEffect } from 'react';
import { api } from '@/utils/api';
import { useToast } from '@/contexts/ToastContext';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2, Shield, Mail, CheckCircle, XCircle, Send, AlertTriangle } from 'lucide-react';
import { ModuleLabel } from '@/components/ui';

const TwoFactorSettings = ({ isSystemAdmin, isCompanyAdmin }) => {
  const { user } = useAuth();
  const { toast_success, toast_error } = useToast();
  
  const [twoFactorStatus, setTwoFactorStatus] = useState({
    enabled: false,
    method: null,
    companyPolicy: {
      required: false,
      allowed: true,
      allowedMethods: ['email']
    }
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isEnabling, setIsEnabling] = useState(false);
  const [isDisabling, setIsDisabling] = useState(false);
  const [isSendingTest, setIsSendingTest] = useState(false);
  const [isVerifyingTest, setIsVerifyingTest] = useState(false);
  const [testToken, setTestToken] = useState('');
  const [showTestSection, setShowTestSection] = useState(false);

  // Load 2FA status
  useEffect(() => {
    loadTwoFactorStatus();
  }, []);

  const loadTwoFactorStatus = async () => {
    try {
      setIsLoading(true);
      const response = await api.get('/2fa/settings');
      if (response.data.success) {
        const data = response.data.data;
        // Map backend structure to frontend structure
        setTwoFactorStatus({
          enabled: data.twoFactorEnabled || false,
          method: data.twoFactorMethod || null,
          companyPolicy: data.companyPolicy || {
            required: false,
            allowed: true,
            allowedMethods: ['email']
          }
        });
      }
    } catch (error) {
      console.error('Error loading 2FA status:', error);
      toast_error('Erro ao carregar status da autenticação de dois fatores');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEnable2FA = async () => {
    try {
      setIsEnabling(true);
      const response = await api.post('/2fa/enable', { method: 'email' });
      
      if (response.data.success) {
        toast_success(response.data.message);
        setTwoFactorStatus(prevStatus => ({
          ...prevStatus,
          enabled: true,
          method: 'email'
        }));
        setShowTestSection(true);
      }
    } catch (error) {
      console.error('Error enabling 2FA:', error);
      const message = error.response?.data?.message || 'Erro ao ativar autenticação de dois fatores';
      toast_error(message);
    } finally {
      setIsEnabling(false);
    }
  };

  const handleDisable2FA = async () => {
    if (!window.confirm('Tem certeza que deseja desativar a autenticação de dois fatores? Isso reduzirá a segurança da sua conta.')) {
      return;
    }

    try {
      setIsDisabling(true);
      const response = await api.post('/2fa/disable');
      
      if (response.data.success) {
        toast_success(response.data.message);
        setTwoFactorStatus(prevStatus => ({
          ...prevStatus,
          enabled: false,
          method: null
        }));
        setShowTestSection(false);
        setTestToken('');
      }
    } catch (error) {
      console.error('Error disabling 2FA:', error);
      const message = error.response?.data?.message || 'Erro ao desativar autenticação de dois fatores';
      toast_error(message);
    } finally {
      setIsDisabling(false);
    }
  };

  const handleSendTestToken = async () => {
    try {
      setIsSendingTest(true);
      const response = await api.post('/2fa/test/send');
      
      if (response.data.success) {
        toast_success('Token de teste enviado para seu email');
      }
    } catch (error) {
      console.error('Error sending test token:', error);
      const message = error.response?.data?.message || 'Erro ao enviar token de teste';
      toast_error(message);
    } finally {
      setIsSendingTest(false);
    }
  };

  const handleVerifyTestToken = async () => {
    if (testToken.length !== 6) {
      toast_error('Token deve ter 6 dígitos');
      return;
    }

    try {
      setIsVerifyingTest(true);
      const response = await api.post('/2fa/test/verify', { token: testToken });
      
      if (response.data.success) {
        toast_success('Token verificado com sucesso! Sua autenticação de dois fatores está funcionando corretamente.');
        setTestToken('');
        setShowTestSection(false);
      }
    } catch (error) {
      console.error('Error verifying test token:', error);
      const message = error.response?.data?.message || 'Erro ao verificar token de teste';
      toast_error(message);
    } finally {
      setIsVerifyingTest(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-4">
        <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex items-center mb-6">
        <Shield className="h-6 w-6 text-blue-500 mr-3" />
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Autenticação de Dois Fatores
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Adicione uma camada extra de segurança à sua conta
          </p>
        </div>
      </div>

      {/* Status atual */}
      <div className="mb-6">
        {/* Company Policy Alert */}
        {twoFactorStatus.companyPolicy?.required && !twoFactorStatus.enabled && (
          <div className="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-red-800 dark:text-red-200">
                  2FA Obrigatório
                </h4>
                <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                  Sua empresa exige autenticação de dois fatores. Você deve ativar 2FA para continuar usando o sistema.
                </p>
              </div>
            </div>
          </div>
        )}
        
        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center">
            {twoFactorStatus.enabled ? (
              <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
            ) : twoFactorStatus.companyPolicy?.required ? (
              <AlertTriangle className="h-5 w-5 text-red-500 mr-3" />
            ) : (
              <XCircle className="h-5 w-5 text-gray-500 mr-3" />
            )}
            <div>
              <div className="flex items-center gap-2">
                <p className="font-medium text-gray-900 dark:text-white">
                  {twoFactorStatus.enabled ? 'Ativado' : 'Desativado'}
                </p>
                {twoFactorStatus.companyPolicy?.required && (
                  <span className="px-2 py-1 text-xs bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 rounded-full">
                    Obrigatório
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {twoFactorStatus.enabled 
                  ? `Método: ${twoFactorStatus.method === 'email' ? 'Email' : twoFactorStatus.method}`
                  : twoFactorStatus.companyPolicy?.required
                    ? 'Configuração obrigatória pela empresa'
                    : 'Sua conta não está protegida por 2FA'
                }
              </p>
            </div>
          </div>
          
          <div className="flex space-x-2">
            {!twoFactorStatus.enabled ? (
              <button
                onClick={handleEnable2FA}
                disabled={isEnabling}
                className="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                {isEnabling ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Ativando...
                  </>
                ) : (
                  <>
                    <Shield className="h-4 w-4 mr-2" />
                    Ativar 2FA
                  </>
                )}
              </button>
            ) : (
              <div className="flex space-x-2">
                <button
                  onClick={() => setShowTestSection(!showTestSection)}
                  className="flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  <Send className="h-4 w-4 mr-2" />
                  Testar
                </button>
                <button
                  onClick={handleDisable2FA}
                  disabled={isDisabling || twoFactorStatus.companyPolicy?.required}
                  className="flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50"
                  title={twoFactorStatus.companyPolicy?.required ? 'Não é possível desativar: 2FA é obrigatório pela empresa' : ''}
                >
                  {isDisabling ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Desativando...
                    </>
                  ) : (
                    <>
                      <XCircle className="h-4 w-4 mr-2" />
                      {twoFactorStatus.companyPolicy?.required ? 'Bloqueado' : 'Desativar'}
                    </>
                  )}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Seção de teste */}
      {showTestSection && twoFactorStatus.enabled && (
        <div className="border-t border-gray-200 dark:border-gray-600 pt-6">
          <div className="flex items-center mb-4">
            <AlertTriangle className="h-5 w-5 text-orange-500 mr-2" />
            <h4 className="font-medium text-gray-900 dark:text-white">
              Testar Autenticação de Dois Fatores
            </h4>
          </div>
          
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Envie um token de teste para verificar se sua configuração está funcionando corretamente.
          </p>

          <div className="flex flex-col space-y-3">
            <button
              onClick={handleSendTestToken}
              disabled={isSendingTest}
              className="flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
            >
              {isSendingTest ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Enviando token...
                </>
              ) : (
                <>
                  <Mail className="h-4 w-4 mr-2" />
                  Enviar Token de Teste
                </>
              )}
            </button>

            {/* Campo para inserir token de teste */}
            <div className="flex space-x-2">
              <input
                type="text"
                value={testToken}
                onChange={(e) => setTestToken(e.target.value.replace(/\D/g, '').slice(0, 6))}
                placeholder="123456"
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-center font-mono"
                maxLength={6}
                autoComplete="off"
              />
              <button
                onClick={handleVerifyTestToken}
                disabled={testToken.length !== 6 || isVerifyingTest}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                {isVerifyingTest ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  'Verificar'
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Informações sobre 2FA */}
      {!twoFactorStatus.enabled && (
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <div className="flex items-start">
            <Shield className="h-5 w-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                Por que usar autenticação de dois fatores?
              </h4>
              <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                <li>• Protege sua conta mesmo se sua senha for comprometida</li>
                <li>• Códigos temporários enviados por email</li>
                <li>• Exigido para ações sensíveis do sistema</li>
                <li>• Pode ser desativado a qualquer momento</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TwoFactorSettings;