'use client';

import React, { useCallback, useState, useEffect } from 'react';
import { ChevronLeft, ChevronDown, ChevronRight, Construction, HardHat, Hammer, Wrench, AlertTriangle, PanelLeftClose, PanelLeftOpen } from 'lucide-react';
import { usePathname } from 'next/navigation';
import { modules, moduleSubmenus } from '@/app/dashboard/components';
import { usePermissions } from '@/hooks/usePermissions';
import { useAuth } from '@/contexts/AuthContext';
import { useSchedulingPreferences } from '@/hooks/useSchedulingPreferences';
import CustomScrollArea from '@/components/ui/CustomScrollArea';
import { useConstructionMessage } from '@/hooks/useConstructionMessage';
import { ConstructionButton } from '@/components/construction';
import { underConstructionSubmenus, constructionMessages, isUnderConstruction, getConstructionMessage } from '@/utils/constructionUtils';
import CompanySelector from '@/components/dashboard/CompanySelector';

const ModuleTitle = ({ moduleId, title, icon: Icon }) => {
  // Exibir o logo no lugar do título do módulo, como botão para /dashboard
  const handleLogoClick = () => {
    if (typeof window !== 'undefined') {
      window.location.href = '/dashboard';
    }
  };
  return (
    <div className="mb-6 flex justify-center items-center">
      <button
        onClick={handleLogoClick}
        style={{ background: 'none', border: 'none', padding: 0 }}
        className="focus:outline-none cursor-pointer"
        aria-label="Ir para o dashboard"
      >
        <div className="relative">
          <img
            src="/logo_horizontal_sem_fundo.png"
            alt="High Tide Logo"
            className="h-12 dark:invert dark:text-white transition-all"
          />
        </div>
      </button>
    </div>
  );
};



// Mapeamento de submenus para permissões
const submenuPermissionsMap = {
  // Admin
  'admin.introduction': 'admin.dashboard.view',
  'admin.dashboard': 'admin.dashboard.view',
  'admin.users': 'admin.users.view',
  'admin.professions': ['admin.professions.view', 'admin.profession-groups.view'],
  'admin.bug-reports': 'admin.dashboard.view', // Apenas SYSTEM_ADMIN tem acesso
  'admin.plans': 'admin.dashboard.view',
  'admin.logs': 'admin.logs.view',
  'admin.settings': 'admin.config.edit',
  'admin.backup': 'admin.config.edit',
  'admin.affiliates': 'admin.dashboard.view', // Apenas SYSTEM_ADMIN tem acesso

  // ABA+
  'abaplus.anamnese': 'abaplus.anamnese.view',
  'abaplus.evolucoes-diarias': 'abaplus.evolucoes-diarias.view',
  'abaplus.sessao': 'abaplus.sessao.view',

  // Financeiro
  'financial.invoices': 'financial.invoices.view',
  'financial.payments': 'financial.payments.view',
  'financial.expenses': 'financial.expenses.view',
  'financial.reports': 'financial.reports.view',
  'financial.cashflow': 'financial.reports.view',

  // RH
  'hr.employees': 'rh.employees.view',
  'hr.payroll': 'rh.payroll.view',
  'hr.documents': 'rh.employees.view',
  'hr.departments': 'rh.employees.view',
  'hr.attendance': 'rh.attendance.view',
  'hr.benefits': 'rh.benefits.view',
  'hr.training': 'rh.employees.view',

  // Pessoas
  'people.clients': 'people.clients.view',
  'people.persons': 'people.persons.view',
  'people.documents': 'people.documents.view',
  'people.insurances': 'people.insurances.view',
  'people.insurance-limits': 'people.insurance-limits.view',

  // Agendamento
  'scheduler.calendar': 'scheduling.calendar.view',
  'scheduler.working-hours': 'scheduling.working-hours.view',
  'scheduler.service-types': 'scheduling.service-types.view',
  'scheduler.locations': 'scheduling.locations.view',
  'scheduler.occupancy': 'scheduling.occupancy.view',
  'scheduler.appointment-requests': 'scheduling.appointment-requests.view',
  'scheduler.appointments-report': 'scheduling.appointments-report.view',
  'scheduler.appointments-dashboard': 'scheduling.appointments-dashboard.view',
};

const Sidebar = ({
  activeModule,
  activeModuleTitle,
  isSubmenuActive,
  handleModuleSubmenuClick,
  handleBackToModules,
  isSidebarOpen
}) => {
  const { can, hasModule, isAdmin } = usePermissions();
  const { user } = useAuth();
  const pathname = usePathname();
  const { schedulingPreferences, isLoading: preferencesLoading } = useSchedulingPreferences();

  // Inicializar o estado de grupos expandidos a partir do localStorage
  const [expandedGroups, setExpandedGroups] = useState(() => {
    // Verificar se estamos no cliente (browser) antes de acessar localStorage
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem('sidebarExpandedGroups');
      return savedState ? JSON.parse(savedState) : {};
    }
    return {};
  });

  // Estado para controlar se a sidebar está minimizada
  const [isMinimized, setIsMinimized] = useState(() => {
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem('sidebarMinimized');
      return savedState ? JSON.parse(savedState) : false;
    }
    return false;
  });

  // Encontrar o objeto do módulo ativo para acessar seu ícone
  const activeModuleObject = modules.find(m => m.id === activeModule);
  const ModuleIcon = activeModuleObject?.icon;

  // Função para verificar se o usuário tem permissão para ver um submenu
  const hasPermissionForSubmenu = useCallback((moduleId, submenuId) => {
    // Ignorar verificação para grupos, pois a permissão é verificada para cada item
    if (submenuId === 'cadastro' || submenuId === 'configuracoes' ||
      submenuId === 'gestao' || submenuId === 'convenios' ||
      submenuId === 'financeiro' || submenuId === 'relatorios') {
      return true;
    }

    // Buscar o submenu específico para verificar systemAdminOnly
    const submenuItem = moduleSubmenus[moduleId]?.find(item => item.id === submenuId);
    
    // Verificar se o submenu requer SYSTEM_ADMIN
    if (submenuItem?.systemAdminOnly) {
      return user?.role === 'SYSTEM_ADMIN';
    }

    // Verificação especial adicional para afiliados - apenas SYSTEM_ADMIN
    if (submenuId === 'affiliates') {
      return user?.role === 'SYSTEM_ADMIN';
    }

    // Verificação especial adicional para bug-reports - apenas SYSTEM_ADMIN
    if (submenuId === 'bug-reports') {
      return user?.role === 'SYSTEM_ADMIN';
    }

    const permissionKey = `${moduleId}.${submenuId}`;
    const requiredPermission = submenuPermissionsMap[permissionKey];

    // Se não há mapeamento de permissão, permitir acesso
    if (!requiredPermission) return true;

    // Administradores têm acesso a tudo (exceto itens systemAdminOnly)
    if (isAdmin()) return true;

    // Verificar permissão específica
    if (Array.isArray(requiredPermission)) {
      // Se for um array de permissões, verificar se o usuário tem pelo menos uma delas
      return requiredPermission.some(perm => can(perm));
    } else {
      // Se for uma única permissão, verificar normalmente
      return can(requiredPermission);
    }
  }, [can, isAdmin, user.role]);

  // Função para verificar se um submenu deve ser exibido baseado nas preferências
  const shouldShowSubmenuByPreferences = useCallback((moduleId, submenuId) => {
    // Apenas verificar preferências para o módulo de agendamento
    if (moduleId !== 'scheduler') return true;

    // Se as preferências ainda estão carregando, não mostrar os itens que podem ser escondidos
    if (preferencesLoading) {
      return false;
    }

    // Verificar se as preferências estão carregadas
    if (!schedulingPreferences) {
      return false;
    }

    switch (submenuId) {
      case 'locations':
        const showLocations = schedulingPreferences.showLocations !== false;
        return showLocations;
      case 'service-types':
        const showServiceTypes = schedulingPreferences.showServiceTypes !== false;
        return showServiceTypes;
      case 'insurances':
        const showInsurance = schedulingPreferences.showInsurance !== false;
        return showInsurance;
      case 'working-hours':
        const showWorkingHours = schedulingPreferences.showWorkingHours !== false;
        return showWorkingHours;
      default:
        return true;
    }
  }, [schedulingPreferences, preferencesLoading]);

  // Função para alternar a expansão de um grupo
  const toggleGroup = (groupId) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  };

  // Função para alternar o estado de minimização da sidebar
  const toggleMinimized = () => {
    setIsMinimized(prev => {
      const newState = !prev;
      if (typeof window !== 'undefined') {
        localStorage.setItem('sidebarMinimized', JSON.stringify(newState));
      }
      return newState;
    });
  };

  // Verificar se algum item dentro de um grupo está ativo
  const isGroupActive = useCallback((moduleId, groupItems) => {
    return groupItems.some(item => isSubmenuActive(moduleId, item.id));
  }, [isSubmenuActive]);

  // Expandir automaticamente grupos que contêm o item ativo
  useEffect(() => {
    if (activeModule && moduleSubmenus[activeModule]) {
      const newExpandedGroups = { ...expandedGroups };

      moduleSubmenus[activeModule].forEach(submenu => {
        if (submenu.type === 'group' && isGroupActive(activeModule, submenu.items)) {
          newExpandedGroups[submenu.id] = true;
        }
      });

      setExpandedGroups(newExpandedGroups);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeModule, pathname, isGroupActive]);

  // Salvar estado dos grupos expandidos no localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('sidebarExpandedGroups', JSON.stringify(expandedGroups));
    }
  }, [expandedGroups]);

  // Verificar se um item de submenu tem permissão para ser exibido
  const hasPermissionForSubmenuItem = useCallback((moduleId, submenuId) => {
    // Buscar o item dentro dos grupos do módulo
    let submenuItem = null;
    
    // Primeiro buscar nos itens diretos
    submenuItem = moduleSubmenus[moduleId]?.find(item => item.id === submenuId);
    
    // Se não encontrou, buscar dentro dos grupos
    if (!submenuItem) {
      for (const item of moduleSubmenus[moduleId] || []) {
        if (item.type === 'group' && item.items) {
          submenuItem = item.items.find(subItem => subItem.id === submenuId);
          if (submenuItem) break;
        }
      }
    }
    
    // Verificar se o item requer SYSTEM_ADMIN
    if (submenuItem?.systemAdminOnly) {
      return user?.role === 'SYSTEM_ADMIN';
    }
    
    return hasPermissionForSubmenu(moduleId, submenuId);
  }, [hasPermissionForSubmenu, user?.role]);

  return (
    <aside
      className={`${isMinimized ? 'w-16' : 'w-72'} bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
        }`}
      aria-label="Navegação lateral"
    >
      {/* Conteúdo principal da sidebar */}
      <CustomScrollArea className="flex-1 p-5" moduleColor={activeModule}>
        {/* Título do módulo estilizado (logo) */}
        {activeModuleObject && !isMinimized && (
          <ModuleTitle
            moduleId={activeModule}
            title={activeModuleTitle}
            icon={ModuleIcon}
          />
        )}

        {/* Botão de minimizar/expandir */}
        <div className={`flex ${isMinimized ? 'justify-center mb-6' : 'justify-end mb-4'}`}>
          <button
            onClick={toggleMinimized}
            className={`p-2 rounded-lg transition-all duration-300 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 ${
              isMinimized ? 'mt-4' : ''
            }`}
            aria-label={isMinimized ? "Expandir sidebar" : "Minimizar sidebar"}
            title={isMinimized ? "Expandir sidebar" : "Minimizar sidebar"}
          >
            {isMinimized ? (
              <PanelLeftOpen size={20} aria-hidden="true" />
            ) : (
              <PanelLeftClose size={20} aria-hidden="true" />
            )}
          </button>
        </div>

        {/* Seletor de empresa para SYSTEM_ADMIN */}
        {!isMinimized && <CompanySelector activeModule={activeModule} />}

        {/* Loading state enquanto as preferências carregam */}
        {preferencesLoading && activeModule === 'scheduler' && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"></div>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">Carregando...</span>
          </div>
        )}

        {/* Centralizar e afastar os botões das páginas */}
        <nav
          className={`space-y-2 flex flex-col ${isMinimized ? 'items-center' : 'justify-center items-center'} mt-8`}
          aria-labelledby="sidebar-heading"
        >
          {/* Não renderizar submenus enquanto as preferências carregam para o módulo scheduler */}
          {!(preferencesLoading && activeModule === 'scheduler') && activeModule && moduleSubmenus[activeModule]?.map((submenu) => {
            // Verificar se é um grupo ou um item individual
            if (submenu.type === 'group') {
              // Verificar se algum item do grupo tem permissão para ser exibido
              const hasAnyPermission = submenu.items.some(item =>
                hasPermissionForSubmenuItem(activeModule, item.id)
              );

              if (!hasAnyPermission) {
                return null; // Não renderizar o grupo se nenhum item tiver permissão
              }

              const isGroupExpanded = expandedGroups[submenu.id] || false;
              const isAnyItemActive = isGroupActive(activeModule, submenu.items);

              return (
                <div key={submenu.id} className="space-y-1">
                  {/* Cabeçalho do grupo */}
                  <button
                    onClick={() => !isMinimized && toggleGroup(submenu.id)}
                    className={`
                      group w-full flex items-center ${isMinimized ? 'justify-center px-2 py-2.5' : 'justify-between px-4 py-2.5'} rounded-lg transition-all duration-300
                      ${isAnyItemActive
                        ? `text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark font-medium`
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                      }
                    `}
                    aria-expanded={isGroupExpanded}
                    title={isMinimized ? submenu.title : undefined}
                  >
                    {!isMinimized && <span className="font-medium text-left">{submenu.title}</span>}
                    {isMinimized ? (
                      // Mostrar ícone do primeiro item do grupo quando minimizado
                      submenu.items[0]?.icon && React.createElement(submenu.items[0].icon, { size: 18, 'aria-hidden': true })
                    ) : (
                      <div className="text-gray-500 dark:text-gray-400">
                        {isGroupExpanded ? (
                          <ChevronDown size={18} aria-hidden="true" />
                        ) : (
                          <ChevronRight size={18} aria-hidden="true" />
                        )}
                      </div>
                    )}
                  </button>

                  {/* Itens do grupo - visíveis apenas quando expandido e não minimizado */}
                  {isGroupExpanded && !isMinimized && (
                    <div className="ml-4 pl-2 border-l border-gray-200 dark:border-gray-700 space-y-1">
                      {submenu.items.map(item => {
                        const isItemActive = isSubmenuActive(activeModule, item.id);

                                      // Verificar permissão antes de renderizar o item
              if (!hasPermissionForSubmenuItem(activeModule, item.id)) {
                return null; // Não renderizar se não tiver permissão
              }

              // Verificar se o item deve ser exibido baseado nas preferências
              if (!shouldShowSubmenuByPreferences(activeModule, item.id)) {
                return null; // Não renderizar se não estiver habilitado nas preferências
              }

                        // Verificar se o item está em construção
                        const isItemUnderConstruction = isUnderConstruction(activeModule, item.id);
                        const constructionMessage = getConstructionMessage(activeModule, item.id);

                        // Estilo comum para os itens
                        const itemClassName = `
                          group w-full flex items-center ${isMinimized ? 'justify-center px-2 py-2' : 'gap-3 px-3 py-2'} rounded-lg transition-all duration-300
                          ${isItemActive
                            ? `rounded-xl border border-module-${activeModule}-border dark:border-module-${activeModule}-border-dark
                               bg-module-${activeModule}-bg dark:bg-gray-700 dark:bg-opacity-90
                               shadow-md dark:shadow-black/20`
                            : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark'
                          }
                        `;

                        // Se estiver em construção, usar o ConstructionButton
                        if (isItemUnderConstruction) {
                          return (
                            <ConstructionButton
                              key={item.id}
                              className={itemClassName}
                              aria-current={isItemActive ? 'page' : undefined}
                              title={isMinimized ? item.title : constructionMessage.title}
                              content={constructionMessage.content}
                              icon={constructionMessage.icon}
                              position="right"
                            >
                              {item.icon && (
                                <div className={`
                                  ${isItemActive
                                    ? `text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark`
                                    : `text-gray-500 dark:text-gray-400 group-hover:text-module-${activeModule}-icon dark:group-hover:text-module-${activeModule}-icon-dark transition-colors duration-200`
                                  }
                                `}>
                                  <item.icon
                                    size={18}
                                    aria-hidden="true"
                                  />
                                </div>
                              )}
                              {!isMinimized && <span className={`font-medium text-left text-sm ${isItemActive ? 'dark:text-white' : ''}`}>{item.title}</span>}
                            </ConstructionButton>
                          );
                        }

                        // Se não estiver em construção, usar o botão normal
                        return (
                          <button
                            key={item.id}
                            onClick={() => handleModuleSubmenuClick(activeModule, item.id)}
                            className={itemClassName}
                            aria-current={isItemActive ? 'page' : undefined}
                            title={isMinimized ? item.title : undefined}
                          >
                            {item.icon && (
                              <div className={`
                                ${isItemActive
                                  ? `text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark`
                                  : `text-gray-500 dark:text-gray-400 group-hover:text-module-${activeModule}-icon dark:group-hover:text-module-${activeModule}-icon-dark transition-colors duration-200`
                                }
                              `}>
                                <item.icon
                                  size={18}
                                  aria-hidden="true"
                                />
                              </div>
                            )}
                            {!isMinimized && <span className={`font-medium text-left text-sm ${isItemActive ? 'dark:text-white' : ''}`}>{item.title}</span>}
                          </button>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            } else {
              // Renderização de itens individuais (não agrupados)
              const isActive = isSubmenuActive(activeModule, submenu.id);

              // Verificar permissão antes de renderizar o item
              const hasPermission = hasPermissionForSubmenu(activeModule, submenu.id);
              
              if (!hasPermission) {
                return null; // Não renderizar se não tiver permissão
              }

              // Verificar se o submenu deve ser exibido baseado nas preferências
              const shouldShowByPreferences = shouldShowSubmenuByPreferences(activeModule, submenu.id);
              
              if (!shouldShowByPreferences) {
                return null; // Não renderizar se não estiver habilitado nas preferências
              }

              // Verificar se o submenu está em construção
              const submenuKey = `${activeModule}.${submenu.id}`;
              const isSubmenuUnderConstruction = isUnderConstruction(activeModule, submenu.id);
              const constructionMessage = getConstructionMessage(activeModule, submenu.id);

              // Estilo comum para ambos os tipos de botões
              const buttonClassName = `
                group w-full flex items-center ${isMinimized ? 'justify-center px-2 py-3' : 'gap-3 px-4 py-3'} rounded-lg transition-all duration-300
                ${isActive
                  ? `rounded-xl border border-module-${activeModule}-border dark:border-module-${activeModule}-border-dark
                     bg-module-${activeModule}-bg dark:bg-gray-700 dark:bg-opacity-90
                     shadow-md dark:shadow-black/20`
                  : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark'
                }
              `;

              // Se estiver em construção, usar o ConstructionButton
              if (isSubmenuUnderConstruction) {
                return (
                  <ConstructionButton
                    key={submenu.id}
                    className={buttonClassName}
                    aria-current={isActive ? 'page' : undefined}
                    title={isMinimized ? submenu.title : constructionMessage.title}
                    content={constructionMessage.content}
                    icon={constructionMessage.icon}
                    position="right"
                  >
                    {submenu.icon && (
                      <div className={`
                      ${isActive
                          ? `text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark`
                          : `text-gray-500 dark:text-gray-400 group-hover:text-module-${activeModule}-icon dark:group-hover:text-module-${activeModule}-icon-dark transition-colors duration-200`
                        }
                    `}>
                        <submenu.icon
                          size={20}
                          aria-hidden="true"
                        />
                      </div>
                    )}
                    {!isMinimized && <span className={`font-medium text-left ${isActive ? 'dark:text-white' : ''}`}>{submenu.title}</span>}
                  </ConstructionButton>
                );
              }

              // Se não estiver em construção, usar o botão normal
              return (
                <button
                  key={submenu.id}
                  onClick={() => handleModuleSubmenuClick(activeModule, submenu.id)}
                  className={buttonClassName}
                  aria-current={isActive ? 'page' : undefined}
                  title={isMinimized ? submenu.title : undefined}
                >
                  {submenu.icon && (
                    <div className={`
                      ${isActive
                        ? `text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark`
                        : `text-gray-500 dark:text-gray-400 group-hover:text-module-${activeModule}-icon dark:group-hover:text-module-${activeModule}-icon-dark transition-colors duration-200`
                      }
                    `}>
                      <submenu.icon
                        size={20}
                        aria-hidden="true"
                      />
                    </div>
                  )}
                  {!isMinimized && <span className={`font-medium text-left ${isActive ? 'dark:text-white' : ''}`}>{submenu.title}</span>}
                </button>
              );
            }
          })}
        </nav>
      </CustomScrollArea>

      {/* Botão de voltar fixo na parte inferior */}
      <div className={`${isMinimized ? 'p-2' : 'p-5'} border-t border-gray-100 dark:border-gray-700 mt-auto`}>
        <button
          onClick={handleBackToModules}
          className={`
            group w-full py-3 ${isMinimized ? 'px-2 justify-center' : 'px-4 gap-3'} rounded-lg flex items-center
            border-2 border-module-${activeModule}-border dark:border-module-${activeModule}-border-dark
            bg-transparent dark:bg-gray-800 hover:bg-module-${activeModule}-bg/10 dark:hover:bg-module-${activeModule}-bg-dark/10
            transition-all duration-300
          `}
          aria-label="Voltar para o dashboard principal"
          title={isMinimized ? "Voltar a Tela Inicial" : undefined}
        >
          <div className={`
            flex items-center justify-center w-8 h-8 rounded-full
            bg-module-${activeModule}-bg dark:bg-module-${activeModule}-bg-dark/70
            text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark
            shadow-sm dark:shadow-md dark:shadow-black/20
            group-hover:scale-110 transition-transform duration-200
          `}>
            <ChevronLeft size={20} aria-hidden="true" />
          </div>
          {!isMinimized && <span className="font-medium text-gray-800 dark:text-gray-200">Voltar a Tela Inicial</span>}
        </button>
      </div>
    </aside>
  );
};

export default Sidebar;