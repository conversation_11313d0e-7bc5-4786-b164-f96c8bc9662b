import { useState, useEffect } from 'react';
import { preferencesService } from '@/services/preferencesService';

export const usePreferences = () => {
  const [preferences, setPreferences] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Carregar preferências
  const loadPreferences = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await preferencesService.get();
      setPreferences(data);
    } catch (err) {
      console.error('Erro ao carregar preferências:', err);
      setError(err.message || 'Erro ao carregar preferências');
      // Definir preferências padrão em caso de erro
      setPreferences({
        user: {
          userCpfCnpj: true,
          userUnit: false,
          userBirthDate: true,
          userPhone: true,
          userCep: true,
          userRole: true,
          userProfilePhoto: false,
          require2FA: false
        },

        patient: {
          patientCpfCnpj: true,
          patientBirthDate: true,
          patientGender: false,
          patientPhone: false,
          patientCep: false,
          patientProfilePhoto: false,
          patientAssociateClient: true,
          patientObservation: false
        },
        userPrivacy: {
          hideUserCpf: false,
          hideUserCnpj: false,
          hideUserEmail: false,
          hideUserPhone: false,
          hideUserAddress: false,
          hideUserBirthDate: false,
          hideUserLastLoginIp: false
        },
        clientPrivacy: {
          hideClientEmail: false,
          hideClientFullName: false
        },
        patientPrivacy: {
          hidePatientCpf: false,
          hidePatientEmail: false,
          hidePatientPhone: false,
          hidePatientAddress: false,
          hidePatientBirthDate: false,
          hidePatientNotes: false,
          hidePatientProfileImage: false
        }
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Salvar preferências
  const savePreferences = async (newPreferences) => {
    try {
      setError(null);
      const data = await preferencesService.save(newPreferences);
      setPreferences(data);
      return { success: true, data };
    } catch (err) {
      console.error('Erro ao salvar preferências:', err);
      setError(err.message || 'Erro ao salvar preferências');
      return { success: false, error: err.message };
    }
  };

  // Obter campos obrigatórios para um tipo de entidade
  const getRequiredFields = (entityType) => {
    if (!preferences || !preferences[entityType]) {
      return {};
    }
    return preferences[entityType];
  };

  // Verificar se um campo específico é obrigatório
  const isFieldRequired = (entityType, fieldName) => {
    const entityPrefs = getRequiredFields(entityType);
    return entityPrefs[fieldName] === true;
  };

  // Obter todos os campos obrigatórios para validação
  const getRequiredFieldsForValidation = (entityType) => {
    const entityPrefs = getRequiredFields(entityType);
    const requiredFields = {};

    // Campos sempre obrigatórios
    if (entityType === 'user') {
      requiredFields.login = true;
      requiredFields.fullName = true;
      requiredFields.email = true;
      requiredFields.password = true;
    } else if (entityType === 'client') {
      requiredFields.login = true;
      requiredFields.email = true;
      requiredFields.password = true;
      requiredFields['person.fullName'] = true;
    } else if (entityType === 'patient') {
      requiredFields.fullName = true;
    }

    // Adicionar campos obrigatórios baseados nas preferências
    Object.keys(entityPrefs).forEach(key => {
      if (entityPrefs[key] === true) {
        requiredFields[key] = true;
      }
    });

    return requiredFields;
  };

  // Verificar se um campo específico deve ser ocultado
  const shouldHideField = (entityType, fieldName) => {
    const privacySection = `${entityType}Privacy`;
    if (!preferences || !preferences[privacySection]) {
      return false;
    }
    const hideKey = `hide${entityType.charAt(0).toUpperCase() + entityType.slice(1)}${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)}`;
    return preferences[privacySection][hideKey] === true;
  };

  // Obter todas as configurações de privacidade para uma entidade
  const getPrivacySettings = (entityType) => {
    const privacySection = `${entityType}Privacy`;
    if (!preferences || !preferences[privacySection]) {
      return {};
    }
    return preferences[privacySection];
  };

  // Verificar se qualquer campo de privacidade está ativo para uma entidade
  const hasAnyPrivacySettings = (entityType) => {
    const privacySettings = getPrivacySettings(entityType);
    return Object.values(privacySettings).some(value => value === true);
  };

  // Carregar preferências automaticamente
  useEffect(() => {
    loadPreferences();
  }, []);

  return {
    preferences,
    isLoading,
    error,
    loadPreferences,
    savePreferences,
    getRequiredFields,
    isFieldRequired,
    getRequiredFieldsForValidation,
    shouldHideField,
    getPrivacySettings,
    hasAnyPrivacySettings
  };
}; 