"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(auth)/login/page",{

/***/ "(app-pages-browser)/./src/contexts/ToastContext.js":
/*!**************************************!*\
  !*** ./src/contexts/ToastContext.js ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TOAST_TYPES: () => (/* binding */ TOAST_TYPES),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* __next_internal_client_entry_do_not_use__ TOAST_TYPES,ToastProvider,useToast auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n// Tipos de Toast\nconst TOAST_TYPES = {\n    SUCCESS: 'success',\n    ERROR: 'error',\n    WARNING: 'warning',\n    INFO: 'info'\n};\n// Estado inicial\nconst initialState = {\n    toasts: []\n};\n// Ações\nconst ACTIONS = {\n    ADD_TOAST: 'ADD_TOAST',\n    REMOVE_TOAST: 'REMOVE_TOAST',\n    REMOVE_ALL_TOASTS: 'REMOVE_ALL_TOASTS'\n};\n// Reducer para manipular os toasts\nconst toastReducer = (state, action)=>{\n    switch(action.type){\n        case ACTIONS.ADD_TOAST:\n            return {\n                ...state,\n                toasts: [\n                    ...state.toasts,\n                    action.payload\n                ]\n            };\n        case ACTIONS.REMOVE_TOAST:\n            return {\n                ...state,\n                toasts: state.toasts.filter((toast)=>toast.id !== action.payload)\n            };\n        case ACTIONS.REMOVE_ALL_TOASTS:\n            return {\n                ...state,\n                toasts: []\n            };\n        default:\n            return state;\n    }\n};\n// Criar o contexto\nconst ToastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst ToastProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(toastReducer, initialState);\n    // Função para adicionar um toast\n    const addToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ToastProvider.useCallback[addToast]\": function(messageOrOptions) {\n            let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : TOAST_TYPES.INFO, duration = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 5000;\n            const id = (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n            // Definir o toast baseado no tipo de entrada\n            let toast;\n            if (typeof messageOrOptions === 'object' && messageOrOptions !== null) {\n                // É um objeto de opções\n                toast = {\n                    id,\n                    title: messageOrOptions.title || null,\n                    message: messageOrOptions.message || '',\n                    type: messageOrOptions.type || type,\n                    duration: typeof messageOrOptions.duration === 'number' ? messageOrOptions.duration : duration,\n                    createdAt: new Date()\n                };\n            } else {\n                // É uma string simples\n                toast = {\n                    id,\n                    title: null,\n                    message: String(messageOrOptions),\n                    type,\n                    duration,\n                    createdAt: new Date()\n                };\n            }\n            // Adicionar o toast\n            dispatch({\n                type: ACTIONS.ADD_TOAST,\n                payload: toast\n            });\n            // Configurar remoção automática se duration > 0\n            if (toast.duration > 0) {\n                setTimeout({\n                    \"ToastProvider.useCallback[addToast]\": ()=>{\n                        removeToast(id);\n                    }\n                }[\"ToastProvider.useCallback[addToast]\"], toast.duration);\n            }\n            return id;\n        }\n    }[\"ToastProvider.useCallback[addToast]\"], []);\n    // Função para remover um toast específico\n    const removeToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ToastProvider.useCallback[removeToast]\": (id)=>{\n            dispatch({\n                type: ACTIONS.REMOVE_TOAST,\n                payload: id\n            });\n        }\n    }[\"ToastProvider.useCallback[removeToast]\"], []);\n    // Função para remover todos os toasts\n    const removeAllToasts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ToastProvider.useCallback[removeAllToasts]\": ()=>{\n            dispatch({\n                type: ACTIONS.REMOVE_ALL_TOASTS\n            });\n        }\n    }[\"ToastProvider.useCallback[removeAllToasts]\"], []);\n    // Funções de conveniência para cada tipo de toast\n    const toast_success = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ToastProvider.useCallback[toast_success]\": function(messageOrOptions) {\n            let duration = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3000;\n            // Reduzir duração padrão dos toasts de sucesso para evitar interferências\n            return addToast(messageOrOptions, TOAST_TYPES.SUCCESS, duration);\n        }\n    }[\"ToastProvider.useCallback[toast_success]\"], [\n        addToast\n    ]);\n    const toast_error = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ToastProvider.useCallback[toast_error]\": (messageOrOptions, duration)=>{\n            return addToast(messageOrOptions, TOAST_TYPES.ERROR, duration);\n        }\n    }[\"ToastProvider.useCallback[toast_error]\"], [\n        addToast\n    ]);\n    const toast_warning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ToastProvider.useCallback[toast_warning]\": (messageOrOptions, duration)=>{\n            return addToast(messageOrOptions, TOAST_TYPES.WARNING, duration);\n        }\n    }[\"ToastProvider.useCallback[toast_warning]\"], [\n        addToast\n    ]);\n    const toast_info = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ToastProvider.useCallback[toast_info]\": (messageOrOptions, duration)=>{\n            return addToast(messageOrOptions, TOAST_TYPES.INFO, duration);\n        }\n    }[\"ToastProvider.useCallback[toast_info]\"], [\n        addToast\n    ]);\n    // Valores que serão expostos pelo contexto\n    const contextValue = {\n        toasts: state.toasts,\n        addToast,\n        removeToast,\n        removeAllToasts,\n        toast_success,\n        toast_error,\n        toast_warning,\n        toast_info\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\contexts\\\\ToastContext.js\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ToastProvider, \"enPXEUnchue9S0ZY4aZmLtWi7tU=\");\n_c = ToastProvider;\nconst useToast = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (!context) {\n        throw new Error('useToast deve ser usado dentro de um ToastProvider');\n    }\n    return context;\n};\n_s1(useToast, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ToastProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ToastContext.js\n"));

/***/ })

});