"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar/ClientSidebar.js":
/*!***********************************************************!*\
  !*** ./src/components/dashboard/Sidebar/ClientSidebar.js ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronLeft,Clock,FileText,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronLeft,Clock,FileText,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronLeft,Clock,FileText,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronLeft,Clock,FileText,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronLeft,Clock,FileText,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronLeft,Clock,FileText,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/CustomScrollArea */ \"(app-pages-browser)/./src/components/ui/CustomScrollArea.js\");\n// src/components/dashboard/Sidebar/ClientSidebar.js\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Client-specific submenu configuration\nconst clientModuleSubmenus = {\n    people: [\n        {\n            id: 'persons',\n            title: 'Pessoas',\n            icon: _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            description: 'Gerenciar pessoas relacionadas'\n        }\n    ],\n    scheduler: [\n        {\n            id: 'calendar',\n            title: 'Calendário',\n            icon: _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            description: 'Visualizar agenda'\n        },\n        {\n            id: 'appointments',\n            title: 'Meus Agendamentos',\n            icon: _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: 'Visualizar meus agendamentos'\n        },\n        {\n            id: 'requests',\n            title: 'Minhas Solicitações',\n            icon: _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: 'Acompanhar solicitações de agendamento'\n        }\n    ],\n    profile: [\n        {\n            id: 'profile',\n            title: 'Meu Perfil',\n            icon: _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: 'Gerenciar meu perfil e dados pessoais'\n        }\n    ]\n};\nconst ModuleTitle = (param)=>{\n    let { moduleId, title, icon: Icon, onDashboardClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6 flex justify-center items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: onDashboardClick,\n            style: {\n                background: 'none',\n                border: 'none',\n                padding: 0\n            },\n            className: \"focus:outline-none cursor-pointer\",\n            \"aria-label\": \"Ir para o dashboard\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/logo_horizontal_sem_fundo.png\",\n                    alt: \"High Tide Logo\",\n                    className: \"h-12 dark:invert dark:text-white transition-all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                    lineNumber: 33,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ModuleTitle;\nconst ClientSidebar = (param)=>{\n    let { activeModule, activeModuleTitle, isSubmenuActive, handleModuleSubmenuClick, handleBackToModules, isSidebarOpen, isMinimized, toggleMinimized } = param;\n    var _clientModuleSubmenus_activeModule;\n    // Encontrar o objeto do módulo ativo para acessar seu ícone\n    const ModuleIcon = activeModule === 'people' ? _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"] : activeModule === 'scheduler' ? _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"] : activeModule === 'profile' ? _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"] : _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"\".concat(isMinimized ? 'w-16' : 'w-72', \" bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col \").concat(isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'),\n        \"aria-label\": \"Navega\\xe7\\xe3o lateral\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"flex-1 \".concat(isMinimized ? 'p-2' : 'p-5'),\n                moduleColor: activeModule,\n                children: [\n                    activeModule && !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModuleTitle, {\n                        moduleId: activeModule,\n                        title: activeModuleTitle,\n                        icon: ModuleIcon,\n                        onDashboardClick: handleBackToModules\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2\",\n                        \"aria-labelledby\": \"sidebar-heading\",\n                        children: activeModule && ((_clientModuleSubmenus_activeModule = clientModuleSubmenus[activeModule]) === null || _clientModuleSubmenus_activeModule === void 0 ? void 0 : _clientModuleSubmenus_activeModule.map((submenu)=>{\n                            const isActive = isSubmenuActive(activeModule, submenu.id);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleModuleSubmenuClick(activeModule, submenu.id),\n                                className: \"\\n                  group \".concat(isMinimized ? 'w-12 h-12 flex items-center justify-center rounded-full mx-auto' : 'w-full flex items-center gap-3 px-4 py-3 rounded-lg', \" transition-all duration-300\\n                  \").concat(isActive ? \"\".concat(isMinimized ? 'rounded-full' : 'rounded-xl', \" border border-module-\").concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                       bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                       shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n                \"),\n                                \"aria-current\": isActive ? 'page' : undefined,\n                                title: isMinimized ? submenu.title : undefined,\n                                children: [\n                                    submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\\n                    \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                  \"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                            size: 20,\n                                            \"aria-hidden\": \"true\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                                            lineNumber: 108,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                                        lineNumber: 102,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                        children: submenu.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                                        lineNumber: 114,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, submenu.id, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, undefined);\n                        }))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-5 border-t border-gray-100 dark:border-gray-700 mt-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToModules,\n                    className: \"\\n            group w-full py-3 px-4 rounded-lg flex items-center gap-3\\n            border-2 border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n            bg-transparent dark:bg-gray-800 hover:bg-module-\").concat(activeModule, \"-bg/10 dark:hover:bg-module-\").concat(activeModule, \"-bg-dark/10\\n            transition-all duration-300\\n          \"),\n                    \"aria-label\": \"Voltar para o dashboard principal\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            flex items-center justify-center w-8 h-8 rounded-full\\n            bg-module-\".concat(activeModule, \"-bg dark:bg-module-\").concat(activeModule, \"-bg-dark/70\\n            text-module-\").concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\\n            shadow-sm dark:shadow-md dark:shadow-black/20\\n            group-hover:scale-110 transition-transform duration-200\\n          \"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-gray-800 dark:text-gray-200\",\n                            children: \"Voltar a Tela Inicial\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ClientSidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClientSidebar);\nvar _c, _c1;\n$RefreshReg$(_c, \"ModuleTitle\");\n$RefreshReg$(_c1, \"ClientSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar/ClientSidebar.js\n"));

/***/ })

});