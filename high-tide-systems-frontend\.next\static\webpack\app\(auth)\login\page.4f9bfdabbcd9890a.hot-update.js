"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(auth)/login/page",{

/***/ "(app-pages-browser)/./src/app/(auth)/login/page.js":
/*!**************************************!*\
  !*** ./src/app/(auth)/login/page.js ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,HelpCircle,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,HelpCircle,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,HelpCircle,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,HelpCircle,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,HelpCircle,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,HelpCircle,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _config_appConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/appConfig */ \"(app-pages-browser)/./src/config/appConfig.js\");\n/* harmony import */ var _components_auth_TwoFactorModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/auth/TwoFactorModal */ \"(app-pages-browser)/./src/components/auth/TwoFactorModal.js\");\n// app/(auth)/login/page.js\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction LoginPage() {\n    _s();\n    const [loginType, setLoginType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('email'); // 'email' ou 'username'\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [show2FAModal, setShow2FAModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [twoFactorData, setTwoFactorData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { login, verify2FAToken, resend2FAToken, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        try {\n            // Decide qual dado enviar com base no tipo de login selecionado\n            const identifier = loginType === 'email' ? email : username;\n            const result = await login(identifier, password, loginType);\n            // Check if 2FA is required\n            if (result && result.requiresTwoFactor) {\n                setTwoFactorData({\n                    userId: result.userId,\n                    expiresAt: result.expiresAt\n                });\n                setShow2FAModal(true);\n            }\n        } catch (err) {\n            if (err.message === 'USER_LIMIT_EXCEEDED') {\n                setError('O limite de usuários ativos do seu plano foi excedido. Entre em contato com o administrador da empresa para liberar acesso.');\n            } else {\n                setError(err.message || 'Erro ao fazer login');\n            }\n        }\n    };\n    const toggleShowPassword = ()=>{\n        setShowPassword(!showPassword);\n    };\n    const handle2FASuccess = async (token)=>{\n        try {\n            await verify2FAToken(twoFactorData.userId, token);\n            setShow2FAModal(false);\n            setTwoFactorData(null);\n        } catch (err) {\n            // Error is handled by the modal itself\n            throw err;\n        }\n    };\n    const handle2FACancel = ()=>{\n        setShow2FAModal(false);\n        setTwoFactorData(null);\n        setError('');\n    };\n    const handle2FAResend = async ()=>{\n        try {\n            const result = await resend2FAToken(twoFactorData.userId);\n            setTwoFactorData((prev)=>({\n                    ...prev,\n                    expiresAt: result.expiresAt\n                }));\n            return result;\n        } catch (err) {\n            throw err;\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-orange-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-orange-50 dark:bg-background flex justify-items-center flex-col md:flex-row transition-colors\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full flex items-center justify-center p-6 bg-gradient-to-br from-orange-200 to-orange-600 dark:from-background dark:to-background transition-colors\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-orange-50/80 dark:bg-[#23272f]/90 rounded-2xl shadow-xl w-full max-w-md p-8 transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"/logo_horizontal_sem_fundo.png\",\n                                alt: \"High Tide Logo\",\n                                className: \"max-w-full max-h-20 dark:invert transition-all\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute -bottom-1 right-3 text-xs text-gray-500 dark:text-gray-400 font-mono\",\n                                children: _config_appConfig__WEBPACK_IMPORTED_MODULE_4__.APP_VERSION\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 dark:bg-red-900 text-red-500 dark:text-red-200 p-3 rounded-lg text-sm mb-6 transition-colors\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                        lineNumber: 104,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex mb-6 border-b border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex-1 py-3 px-4 text-center font-medium text-sm transition-colors \".concat(loginType === 'username' ? 'border-b-2 border-orange-500 text-orange-600 dark:text-orange-400 dark:border-orange-400' : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'),\n                                onClick: ()=>setLoginType('username'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            size: 16,\n                                            className: \"mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Entrar com Login\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex-1 py-3 px-4 text-center font-medium text-sm transition-colors \".concat(loginType === 'email' ? 'border-b-2 border-orange-500 text-orange-600 dark:text-orange-400 dark:border-orange-400' : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'),\n                                onClick: ()=>setLoginType('email'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            size: 16,\n                                            className: \"mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Entrar com Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-6\",\n                        children: [\n                            loginType === 'email' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-200\",\n                                        htmlFor: \"email\",\n                                        children: \"Email\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1 relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-y-0 left-0 pl-3 flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"email\",\n                                                type: \"email\",\n                                                required: true,\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                className: \"block w-full text-gray-600 dark:text-gray-100 pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:focus:ring-orange-400 dark:focus:border-orange-400 bg-white dark:bg-background\",\n                                                placeholder: \"<EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                lineNumber: 141,\n                                columnNumber: 15\n                            }, this),\n                            loginType === 'username' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-200\",\n                                        htmlFor: \"username\",\n                                        children: \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1 relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-y-0 left-0 pl-3 flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                lineNumber: 169,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"username\",\n                                                type: \"text\",\n                                                required: true,\n                                                value: username,\n                                                onChange: (e)=>setUsername(e.target.value),\n                                                className: \"block w-full text-gray-600 dark:text-gray-100 pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:focus:ring-orange-400 dark:focus:border-orange-400 bg-white dark:bg-background\",\n                                                placeholder: \"Seu Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-200\",\n                                                htmlFor: \"password\",\n                                                children: \"Senha\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/forgot-password\",\n                                                className: \"text-sm text-orange-500 dark:text-orange-400 hover:text-orange-600 dark:hover:text-orange-300\",\n                                                children: \"Esqueci a senha\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1 relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-y-0 left-0 pl-3 flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"password\",\n                                                type: showPassword ? 'text' : 'password',\n                                                required: true,\n                                                value: password,\n                                                onChange: (e)=>setPassword(e.target.value),\n                                                className: \"block w-full text-gray-600 dark:text-gray-100 pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:focus:ring-orange-400 dark:focus:border-orange-400 bg-white dark:bg-background\",\n                                                placeholder: \"••••••••\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: toggleShowPassword,\n                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"w-full py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-orange-500 dark:bg-orange-600 hover:bg-orange-600 dark:hover:bg-orange-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-orange-400 transition-colors\",\n                                children: \"Entrar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-300 mr-1\",\n                                        children: \"Ainda n\\xe3o tem conta?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/subscription/signup\",\n                                        className: \"text-sm text-orange-500 dark:text-orange-400 hover:text-orange-600 dark:hover:text-orange-300 font-medium transition-colors\",\n                                        children: \"Criar uma conta\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/tutorial\",\n                            className: \"text-sm text-gray-500 dark:text-gray-300 hover:text-orange-500 dark:hover:text-orange-400 flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_HelpCircle_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    size: 16,\n                                    className: \"mr-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this),\n                                \"Precisa de ajuda? Acesse nossos tutoriais\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 text-center text-xs text-gray-500 dark:text-gray-400\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"Ao entrar, voc\\xea concorda com nossos\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/legal/termos-uso\",\n                                    className: \"text-orange-500 dark:text-orange-400 hover:underline\",\n                                    children: \"Termos de Uso\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                    lineNumber: 254,\n                                    columnNumber: 15\n                                }, this),\n                                ' ',\n                                \"e\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/legal/politica-privacidade\",\n                                    className: \"text-orange-500 dark:text-orange-400 hover:underline\",\n                                    children: \"Pol\\xedtica de Privacidade\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\(auth)\\\\login\\\\page.js\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"UJAe0PD82SJXcX7DcD4ZyGjXqOc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(auth)/login/page.js\n"));

/***/ })

});