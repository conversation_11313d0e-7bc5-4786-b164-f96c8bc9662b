"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar/index.js":
/*!***************************************************!*\
  !*** ./src/components/dashboard/Sidebar/index.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Menu,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Menu,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Menu,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/dashboard/components */ \"(app-pages-browser)/./src/app/dashboard/components.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useSchedulingPreferences */ \"(app-pages-browser)/./src/hooks/useSchedulingPreferences.js\");\n/* harmony import */ var _components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/CustomScrollArea */ \"(app-pages-browser)/./src/components/ui/CustomScrollArea.js\");\n/* harmony import */ var _hooks_useConstructionMessage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useConstructionMessage */ \"(app-pages-browser)/./src/hooks/useConstructionMessage.js\");\n/* harmony import */ var _components_construction__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/construction */ \"(app-pages-browser)/./src/components/construction/index.js\");\n/* harmony import */ var _utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/constructionUtils */ \"(app-pages-browser)/./src/utils/constructionUtils.js\");\n/* harmony import */ var _components_dashboard_CompanySelector__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/CompanySelector */ \"(app-pages-browser)/./src/components/dashboard/CompanySelector.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ModuleTitle = (param)=>{\n    let { moduleId, title, icon: Icon } = param;\n    // Exibir o logo no lugar do título do módulo, como botão para /dashboard\n    const handleLogoClick = ()=>{\n        if (true) {\n            window.location.href = '/dashboard';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6 flex justify-center items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleLogoClick,\n            style: {\n                background: 'none',\n                border: 'none',\n                padding: 0\n            },\n            className: \"focus:outline-none cursor-pointer\",\n            \"aria-label\": \"Ir para o dashboard\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/logo_horizontal_sem_fundo.png\",\n                    alt: \"High Tide Logo\",\n                    className: \"h-12 dark:invert dark:text-white transition-all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ModuleTitle;\n// Mapeamento de submenus para permissões\nconst submenuPermissionsMap = {\n    // Admin\n    'admin.introduction': 'admin.dashboard.view',\n    'admin.dashboard': 'admin.dashboard.view',\n    'admin.users': 'admin.users.view',\n    'admin.professions': [\n        'admin.professions.view',\n        'admin.profession-groups.view'\n    ],\n    'admin.bug-reports': 'admin.dashboard.view',\n    'admin.plans': 'admin.dashboard.view',\n    'admin.logs': 'admin.logs.view',\n    'admin.settings': 'admin.config.edit',\n    'admin.backup': 'admin.config.edit',\n    'admin.affiliates': 'admin.dashboard.view',\n    // ABA+\n    'abaplus.anamnese': 'abaplus.anamnese.view',\n    'abaplus.evolucoes-diarias': 'abaplus.evolucoes-diarias.view',\n    'abaplus.sessao': 'abaplus.sessao.view',\n    // Financeiro\n    'financial.invoices': 'financial.invoices.view',\n    'financial.payments': 'financial.payments.view',\n    'financial.expenses': 'financial.expenses.view',\n    'financial.reports': 'financial.reports.view',\n    'financial.cashflow': 'financial.reports.view',\n    // RH\n    'hr.employees': 'rh.employees.view',\n    'hr.payroll': 'rh.payroll.view',\n    'hr.documents': 'rh.employees.view',\n    'hr.departments': 'rh.employees.view',\n    'hr.attendance': 'rh.attendance.view',\n    'hr.benefits': 'rh.benefits.view',\n    'hr.training': 'rh.employees.view',\n    // Pessoas\n    'people.clients': 'people.clients.view',\n    'people.persons': 'people.persons.view',\n    'people.documents': 'people.documents.view',\n    'people.insurances': 'people.insurances.view',\n    'people.insurance-limits': 'people.insurance-limits.view',\n    // Agendamento\n    'scheduler.calendar': 'scheduling.calendar.view',\n    'scheduler.working-hours': 'scheduling.working-hours.view',\n    'scheduler.service-types': 'scheduling.service-types.view',\n    'scheduler.locations': 'scheduling.locations.view',\n    'scheduler.occupancy': 'scheduling.occupancy.view',\n    'scheduler.appointment-requests': 'scheduling.appointment-requests.view',\n    'scheduler.appointments-report': 'scheduling.appointments-report.view',\n    'scheduler.appointments-dashboard': 'scheduling.appointments-dashboard.view'\n};\nconst Sidebar = (param)=>{\n    let { activeModule, activeModuleTitle, isSubmenuActive, handleModuleSubmenuClick, handleBackToModules, isSidebarOpen } = param;\n    var _moduleSubmenus_activeModule;\n    _s();\n    const { can, hasModule, isAdmin } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { schedulingPreferences, isLoading: preferencesLoading } = (0,_hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__.useSchedulingPreferences)();\n    // Estado para controlar se a sidebar está colapsada\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Sidebar.useState\": ()=>{\n            if (true) {\n                const savedCollapsed = localStorage.getItem('sidebarCollapsed');\n                return savedCollapsed === 'true';\n            }\n            return false;\n        }\n    }[\"Sidebar.useState\"]);\n    // Inicializar o estado de grupos expandidos a partir do localStorage\n    const [expandedGroups, setExpandedGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Sidebar.useState\": ()=>{\n            // Verificar se estamos no cliente (browser) antes de acessar localStorage\n            if (true) {\n                const savedState = localStorage.getItem('sidebarExpandedGroups');\n                return savedState ? JSON.parse(savedState) : {};\n            }\n            return {};\n        }\n    }[\"Sidebar.useState\"]);\n    // Encontrar o objeto do módulo ativo para acessar seu ícone\n    const activeModuleObject = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.modules.find((m)=>m.id === activeModule);\n    const ModuleIcon = activeModuleObject === null || activeModuleObject === void 0 ? void 0 : activeModuleObject.icon;\n    // Função para verificar se o usuário tem permissão para ver um submenu\n    const hasPermissionForSubmenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenu]\": (moduleId, submenuId)=>{\n            var _moduleSubmenus_moduleId;\n            // Ignorar verificação para grupos, pois a permissão é verificada para cada item\n            if (submenuId === 'cadastro' || submenuId === 'configuracoes' || submenuId === 'gestao' || submenuId === 'convenios' || submenuId === 'financeiro' || submenuId === 'relatorios') {\n                return true;\n            }\n            // Buscar o submenu específico para verificar systemAdminOnly\n            const submenuItem = (_moduleSubmenus_moduleId = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId]) === null || _moduleSubmenus_moduleId === void 0 ? void 0 : _moduleSubmenus_moduleId.find({\n                \"Sidebar.useCallback[hasPermissionForSubmenu]\": (item)=>item.id === submenuId\n            }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"]);\n            // Verificar se o submenu requer SYSTEM_ADMIN\n            if (submenuItem === null || submenuItem === void 0 ? void 0 : submenuItem.systemAdminOnly) {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            // Verificação especial adicional para afiliados - apenas SYSTEM_ADMIN\n            if (submenuId === 'affiliates') {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            // Verificação especial adicional para bug-reports - apenas SYSTEM_ADMIN\n            if (submenuId === 'bug-reports') {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            const permissionKey = \"\".concat(moduleId, \".\").concat(submenuId);\n            const requiredPermission = submenuPermissionsMap[permissionKey];\n            // Se não há mapeamento de permissão, permitir acesso\n            if (!requiredPermission) return true;\n            // Administradores têm acesso a tudo (exceto itens systemAdminOnly)\n            if (isAdmin()) return true;\n            // Verificar permissão específica\n            if (Array.isArray(requiredPermission)) {\n                // Se for um array de permissões, verificar se o usuário tem pelo menos uma delas\n                return requiredPermission.some({\n                    \"Sidebar.useCallback[hasPermissionForSubmenu]\": (perm)=>can(perm)\n                }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"]);\n            } else {\n                // Se for uma única permissão, verificar normalmente\n                return can(requiredPermission);\n            }\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"], [\n        can,\n        isAdmin,\n        user.role\n    ]);\n    // Função para verificar se um submenu deve ser exibido baseado nas preferências\n    const shouldShowSubmenuByPreferences = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[shouldShowSubmenuByPreferences]\": (moduleId, submenuId)=>{\n            // Apenas verificar preferências para o módulo de agendamento\n            if (moduleId !== 'scheduler') return true;\n            // Se as preferências ainda estão carregando, não mostrar os itens que podem ser escondidos\n            if (preferencesLoading) {\n                return false;\n            }\n            // Verificar se as preferências estão carregadas\n            if (!schedulingPreferences) {\n                return false;\n            }\n            switch(submenuId){\n                case 'locations':\n                    const showLocations = schedulingPreferences.showLocations !== false;\n                    return showLocations;\n                case 'service-types':\n                    const showServiceTypes = schedulingPreferences.showServiceTypes !== false;\n                    return showServiceTypes;\n                case 'insurances':\n                    const showInsurance = schedulingPreferences.showInsurance !== false;\n                    return showInsurance;\n                case 'working-hours':\n                    const showWorkingHours = schedulingPreferences.showWorkingHours !== false;\n                    return showWorkingHours;\n                default:\n                    return true;\n            }\n        }\n    }[\"Sidebar.useCallback[shouldShowSubmenuByPreferences]\"], [\n        schedulingPreferences,\n        preferencesLoading\n    ]);\n    // Função para alternar o colapso da sidebar\n    const toggleCollapse = ()=>{\n        const newCollapsedState = !isCollapsed;\n        setIsCollapsed(newCollapsedState);\n        if (true) {\n            localStorage.setItem('sidebarCollapsed', newCollapsedState.toString());\n        }\n    };\n    // Função para alternar a expansão de um grupo\n    const toggleGroup = (groupId)=>{\n        setExpandedGroups((prev)=>({\n                ...prev,\n                [groupId]: !prev[groupId]\n            }));\n    };\n    // Verificar se algum item dentro de um grupo está ativo\n    const isGroupActive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[isGroupActive]\": (moduleId, groupItems)=>{\n            return groupItems.some({\n                \"Sidebar.useCallback[isGroupActive]\": (item)=>isSubmenuActive(moduleId, item.id)\n            }[\"Sidebar.useCallback[isGroupActive]\"]);\n        }\n    }[\"Sidebar.useCallback[isGroupActive]\"], [\n        isSubmenuActive\n    ]);\n    // Expandir automaticamente grupos que contêm o item ativo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            if (activeModule && _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) {\n                const newExpandedGroups = {\n                    ...expandedGroups\n                };\n                _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule].forEach({\n                    \"Sidebar.useEffect\": (submenu)=>{\n                        if (submenu.type === 'group' && isGroupActive(activeModule, submenu.items)) {\n                            newExpandedGroups[submenu.id] = true;\n                        }\n                    }\n                }[\"Sidebar.useEffect\"]);\n                setExpandedGroups(newExpandedGroups);\n            }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"Sidebar.useEffect\"], [\n        activeModule,\n        pathname,\n        isGroupActive\n    ]);\n    // Verificar se um item de submenu tem permissão para ser exibido\n    const hasPermissionForSubmenuItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (moduleId, submenuId)=>{\n            var _moduleSubmenus_moduleId;\n            // Buscar o item dentro dos grupos do módulo\n            let submenuItem = null;\n            // Primeiro buscar nos itens diretos\n            submenuItem = (_moduleSubmenus_moduleId = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId]) === null || _moduleSubmenus_moduleId === void 0 ? void 0 : _moduleSubmenus_moduleId.find({\n                \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (item)=>item.id === submenuId\n            }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"]);\n            // Se não encontrou, buscar dentro dos grupos\n            if (!submenuItem) {\n                for (const item of _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId] || []){\n                    if (item.type === 'group' && item.items) {\n                        submenuItem = item.items.find({\n                            \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (subItem)=>subItem.id === submenuId\n                        }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"]);\n                        if (submenuItem) break;\n                    }\n                }\n            }\n            // Verificar se o item requer SYSTEM_ADMIN\n            if (submenuItem === null || submenuItem === void 0 ? void 0 : submenuItem.systemAdminOnly) {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            return hasPermissionForSubmenu(moduleId, submenuId);\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"], [\n        hasPermissionForSubmenu,\n        user === null || user === void 0 ? void 0 : user.role\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"\".concat(isCollapsed ? 'w-16' : 'w-72', \" bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col \").concat(isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'),\n        \"aria-label\": \"Navega\\xe7\\xe3o lateral\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"flex-1 p-5\",\n                moduleColor: activeModule,\n                children: [\n                    activeModuleObject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModuleTitle, {\n                        moduleId: activeModule,\n                        title: activeModuleTitle,\n                        icon: ModuleIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 287,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_CompanySelector__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        activeModule: activeModule\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, undefined),\n                    preferencesLoading && activeModule === 'scheduler' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-gray-500 dark:text-gray-400\",\n                                children: \"Carregando...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2 flex flex-col justify-center items-center mt-8\",\n                        \"aria-labelledby\": \"sidebar-heading\",\n                        children: !(preferencesLoading && activeModule === 'scheduler') && activeModule && ((_moduleSubmenus_activeModule = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) === null || _moduleSubmenus_activeModule === void 0 ? void 0 : _moduleSubmenus_activeModule.map((submenu)=>{\n                            // Verificar se é um grupo ou um item individual\n                            if (submenu.type === 'group') {\n                                // Verificar se algum item do grupo tem permissão para ser exibido\n                                const hasAnyPermission = submenu.items.some((item)=>hasPermissionForSubmenuItem(activeModule, item.id));\n                                if (!hasAnyPermission) {\n                                    return null; // Não renderizar o grupo se nenhum item tiver permissão\n                                }\n                                const isGroupExpanded = expandedGroups[submenu.id] || false;\n                                const isAnyItemActive = isGroupActive(activeModule, submenu.items);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleGroup(submenu.id),\n                                            className: \"\\n                      group w-full flex items-center justify-between px-4 py-2.5 rounded-lg transition-all duration-300\\n                      \".concat(isAnyItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark font-medium\") : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700', \"\\n                    \"),\n                                            \"aria-expanded\": isGroupExpanded,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-left\",\n                                                    children: submenu.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-500 dark:text-gray-400\",\n                                                    children: isGroupExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 25\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 329,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isGroupExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4 pl-2 border-l border-gray-200 dark:border-gray-700 space-y-1\",\n                                            children: submenu.items.map((item)=>{\n                                                const isItemActive = isSubmenuActive(activeModule, item.id);\n                                                // Verificar permissão antes de renderizar o item\n                                                if (!hasPermissionForSubmenuItem(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não tiver permissão\n                                                }\n                                                // Verificar se o item deve ser exibido baseado nas preferências\n                                                if (!shouldShowSubmenuByPreferences(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não estiver habilitado nas preferências\n                                                }\n                                                // Verificar se o item está em construção\n                                                const itemKey = \"\".concat(activeModule, \".\").concat(item.id);\n                                                const isItemUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.isUnderConstruction)(activeModule, item.id);\n                                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.getConstructionMessage)(activeModule, item.id);\n                                                // Estilo comum para os itens\n                                                const itemClassName = \"\\n                          group w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-300\\n                          \".concat(isItemActive ? \"rounded-xl border border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                               bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                               shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n                        \");\n                                                // Se estiver em construção, usar o ConstructionButton\n                                                if (isItemUnderConstruction) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_9__.ConstructionButton, {\n                                                        className: itemClassName,\n                                                        \"aria-current\": isItemActive ? 'page' : undefined,\n                                                        title: constructionMessage.title,\n                                                        content: constructionMessage.content,\n                                                        icon: constructionMessage.icon,\n                                                        position: \"right\",\n                                                        children: [\n                                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\\n                                  \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                                \"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                    size: 18,\n                                                                    \"aria-hidden\": \"true\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 35\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 33\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                                children: item.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 31\n                                                            }, undefined)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 29\n                                                    }, undefined);\n                                                }\n                                                // Se não estiver em construção, usar o botão normal\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleModuleSubmenuClick(activeModule, item.id),\n                                                    className: itemClassName,\n                                                    \"aria-current\": isItemActive ? 'page' : undefined,\n                                                    children: [\n                                                        item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"\\n                                \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                              \"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                size: 18,\n                                                                \"aria-hidden\": \"true\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 31\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 27\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 352,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 327,\n                                    columnNumber: 17\n                                }, undefined);\n                            } else {\n                                // Renderização de itens individuais (não agrupados)\n                                const isActive = isSubmenuActive(activeModule, submenu.id);\n                                // Verificar permissão antes de renderizar o item\n                                const hasPermission = hasPermissionForSubmenu(activeModule, submenu.id);\n                                if (!hasPermission) {\n                                    return null; // Não renderizar se não tiver permissão\n                                }\n                                // Verificar se o submenu deve ser exibido baseado nas preferências\n                                const shouldShowByPreferences = shouldShowSubmenuByPreferences(activeModule, submenu.id);\n                                if (!shouldShowByPreferences) {\n                                    return null; // Não renderizar se não estiver habilitado nas preferências\n                                }\n                                // Verificar se o submenu está em construção\n                                const submenuKey = \"\".concat(activeModule, \".\").concat(submenu.id);\n                                const isSubmenuUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.isUnderConstruction)(activeModule, submenu.id);\n                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.getConstructionMessage)(activeModule, submenu.id);\n                                // Estilo comum para ambos os tipos de botões\n                                const buttonClassName = \"\\n                group w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-300\\n                \".concat(isActive ? \"rounded-xl border border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                     bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                     shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n              \");\n                                // Se estiver em construção, usar o ConstructionButton\n                                if (isSubmenuUnderConstruction) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_9__.ConstructionButton, {\n                                        className: buttonClassName,\n                                        \"aria-current\": isActive ? 'page' : undefined,\n                                        title: constructionMessage.title,\n                                        content: constructionMessage.content,\n                                        icon: constructionMessage.icon,\n                                        position: \"right\",\n                                        children: [\n                                            submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                    size: 20,\n                                                    \"aria-hidden\": \"true\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 488,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                                children: submenu.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 500,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, submenu.id, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                        lineNumber: 478,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }\n                                // Se não estiver em construção, usar o botão normal\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleModuleSubmenuClick(activeModule, submenu.id),\n                                    className: buttonClassName,\n                                    \"aria-current\": isActive ? 'page' : undefined,\n                                    children: [\n                                        submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                size: 20,\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 520,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 514,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                            children: submenu.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 526,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 507,\n                                    columnNumber: 17\n                                }, undefined);\n                            }\n                        }))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-5 border-t border-gray-100 dark:border-gray-700 mt-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToModules,\n                    className: \"\\n            group w-full py-3 px-4 rounded-lg flex items-center gap-3\\n            border-2 border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n            bg-transparent dark:bg-gray-800 hover:bg-module-\").concat(activeModule, \"-bg/10 dark:hover:bg-module-\").concat(activeModule, \"-bg-dark/10\\n            transition-all duration-300\\n          \"),\n                    \"aria-label\": \"Voltar para o dashboard principal\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            flex items-center justify-center w-8 h-8 rounded-full\\n            bg-module-\".concat(activeModule, \"-bg dark:bg-module-\").concat(activeModule, \"-bg-dark/70\\n            text-module-\").concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\\n            shadow-sm dark:shadow-md dark:shadow-black/20\\n            group-hover:scale-110 transition-transform duration-200\\n          \"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 553,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 546,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-gray-800 dark:text-gray-200\",\n                            children: \"Voltar a Tela Inicial\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 555,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 536,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 535,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 278,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Sidebar, \"VAGN2v+pOWDhD1NheeJLNqum7jI=\", false, function() {\n    return [\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__.useSchedulingPreferences\n    ];\n});\n_c1 = Sidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\nvar _c, _c1;\n$RefreshReg$(_c, \"ModuleTitle\");\n$RefreshReg$(_c1, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar/index.js\n"));

/***/ })

});