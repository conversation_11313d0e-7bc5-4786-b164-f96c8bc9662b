"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/app/dashboard/ClientHeader.js":
/*!*******************************************!*\
  !*** ./src/app/dashboard/ClientHeader.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,PanelLeftClose,PanelLeftOpen,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,PanelLeftClose,PanelLeftOpen,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,PanelLeftClose,PanelLeftOpen,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,PanelLeftClose,PanelLeftOpen,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,PanelLeftClose,PanelLeftOpen,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,PanelLeftClose,PanelLeftOpen,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,PanelLeftClose,PanelLeftOpen,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,PanelLeftClose,PanelLeftOpen,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/QuickNavContext */ \"(app-pages-browser)/./src/contexts/QuickNavContext.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(app-pages-browser)/./src/components/ThemeToggle.js\");\n/* harmony import */ var _components_chat_ChatButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/chat/ChatButton */ \"(app-pages-browser)/./src/components/chat/ChatButton.js\");\n/* harmony import */ var _components_notifications_NotificationButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/notifications/NotificationButton */ \"(app-pages-browser)/./src/components/notifications/NotificationButton.js\");\n/* harmony import */ var _components_bug_report_BugReportButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/bug-report/BugReportButton */ \"(app-pages-browser)/./src/components/bug-report/BugReportButton.js\");\n/* harmony import */ var _config_appConfig__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/config/appConfig */ \"(app-pages-browser)/./src/config/appConfig.js\");\n/* harmony import */ var _app_modules_people_services_personsService__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/modules/people/services/personsService */ \"(app-pages-browser)/./src/app/modules/people/services/personsService.js\");\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./components */ \"(app-pages-browser)/./src/app/dashboard/components.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Header Component adaptado para clientes\nconst ClientHeader = (param)=>{\n    let { toggleSidebar, isSidebarOpen, isMinimized, toggleMinimized } = param;\n    _s();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { openQuickNav } = (0,_contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__.useQuickNav)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    // Função para obter o módulo ativo a partir da URL\n    const getActiveModule = ()=>{\n        if (!pathname) return null;\n        const path = pathname.split('/');\n        if (path.length >= 3) {\n            return _components__WEBPACK_IMPORTED_MODULE_11__.modules.find((m)=>m.id === path[2]);\n        }\n        return null;\n    };\n    const activeModule = getActiveModule();\n    // Pegar primeira letra de cada nome para o avatar\n    const getInitials = ()=>{\n        var _user_login;\n        // Verificar se temos uma pessoa associada ao cliente\n        if ((user === null || user === void 0 ? void 0 : user.persons) && user.persons.length > 0 && user.persons[0].fullName) {\n            const fullName = user.persons[0].fullName;\n            const names = fullName.split(' ');\n            if (names.length === 1) return names[0].charAt(0);\n            return \"\".concat(names[0].charAt(0)).concat(names[names.length - 1].charAt(0));\n        }\n        // Fallback para o login do cliente\n        return (user === null || user === void 0 ? void 0 : (_user_login = user.login) === null || _user_login === void 0 ? void 0 : _user_login.charAt(0)) || 'C';\n    };\n    // Obter o nome completo da pessoa associada ao cliente ou o login do cliente\n    const getDisplayName = ()=>{\n        if ((user === null || user === void 0 ? void 0 : user.persons) && user.persons.length > 0 && user.persons[0].fullName) {\n            return user.persons[0].fullName;\n        }\n        return (user === null || user === void 0 ? void 0 : user.login) || 'Cliente';\n    };\n    // Obter a URL da imagem de perfil da pessoa associada ao cliente\n    const getProfileImage = ()=>{\n        if ((user === null || user === void 0 ? void 0 : user.persons) && user.persons.length > 0) {\n            // Primeiro tenta usar a URL completa se disponível\n            if (user.persons[0].profileImageFullUrl) {\n                return user.persons[0].profileImageFullUrl;\n            } else if (user.persons[0].profileImageUrl) {\n                return _app_modules_people_services_personsService__WEBPACK_IMPORTED_MODULE_10__.personsService.getProfileImageUrl(user.persons[0].id, user.persons[0].profileImageUrl);\n            }\n        }\n        return null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white dark:bg-gray-800 border-b border-gray-300 dark:border-gray-700 px-8 py-3 flex justify-between items-center sticky top-0 z-[9000]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleSidebar,\n                        className: \"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg lg:hidden text-gray-600 dark:text-gray-300 transition-colors\",\n                        \"aria-label\": isSidebarOpen ? \"Fechar menu lateral\" : \"Abrir menu lateral\",\n                        children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            size: 22,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 83,\n                            columnNumber: 28\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            size: 22,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 83,\n                            columnNumber: 65\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: pathname !== '/dashboard' && activeModule ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: activeModule.id === 'admin' ? 'w-2 h-10 rounded-full bg-slate-600 dark:bg-slate-400 block mr-2' : \"w-2 h-10 rounded-full bg-module-\".concat(activeModule.id, \"-bg dark:bg-module-\").concat(activeModule.id, \"-bg-dark block mr-2\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, undefined),\n                                activeModule.icon && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(activeModule.icon, {\n                                    size: 28,\n                                    className: \"text-module-\".concat(activeModule.id, \"-icon dark:text-module-\").concat(activeModule.id, \"-icon-dark\")\n                                }),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs uppercase tracking-wider font-semibold text-gray-900 dark:text-white\",\n                                            children: \"M\\xf3dulo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-gray-900 dark:text-white\",\n                                            children: activeModule.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/logo_horizontal_sem_fundo.png\",\n                                    alt: \"High Tide Logo\",\n                                    className: \"h-10 mr-2.5 dark:invert dark:text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute -bottom-1 right-3 text-xs text-gray-500 dark:text-gray-400 font-mono\",\n                                    children: _config_appConfig__WEBPACK_IMPORTED_MODULE_9__.APP_VERSION\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden sm:flex items-center gap-2 px-3 py-1.5 bg-purple-50 dark:bg-purple-900/30 border border-purple-200 dark:border-purple-700 rounded-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                size: 14,\n                                className: \"text-purple-600 dark:text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-purple-700 dark:text-purple-300\",\n                                children: \"\\xc1rea do Cliente\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: openQuickNav,\n                        className: \"flex items-center gap-2 py-2 px-4 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 focus:ring-primary-500 focus:border-primary-500 dark:text-gray-200 outline-none transition-colors\",\n                        \"aria-label\": \"Abrir pesquisa r\\xe1pida\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                size: 18,\n                                className: \"text-gray-400 dark:text-gray-500\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden sm:inline\",\n                                children: \"Pesquisar...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden sm:flex items-center gap-1 ml-2 px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs text-gray-500 dark:text-gray-400\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Ctrl + K\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/dashboard/scheduler/calendar'),\n                        className: \"p-2 text-gray-600 dark:text-gray-300 hover:bg-purple-100 dark:hover:bg-purple-900/30 hover:text-purple-600 dark:hover:text-purple-400 rounded-full transition-all duration-200 hover:scale-105 hover:shadow-md\",\n                        \"aria-label\": \"Calend\\xe1rio\",\n                        title: \"Ver calend\\xe1rio\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            size: 20,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/dashboard/scheduler/appointments-report'),\n                        className: \"p-2 text-gray-600 dark:text-gray-300 hover:bg-purple-100 dark:hover:bg-purple-900/30 hover:text-purple-600 dark:hover:text-purple-400 rounded-full transition-all duration-200 hover:scale-105 hover:shadow-md\",\n                        \"aria-label\": \"Meus Agendamentos\",\n                        title: \"Ver meus agendamentos\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            size: 20,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatButton__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notifications_NotificationButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_bug_report_BugReportButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__.ThemeToggle, {}, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 border-l border-gray-200 dark:border-gray-700 mx-1\",\n                        \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center gap-2 py-1 px-1 rounded-full hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                \"aria-expanded\": \"false\",\n                                \"aria-haspopup\": \"true\",\n                                \"aria-label\": \"Menu do usu\\xe1rio\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-9 w-9 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center font-medium text-purple-600 dark:text-purple-400 overflow-hidden\",\n                                        children: getProfileImage() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: getProfileImage(),\n                                            alt: \"Foto de perfil de \".concat(getDisplayName()),\n                                            className: \"h-10 w-10 rounded-full object-cover\",\n                                            onError: (e)=>{\n                                                e.target.onerror = null;\n                                                e.target.style.display = 'none';\n                                                e.target.parentNode.innerHTML = getInitials();\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, undefined) : getInitials()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:block text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-800 dark:text-gray-200 line-clamp-1\",\n                                                children: getDisplayName()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-purple-700 dark:text-purple-300 px-2 py-0.5 rounded-full inline-flex items-center mt-0.5 bg-purple-50 dark:bg-purple-900/30\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        size: 10,\n                                                        className: \"mr-1\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Cliente\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 195,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-gray-400 dark:text-gray-500 hidden md:block\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-md border border-gray-200 dark:border-gray-700 py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-150 origin-top-right\",\n                                role: \"menu\",\n                                \"aria-orientation\": \"vertical\",\n                                \"aria-labelledby\": \"user-menu-button\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-2 border-b border-gray-100 dark:border-gray-700 md:hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-800 dark:text-gray-200\",\n                                                children: getDisplayName()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                                                children: (user === null || user === void 0 ? void 0 : user.email) || '<EMAIL>'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 211,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-1 mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/dashboard/profile'),\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                                role: \"menuitem\",\n                                                children: \"Meu Perfil\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 220,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/dashboard/people/persons'),\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                                role: \"menuitem\",\n                                                children: \"Minhas Pessoas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 227,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/dashboard/scheduler/appointments-report'),\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                                role: \"menuitem\",\n                                                children: \"Meus Agendamentos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 234,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: logout,\n                                                className: \"w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 flex items-center transition-colors\",\n                                                role: \"menuitem\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"mr-2\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Sair do Sistema\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClientHeader, \"Nu+1f0C/8eoWzJzvxrSWs8iK+Vc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__.useQuickNav,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname\n    ];\n});\n_c = ClientHeader;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClientHeader);\nvar _c;\n$RefreshReg$(_c, \"ClientHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/ClientHeader.js\n"));

/***/ })

});