// src/services/twoFactorService.js
const crypto = require('crypto');
const prisma = require('../utils/prisma');
const emailService = require('./emailService');

class TwoFactorService {
  /**
   * Generate a 6-digit numeric code
   */
  generateToken() {
    return crypto.randomInt(100000, 999999).toString();
  }

  /**
   * Create and send a 2FA token via email
   */
  async sendEmailToken(userId, ipAddress, userAgent) {
    try {
      // Clean up expired tokens for this user first
      await this.cleanupExpiredTokens(userId);

      // Generate new token
      const token = this.generateToken();
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

      // Get user info
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { 
          id: true, 
          email: true, 
          fullName: true,
          companyId: true 
        }
      });


      if (!user) {
        throw new Error('User not found');
      }

      // Save token to database
      const twoFactorToken = await prisma.twoFactorToken.create({
        data: {
          userId,
          token,
          expiresAt,
          method: 'email',
          ipAddress,
          userAgent,
        }
      });

      // Send email with token
      const emailSent = await this.sendTokenEmail(user, token, ipAddress);
      
      if (!emailSent) {
        // Clean up token if email failed
        await prisma.twoFactorToken.delete({
          where: { id: twoFactorToken.id }
        });
        throw new Error('Failed to send 2FA email');
      }

      return {
        success: true,
        message: 'Token de verificação enviado para seu email',
        tokenId: twoFactorToken.id,
        expiresAt: expiresAt.toISOString()
      };
    } catch (error) {
      console.error('Error sending 2FA email token:', error);
      throw error;
    }
  }

  /**
   * Verify a 2FA token
   */
  async verifyToken(userId, token) {
    try {
      // Find valid token
      const twoFactorToken = await prisma.twoFactorToken.findFirst({
        where: {
          userId,
          token,
          verified: false,
          expiresAt: {
            gt: new Date()
          }
        }
      });

      if (!twoFactorToken) {
        return {
          success: false,
          message: 'Token inválido ou expirado'
        };
      }

      // Mark token as verified
      await prisma.twoFactorToken.update({
        where: { id: twoFactorToken.id },
        data: { verified: true }
      });

      // Clean up other tokens for this user
      await this.cleanupUserTokens(userId, twoFactorToken.id);

      return {
        success: true,
        message: 'Token verificado com sucesso',
        tokenId: twoFactorToken.id
      };
    } catch (error) {
      console.error('Error verifying 2FA token:', error);
      throw error;
    }
  }

  /**
   * Send 2FA token email
   */
  async sendTokenEmail(user, token, ipAddress = null) {
    try {
      const subject = '🔐 Seu código de verificação - High Tide Systems';
      
      const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e5e7eb; border-radius: 12px; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);">
        <div style="text-align: center; margin-bottom: 30px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 20px; display: flex; align-items: center; justify-content: center;">
            <span style="color: white; font-size: 32px;">🔐</span>
          </div>
          <h1 style="color: #1e293b; margin: 0; font-size: 24px;">Código de Verificação</h1>
        </div>
        
        <div style="background: white; padding: 25px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
            Olá <strong>${user.fullName}</strong>,
          </p>
          <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
            Detectamos uma tentativa de login em sua conta. Para continuar, digite o código de verificação abaixo:
          </p>
          
          <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; text-align: center; margin: 20px 0; border: 2px dashed #667eea;">
            <div style="font-size: 32px; font-weight: bold; color: #667eea; letter-spacing: 4px; font-family: 'Courier New', monospace;">
              ${token}
            </div>
            <p style="color: #6b7280; font-size: 14px; margin-top: 10px;">
              Este código expira em <strong>10 minutos</strong>
            </p>
          </div>
          
          ${ipAddress ? `
          <div style="background-color: #fef3cd; padding: 15px; border-radius: 6px; border-left: 4px solid #f59e0b; margin-top: 20px;">
            <p style="color: #92400e; font-size: 14px; margin: 0;">
              <strong>🌐 Tentativa de login de:</strong> ${ipAddress}
            </p>
          </div>` : ''}
        </div>
        
        <div style="background: #fef2f2; padding: 20px; border-radius: 8px; border-left: 4px solid #ef4444; margin-bottom: 20px;">
          <h3 style="color: #dc2626; margin-top: 0; font-size: 16px;">⚠️ Importante:</h3>
          <ul style="color: #7f1d1d; font-size: 14px; margin-bottom: 0; padding-left: 20px;">
            <li>Nunca compartilhe este código com outras pessoas</li>
            <li>A High Tide Systems nunca pedirá este código por telefone ou email</li>
            <li>Se você não tentou fazer login, ignore este email e altere sua senha</li>
          </ul>
        </div>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center;">
          <p style="color: #9ca3af; font-size: 12px; margin: 0;">
            Este email foi gerado automaticamente pelo sistema de segurança.<br>
            © ${new Date().getFullYear()} High Tide Systems - Todos os direitos reservados.
          </p>
        </div>
      </div>
      `;

      return await emailService.sendEmail(
        user.email,
        subject,
        html,
        {
          companyId: user.companyId,
          useSystemSMTP: true
        }
      );
    } catch (error) {
      console.error('Error sending 2FA token email:', error);
      return false;
    }
  }

  /**
   * Clean up expired tokens
   */
  async cleanupExpiredTokens(userId = null) {
    try {
      const where = {
        expiresAt: {
          lt: new Date()
        }
      };

      if (userId) {
        where.userId = userId;
      }

      const deleted = await prisma.twoFactorToken.deleteMany({ where });
      
      if (deleted.count > 0) {
        console.log(`Cleaned up ${deleted.count} expired 2FA tokens${userId ? ' for user ' + userId : ''}`);
      }
      
      return deleted.count;
    } catch (error) {
      console.error('Error cleaning up expired 2FA tokens:', error);
      return 0;
    }
  }

  /**
   * Clean up all tokens for a user except the specified one
   */
  async cleanupUserTokens(userId, excludeTokenId = null) {
    try {
      const where = { userId };
      
      if (excludeTokenId) {
        where.id = { not: excludeTokenId };
      }

      const deleted = await prisma.twoFactorToken.deleteMany({ where });
      return deleted.count;
    } catch (error) {
      console.error('Error cleaning up user 2FA tokens:', error);
      return 0;
    }
  }

  /**
   * Enable 2FA for a user
   */
  async enable2FA(userId, method = 'email') {
    try {
      await prisma.user.update({
        where: { id: userId },
        data: {
          twoFactorEnabled: true,
          twoFactorMethod: method
        }
      });

      return {
        success: true,
        message: 'Autenticação de dois fatores ativada com sucesso'
      };
    } catch (error) {
      console.error('Error enabling 2FA:', error);
      throw error;
    }
  }

  /**
   * Disable 2FA for a user
   */
  async disable2FA(userId) {
    try {
      // Clean up all tokens
      await this.cleanupUserTokens(userId);

      // Disable 2FA
      await prisma.user.update({
        where: { id: userId },
        data: {
          twoFactorEnabled: false,
          twoFactorMethod: null
        }
      });

      return {
        success: true,
        message: 'Autenticação de dois fatores desativada com sucesso'
      };
    } catch (error) {
      console.error('Error disabling 2FA:', error);
      throw error;
    }
  }

  /**
   * Check if user has 2FA enabled
   */
  async is2FAEnabled(userId) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { 
          twoFactorEnabled: true, 
          twoFactorMethod: true 
        }
      });

      return {
        enabled: user?.twoFactorEnabled || false,
        method: user?.twoFactorMethod || null
      };
    } catch (error) {
      console.error('Error checking 2FA status:', error);
      return { enabled: false, method: null };
    }
  }

  /**
   * Get user's 2FA settings
   */
  async get2FASettings(userId) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          twoFactorEnabled: true,
          twoFactorMethod: true
        }
      });

      if (!user) {
        throw new Error('User not found');
      }

      return {
        userId: user.id,
        email: user.email,
        twoFactorEnabled: user.twoFactorEnabled,
        twoFactorMethod: user.twoFactorMethod,
        availableMethods: ['email'] // Pode expandir para TOTP, SMS, etc.
      };
    } catch (error) {
      console.error('Error getting 2FA settings:', error);
      throw error;
    }
  }
}

module.exports = new TwoFactorService();