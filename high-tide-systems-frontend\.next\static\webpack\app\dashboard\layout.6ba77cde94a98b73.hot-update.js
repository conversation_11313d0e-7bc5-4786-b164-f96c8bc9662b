"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar/index.js":
/*!***************************************************!*\
  !*** ./src/components/dashboard/Sidebar/index.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,PanelLeftClose,PanelLeftOpen,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panel-left-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,PanelLeftClose,PanelLeftOpen,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panel-left-close.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,PanelLeftClose,PanelLeftOpen,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,PanelLeftClose,PanelLeftOpen,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,PanelLeftClose,PanelLeftOpen,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/dashboard/components */ \"(app-pages-browser)/./src/app/dashboard/components.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useSchedulingPreferences */ \"(app-pages-browser)/./src/hooks/useSchedulingPreferences.js\");\n/* harmony import */ var _components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/CustomScrollArea */ \"(app-pages-browser)/./src/components/ui/CustomScrollArea.js\");\n/* harmony import */ var _hooks_useConstructionMessage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useConstructionMessage */ \"(app-pages-browser)/./src/hooks/useConstructionMessage.js\");\n/* harmony import */ var _components_construction__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/construction */ \"(app-pages-browser)/./src/components/construction/index.js\");\n/* harmony import */ var _utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/constructionUtils */ \"(app-pages-browser)/./src/utils/constructionUtils.js\");\n/* harmony import */ var _components_dashboard_CompanySelector__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/CompanySelector */ \"(app-pages-browser)/./src/components/dashboard/CompanySelector.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ModuleTitle = (param)=>{\n    let { moduleId, title, icon: Icon } = param;\n    // Exibir o logo no lugar do título do módulo, como botão para /dashboard\n    const handleLogoClick = ()=>{\n        if (true) {\n            window.location.href = '/dashboard';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6 flex justify-center items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleLogoClick,\n            style: {\n                background: 'none',\n                border: 'none',\n                padding: 0\n            },\n            className: \"focus:outline-none cursor-pointer\",\n            \"aria-label\": \"Ir para o dashboard\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/logo_horizontal_sem_fundo.png\",\n                    alt: \"High Tide Logo\",\n                    className: \"h-12 dark:invert dark:text-white transition-all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ModuleTitle;\n// Mapeamento de submenus para permissões\nconst submenuPermissionsMap = {\n    // Admin\n    'admin.introduction': 'admin.dashboard.view',\n    'admin.dashboard': 'admin.dashboard.view',\n    'admin.users': 'admin.users.view',\n    'admin.professions': [\n        'admin.professions.view',\n        'admin.profession-groups.view'\n    ],\n    'admin.bug-reports': 'admin.dashboard.view',\n    'admin.plans': 'admin.dashboard.view',\n    'admin.logs': 'admin.logs.view',\n    'admin.settings': 'admin.config.edit',\n    'admin.backup': 'admin.config.edit',\n    'admin.affiliates': 'admin.dashboard.view',\n    // ABA+\n    'abaplus.anamnese': 'abaplus.anamnese.view',\n    'abaplus.evolucoes-diarias': 'abaplus.evolucoes-diarias.view',\n    'abaplus.sessao': 'abaplus.sessao.view',\n    // Financeiro\n    'financial.invoices': 'financial.invoices.view',\n    'financial.payments': 'financial.payments.view',\n    'financial.expenses': 'financial.expenses.view',\n    'financial.reports': 'financial.reports.view',\n    'financial.cashflow': 'financial.reports.view',\n    // RH\n    'hr.employees': 'rh.employees.view',\n    'hr.payroll': 'rh.payroll.view',\n    'hr.documents': 'rh.employees.view',\n    'hr.departments': 'rh.employees.view',\n    'hr.attendance': 'rh.attendance.view',\n    'hr.benefits': 'rh.benefits.view',\n    'hr.training': 'rh.employees.view',\n    // Pessoas\n    'people.clients': 'people.clients.view',\n    'people.persons': 'people.persons.view',\n    'people.documents': 'people.documents.view',\n    'people.insurances': 'people.insurances.view',\n    'people.insurance-limits': 'people.insurance-limits.view',\n    // Agendamento\n    'scheduler.calendar': 'scheduling.calendar.view',\n    'scheduler.working-hours': 'scheduling.working-hours.view',\n    'scheduler.service-types': 'scheduling.service-types.view',\n    'scheduler.locations': 'scheduling.locations.view',\n    'scheduler.occupancy': 'scheduling.occupancy.view',\n    'scheduler.appointment-requests': 'scheduling.appointment-requests.view',\n    'scheduler.appointments-report': 'scheduling.appointments-report.view',\n    'scheduler.appointments-dashboard': 'scheduling.appointments-dashboard.view'\n};\nconst Sidebar = (param)=>{\n    let { activeModule, activeModuleTitle, isSubmenuActive, handleModuleSubmenuClick, handleBackToModules, isSidebarOpen } = param;\n    var _moduleSubmenus_activeModule;\n    _s();\n    const { can, hasModule, isAdmin } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { schedulingPreferences, isLoading: preferencesLoading } = (0,_hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__.useSchedulingPreferences)();\n    // Inicializar o estado de grupos expandidos a partir do localStorage\n    const [expandedGroups, setExpandedGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Sidebar.useState\": ()=>{\n            // Verificar se estamos no cliente (browser) antes de acessar localStorage\n            if (true) {\n                const savedState = localStorage.getItem('sidebarExpandedGroups');\n                return savedState ? JSON.parse(savedState) : {};\n            }\n            return {};\n        }\n    }[\"Sidebar.useState\"]);\n    // Estado para controlar se a sidebar está minimizada\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Sidebar.useState\": ()=>{\n            if (true) {\n                const savedState = localStorage.getItem('sidebarMinimized');\n                return savedState ? JSON.parse(savedState) : false;\n            }\n            return false;\n        }\n    }[\"Sidebar.useState\"]);\n    // Encontrar o objeto do módulo ativo para acessar seu ícone\n    const activeModuleObject = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.modules.find((m)=>m.id === activeModule);\n    const ModuleIcon = activeModuleObject === null || activeModuleObject === void 0 ? void 0 : activeModuleObject.icon;\n    // Função para verificar se o usuário tem permissão para ver um submenu\n    const hasPermissionForSubmenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenu]\": (moduleId, submenuId)=>{\n            var _moduleSubmenus_moduleId;\n            // Ignorar verificação para grupos, pois a permissão é verificada para cada item\n            if (submenuId === 'cadastro' || submenuId === 'configuracoes' || submenuId === 'gestao' || submenuId === 'convenios' || submenuId === 'financeiro' || submenuId === 'relatorios') {\n                return true;\n            }\n            // Buscar o submenu específico para verificar systemAdminOnly\n            const submenuItem = (_moduleSubmenus_moduleId = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId]) === null || _moduleSubmenus_moduleId === void 0 ? void 0 : _moduleSubmenus_moduleId.find({\n                \"Sidebar.useCallback[hasPermissionForSubmenu]\": (item)=>item.id === submenuId\n            }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"]);\n            // Verificar se o submenu requer SYSTEM_ADMIN\n            if (submenuItem === null || submenuItem === void 0 ? void 0 : submenuItem.systemAdminOnly) {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            // Verificação especial adicional para afiliados - apenas SYSTEM_ADMIN\n            if (submenuId === 'affiliates') {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            // Verificação especial adicional para bug-reports - apenas SYSTEM_ADMIN\n            if (submenuId === 'bug-reports') {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            const permissionKey = \"\".concat(moduleId, \".\").concat(submenuId);\n            const requiredPermission = submenuPermissionsMap[permissionKey];\n            // Se não há mapeamento de permissão, permitir acesso\n            if (!requiredPermission) return true;\n            // Administradores têm acesso a tudo (exceto itens systemAdminOnly)\n            if (isAdmin()) return true;\n            // Verificar permissão específica\n            if (Array.isArray(requiredPermission)) {\n                // Se for um array de permissões, verificar se o usuário tem pelo menos uma delas\n                return requiredPermission.some({\n                    \"Sidebar.useCallback[hasPermissionForSubmenu]\": (perm)=>can(perm)\n                }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"]);\n            } else {\n                // Se for uma única permissão, verificar normalmente\n                return can(requiredPermission);\n            }\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"], [\n        can,\n        isAdmin,\n        user.role\n    ]);\n    // Função para verificar se um submenu deve ser exibido baseado nas preferências\n    const shouldShowSubmenuByPreferences = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[shouldShowSubmenuByPreferences]\": (moduleId, submenuId)=>{\n            // Apenas verificar preferências para o módulo de agendamento\n            if (moduleId !== 'scheduler') return true;\n            // Se as preferências ainda estão carregando, não mostrar os itens que podem ser escondidos\n            if (preferencesLoading) {\n                return false;\n            }\n            // Verificar se as preferências estão carregadas\n            if (!schedulingPreferences) {\n                return false;\n            }\n            switch(submenuId){\n                case 'locations':\n                    const showLocations = schedulingPreferences.showLocations !== false;\n                    return showLocations;\n                case 'service-types':\n                    const showServiceTypes = schedulingPreferences.showServiceTypes !== false;\n                    return showServiceTypes;\n                case 'insurances':\n                    const showInsurance = schedulingPreferences.showInsurance !== false;\n                    return showInsurance;\n                case 'working-hours':\n                    const showWorkingHours = schedulingPreferences.showWorkingHours !== false;\n                    return showWorkingHours;\n                default:\n                    return true;\n            }\n        }\n    }[\"Sidebar.useCallback[shouldShowSubmenuByPreferences]\"], [\n        schedulingPreferences,\n        preferencesLoading\n    ]);\n    // Função para alternar a expansão de um grupo\n    const toggleGroup = (groupId)=>{\n        setExpandedGroups((prev)=>({\n                ...prev,\n                [groupId]: !prev[groupId]\n            }));\n    };\n    // Função para alternar o estado de minimização da sidebar\n    const toggleMinimized = ()=>{\n        setIsMinimized((prev)=>{\n            const newState = !prev;\n            if (true) {\n                localStorage.setItem('sidebarMinimized', JSON.stringify(newState));\n            }\n            return newState;\n        });\n    };\n    // Verificar se algum item dentro de um grupo está ativo\n    const isGroupActive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[isGroupActive]\": (moduleId, groupItems)=>{\n            return groupItems.some({\n                \"Sidebar.useCallback[isGroupActive]\": (item)=>isSubmenuActive(moduleId, item.id)\n            }[\"Sidebar.useCallback[isGroupActive]\"]);\n        }\n    }[\"Sidebar.useCallback[isGroupActive]\"], [\n        isSubmenuActive\n    ]);\n    // Expandir automaticamente grupos que contêm o item ativo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            if (activeModule && _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) {\n                const newExpandedGroups = {\n                    ...expandedGroups\n                };\n                _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule].forEach({\n                    \"Sidebar.useEffect\": (submenu)=>{\n                        if (submenu.type === 'group' && isGroupActive(activeModule, submenu.items)) {\n                            newExpandedGroups[submenu.id] = true;\n                        }\n                    }\n                }[\"Sidebar.useEffect\"]);\n                setExpandedGroups(newExpandedGroups);\n            }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"Sidebar.useEffect\"], [\n        activeModule,\n        pathname,\n        isGroupActive\n    ]);\n    // Salvar estado dos grupos expandidos no localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            if (true) {\n                localStorage.setItem('sidebarExpandedGroups', JSON.stringify(expandedGroups));\n            }\n        }\n    }[\"Sidebar.useEffect\"], [\n        expandedGroups\n    ]);\n    // Verificar se um item de submenu tem permissão para ser exibido\n    const hasPermissionForSubmenuItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (moduleId, submenuId)=>{\n            var _moduleSubmenus_moduleId;\n            // Buscar o item dentro dos grupos do módulo\n            let submenuItem = null;\n            // Primeiro buscar nos itens diretos\n            submenuItem = (_moduleSubmenus_moduleId = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId]) === null || _moduleSubmenus_moduleId === void 0 ? void 0 : _moduleSubmenus_moduleId.find({\n                \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (item)=>item.id === submenuId\n            }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"]);\n            // Se não encontrou, buscar dentro dos grupos\n            if (!submenuItem) {\n                for (const item of _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId] || []){\n                    if (item.type === 'group' && item.items) {\n                        submenuItem = item.items.find({\n                            \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (subItem)=>subItem.id === submenuId\n                        }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"]);\n                        if (submenuItem) break;\n                    }\n                }\n            }\n            // Verificar se o item requer SYSTEM_ADMIN\n            if (submenuItem === null || submenuItem === void 0 ? void 0 : submenuItem.systemAdminOnly) {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            return hasPermissionForSubmenu(moduleId, submenuId);\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"], [\n        hasPermissionForSubmenu,\n        user === null || user === void 0 ? void 0 : user.role\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"\".concat(isMinimized ? 'w-16' : 'w-72', \" bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col \").concat(isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'),\n        \"aria-label\": \"Navega\\xe7\\xe3o lateral\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"flex-1 \".concat(isMinimized ? 'p-2' : 'p-5'),\n                moduleColor: activeModule,\n                children: [\n                    activeModuleObject && !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModuleTitle, {\n                        moduleId: activeModule,\n                        title: activeModuleTitle,\n                        icon: ModuleIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex \".concat(isMinimized ? 'justify-center mb-6' : 'justify-end mb-4'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toggleMinimized,\n                            className: \"p-2 rounded-lg transition-all duration-300 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 \".concat(isMinimized ? 'mt-4' : ''),\n                            \"aria-label\": isMinimized ? \"Expandir sidebar\" : \"Minimizar sidebar\",\n                            title: isMinimized ? \"Expandir sidebar\" : \"Minimizar sidebar\",\n                            children: isMinimized ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 314,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 316,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, undefined),\n                    !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_CompanySelector__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        activeModule: activeModule\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 322,\n                        columnNumber: 26\n                    }, undefined),\n                    preferencesLoading && activeModule === 'scheduler' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-gray-500 dark:text-gray-400\",\n                                children: \"Carregando...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2 flex flex-col \".concat(isMinimized ? 'items-center' : 'justify-center items-center', \" mt-8\"),\n                        \"aria-labelledby\": \"sidebar-heading\",\n                        children: !(preferencesLoading && activeModule === 'scheduler') && activeModule && ((_moduleSubmenus_activeModule = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) === null || _moduleSubmenus_activeModule === void 0 ? void 0 : _moduleSubmenus_activeModule.map((submenu)=>{\n                            // Verificar se é um grupo ou um item individual\n                            if (submenu.type === 'group') {\n                                var // Mostrar ícone do primeiro item do grupo quando minimizado\n                                _submenu_items_;\n                                // Verificar se algum item do grupo tem permissão para ser exibido\n                                const hasAnyPermission = submenu.items.some((item)=>hasPermissionForSubmenuItem(activeModule, item.id));\n                                if (!hasAnyPermission) {\n                                    return null; // Não renderizar o grupo se nenhum item tiver permissão\n                                }\n                                const isGroupExpanded = expandedGroups[submenu.id] || false;\n                                const isAnyItemActive = isGroupActive(activeModule, submenu.items);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>!isMinimized && toggleGroup(submenu.id),\n                                            className: \"\\n                      group \".concat(isMinimized ? 'w-10 h-10 flex items-center justify-center rounded-full mx-auto' : 'w-full flex items-center justify-between px-4 py-2.5 rounded-lg', \" transition-all duration-300\\n                      \").concat(isAnyItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark font-medium\") : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700', \"\\n                    \"),\n                                            \"aria-expanded\": isGroupExpanded,\n                                            title: isMinimized ? submenu.title : undefined,\n                                            children: [\n                                                !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-left\",\n                                                    children: submenu.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 38\n                                                }, undefined),\n                                                isMinimized ? ((_submenu_items_ = submenu.items[0]) === null || _submenu_items_ === void 0 ? void 0 : _submenu_items_.icon) && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(submenu.items[0].icon, {\n                                                    size: 18,\n                                                    'aria-hidden': true\n                                                }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-500 dark:text-gray-400\",\n                                                    children: isGroupExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 27\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 356,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isGroupExpanded && !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4 pl-2 border-l border-gray-200 dark:border-gray-700 space-y-1\",\n                                            children: submenu.items.map((item)=>{\n                                                const isItemActive = isSubmenuActive(activeModule, item.id);\n                                                // Verificar permissão antes de renderizar o item\n                                                if (!hasPermissionForSubmenuItem(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não tiver permissão\n                                                }\n                                                // Verificar se o item deve ser exibido baseado nas preferências\n                                                if (!shouldShowSubmenuByPreferences(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não estiver habilitado nas preferências\n                                                }\n                                                // Verificar se o item está em construção\n                                                const isItemUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.isUnderConstruction)(activeModule, item.id);\n                                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.getConstructionMessage)(activeModule, item.id);\n                                                // Estilo comum para os itens\n                                                const itemClassName = \"\\n                          group \".concat(isMinimized ? 'w-10 h-10 flex items-center justify-center rounded-full mx-auto' : 'w-full flex items-center gap-3 px-3 py-2 rounded-lg', \" transition-all duration-300\\n                          \").concat(isItemActive ? \"\".concat(isMinimized ? 'rounded-full' : 'rounded-xl', \" border border-module-\").concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                               bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                               shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n                        \");\n                                                // Se estiver em construção, usar o ConstructionButton\n                                                if (isItemUnderConstruction) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_9__.ConstructionButton, {\n                                                        className: itemClassName,\n                                                        \"aria-current\": isItemActive ? 'page' : undefined,\n                                                        title: isMinimized ? item.title : constructionMessage.title,\n                                                        content: constructionMessage.content,\n                                                        icon: constructionMessage.icon,\n                                                        position: \"right\",\n                                                        children: [\n                                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\\n                                  \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                                \"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                    size: 18,\n                                                                    \"aria-hidden\": \"true\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 35\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 33\n                                                            }, undefined),\n                                                            !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                                children: item.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 48\n                                                            }, undefined)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 29\n                                                    }, undefined);\n                                                }\n                                                // Se não estiver em construção, usar o botão normal\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleModuleSubmenuClick(activeModule, item.id),\n                                                    className: itemClassName,\n                                                    \"aria-current\": isItemActive ? 'page' : undefined,\n                                                    title: isMinimized ? item.title : undefined,\n                                                    children: [\n                                                        item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"\\n                                \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                              \"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                size: 18,\n                                                                \"aria-hidden\": \"true\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 31\n                                                        }, undefined),\n                                                        !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 46\n                                                        }, undefined)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 27\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 385,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 354,\n                                    columnNumber: 17\n                                }, undefined);\n                            } else {\n                                // Renderização de itens individuais (não agrupados)\n                                const isActive = isSubmenuActive(activeModule, submenu.id);\n                                // Verificar permissão antes de renderizar o item\n                                const hasPermission = hasPermissionForSubmenu(activeModule, submenu.id);\n                                if (!hasPermission) {\n                                    return null; // Não renderizar se não tiver permissão\n                                }\n                                // Verificar se o submenu deve ser exibido baseado nas preferências\n                                const shouldShowByPreferences = shouldShowSubmenuByPreferences(activeModule, submenu.id);\n                                if (!shouldShowByPreferences) {\n                                    return null; // Não renderizar se não estiver habilitado nas preferências\n                                }\n                                // Verificar se o submenu está em construção\n                                const isSubmenuUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.isUnderConstruction)(activeModule, submenu.id);\n                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.getConstructionMessage)(activeModule, submenu.id);\n                                // Estilo comum para ambos os tipos de botões\n                                const buttonClassName = \"\\n                group \".concat(isMinimized ? 'w-12 h-12 flex items-center justify-center rounded-full mx-auto' : 'w-full flex items-center gap-3 px-4 py-3 rounded-lg', \" transition-all duration-300\\n                \").concat(isActive ? \"\".concat(isMinimized ? 'rounded-full' : 'rounded-xl', \" border border-module-\").concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                     bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                     shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n              \");\n                                // Se estiver em construção, usar o ConstructionButton\n                                if (isSubmenuUnderConstruction) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_9__.ConstructionButton, {\n                                        className: buttonClassName,\n                                        \"aria-current\": isActive ? 'page' : undefined,\n                                        title: isMinimized ? submenu.title : constructionMessage.title,\n                                        content: constructionMessage.content,\n                                        icon: constructionMessage.icon,\n                                        position: \"right\",\n                                        children: [\n                                            submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                    size: 20,\n                                                    \"aria-hidden\": \"true\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 520,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                                children: submenu.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 532,\n                                                columnNumber: 38\n                                            }, undefined)\n                                        ]\n                                    }, submenu.id, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                        lineNumber: 510,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }\n                                // Se não estiver em construção, usar o botão normal\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleModuleSubmenuClick(activeModule, submenu.id),\n                                    className: buttonClassName,\n                                    \"aria-current\": isActive ? 'page' : undefined,\n                                    title: isMinimized ? submenu.title : undefined,\n                                    children: [\n                                        submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                size: 20,\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 553,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 547,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                            children: submenu.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 559,\n                                            columnNumber: 36\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 539,\n                                    columnNumber: 17\n                                }, undefined);\n                            }\n                        }))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(isMinimized ? 'p-2' : 'p-5', \" border-t border-gray-100 dark:border-gray-700 mt-auto\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToModules,\n                    className: \"\\n            group \".concat(isMinimized ? 'w-12 h-12 flex items-center justify-center rounded-full mx-auto' : 'w-full py-3 px-4 rounded-lg flex items-center gap-3', \"\\n            border-2 border-module-\").concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n            bg-transparent dark:bg-gray-800 hover:bg-module-\").concat(activeModule, \"-bg/10 dark:hover:bg-module-\").concat(activeModule, \"-bg-dark/10\\n            transition-all duration-300\\n          \"),\n                    \"aria-label\": \"Voltar para o dashboard principal\",\n                    title: isMinimized ? \"Voltar a Tela Inicial\" : undefined,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            flex items-center justify-center w-8 h-8 rounded-full\\n            bg-module-\".concat(activeModule, \"-bg dark:bg-module-\").concat(activeModule, \"-bg-dark/70\\n            text-module-\").concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\\n            shadow-sm dark:shadow-md dark:shadow-black/20\\n            group-hover:scale-110 transition-transform duration-200\\n          \"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_PanelLeftClose_PanelLeftOpen_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 587,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 580,\n                            columnNumber: 11\n                        }, undefined),\n                        !isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-gray-800 dark:text-gray-200\",\n                            children: \"Voltar a Tela Inicial\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 589,\n                            columnNumber: 28\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 569,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 568,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Sidebar, \"wNosui995CZ6nPqQw2gDp/n7fSs=\", false, function() {\n    return [\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__.useSchedulingPreferences\n    ];\n});\n_c1 = Sidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\nvar _c, _c1;\n$RefreshReg$(_c, \"ModuleTitle\");\n$RefreshReg$(_c1, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar/index.js\n"));

/***/ })

});