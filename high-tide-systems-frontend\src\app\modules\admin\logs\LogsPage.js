"use client";

import React, { useState, useEffect } from "react";
import {
  RefreshCw,
  FileText,
  User,
  Calendar,
  Clock,
  Filter,
  ChevronDown,
  ChevronRight,
  Download,
  Trash,
  ShieldAlert,
  UserPlus,
  UserMinus,
  Lock,
  Key,
  Settings,
  RotateCw,
  Activity,
  Info
} from "lucide-react";
import { format, parseISO, subDays } from "date-fns";
import { ptBR } from "date-fns/locale";
import { auditLogService } from "@/app/modules/admin";
import { ModuleHeader, ModuleTable } from "@/components/ui";
import LogsFilters from "@/components/admin/LogsFilters";
import ExportMenu from "@/components/ui/ExportMenu";

const LogsPage = () => {
  // Estados
  const [logs, setLogs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [totalLogs, setTotalLogs] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [expandedLogId, setExpandedLogId] = useState(null);

  // Estados de filtro
  const [filters, setFilters] = useState({
    search: "",
    startDate: format(subDays(new Date(), 7), "yyyy-MM-dd"),
    endDate: format(new Date(), "yyyy-MM-dd"),
    action: [],
    entityType: [],
    userId: []
  });
  const [error, setError] = useState("");

  // Constantes
  const ITEMS_PER_PAGE = 20;

  // Mapeamento de ações para ícones, cores e descrições amigáveis
  const actionMapping = {
    CREATE: {
      icon: <UserPlus size={16} />,
      color: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",
      label: "Criação",
      description: "Um novo item foi criado no sistema"
    },
    UPDATE: {
      icon: <RotateCw size={16} />,
      color: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",
      label: "Atualização",
      description: "Informações foram alteradas"
    },
    DELETE: {
      icon: <Trash size={16} />,
      color: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400",
      label: "Exclusão",
      description: "Um item foi removido do sistema"
    },
    LOGIN: {
      icon: <Key size={16} />,
      color: "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400",
      label: "Login",
      description: "Alguém entrou no sistema"
    },
    LOGOUT: {
      icon: <Lock size={16} />,
      color: "bg-neutral-100 text-neutral-800 dark:bg-gray-700 dark:text-neutral-300",
      label: "Logout",
      description: "Alguém saiu do sistema"
    },
    UPDATE_PERMISSIONS: {
      icon: <ShieldAlert size={16} />,
      color: "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400",
      label: "Alteração de Permissões",
      description: "As permissões de acesso foram modificadas"
    },
    UPDATE_MODULES: {
      icon: <Settings size={16} />,
      color: "bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-400",
      label: "Alteração de Módulos",
      description: "Os módulos disponíveis foram alterados"
    },
    API_ACCESS: {
      icon: <Activity size={16} />,
      color: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",
      label: "Acesso ao Sistema",
      description: "O sistema foi acessado por outro programa"
    },
    PASSWORD_RESET: {
      icon: <Lock size={16} />,
      color: "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400",
      label: "Redefinição de Senha",
      description: "Uma senha foi redefinida"
    },
    PASSWORD_CHANGE: {
      icon: <Lock size={16} />,
      color: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",
      label: "Alteração de Senha",
      description: "Um usuário alterou sua senha"
    }
  };

  // Mapeamento de entidades para nomes amigáveis
  const entityMapping = {
    User: "Usuário",
    Client: "Cliente",
    Scheduling: "Agendamento",
    WorkingHours: "Horários de Trabalho",
    Company: "Empresa",
    Settings: "Configurações",
    Document: "Documento",
    Insurance: "Convênio",
    Location: "Local",
    ServiceType: "Tipo de Serviço",
    Recurrence: "Recorrência",
    Branch: "Unidade"
  };

  // Função para tornar detalhes JSON mais legíveis
  const formatDetails = (details) => {
    if (!details) return null;

    try {
      // Se for string JSON, converte para objeto
      const detailsObj = typeof details === 'string' ? JSON.parse(details) : details;

      // Formata as alterações de maneira mais amigável
      if (detailsObj.before && detailsObj.after) {
        const changes = [];

        // Compara os objetos e identifica alterações
        Object.keys(detailsObj.after).forEach(key => {
          const oldValue = detailsObj.before[key];
          const newValue = detailsObj.after[key];

          if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
            // Substitui nomes técnicos por amigáveis
            let friendlyKey = key
              .replace(/([A-Z])/g, ' $1') // Adiciona espaço antes de letra maiúscula
              .replace(/^./, str => str.toUpperCase()) // Capitaliza primeira letra
              .trim(); // Remove espaços extras

            // Mapeamento manual de alguns campos comuns
            const keyMap = {
              "Id": "Identificador",
              "Created At": "Criado em",
              "Updated At": "Atualizado em",
              "User Id": "Usuário",
              "Email": "E-mail",
              "Password": "Senha",
              "Full Name": "Nome completo",
              "Phone": "Telefone",
              "Status": "Situação",
              "Role": "Função"
            };

            if (keyMap[friendlyKey]) {
              friendlyKey = keyMap[friendlyKey];
            }

            // Formata os valores para exibição
            let displayOldValue = oldValue === null || oldValue === undefined ? "Não definido" : String(oldValue);
            let displayNewValue = newValue === null || newValue === undefined ? "Não definido" : String(newValue);

            // Limita tamanho para valores muito longos
            if (displayOldValue.length > 50) displayOldValue = displayOldValue.substring(0, 47) + "...";
            if (displayNewValue.length > 50) displayNewValue = displayNewValue.substring(0, 47) + "...";

            changes.push({
              field: friendlyKey,
              oldValue: displayOldValue,
              newValue: displayNewValue
            });
          }
        });

        return changes;
      }

      // Se não tiver formato before/after, mostra as chaves de forma amigável
      return Object.keys(detailsObj).map(key => {
        const friendlyKey = key
          .replace(/([A-Z])/g, ' $1')
          .replace(/^./, str => str.toUpperCase())
          .trim();

        return {
          field: friendlyKey,
          value: String(detailsObj[key])
        };
      });

    } catch (e) {
      // Se falhar ao processar, retorna o texto original
      return [{ field: "Detalhes", value: details }];
    }
  };

  // Carregar logs
  const loadLogs = async (page = currentPage) => {
    setIsLoading(true);
    setError("");
    try {
      const { logs, total, pages } = await auditLogService.getLogs({
        page,
        limit: ITEMS_PER_PAGE,
        search: filters.search,
        startDate: filters.startDate,
        endDate: filters.endDate,
        action: filters.action.length > 0 ? filters.action : undefined,
        entityType: filters.entityType.length > 0 ? filters.entityType : undefined,
        userId: filters.userId.length > 0 ? filters.userId : undefined
      });

      setLogs(logs);
      setTotalLogs(total);
      setTotalPages(pages);
      setCurrentPage(page);
    } catch (error) {
      console.error("Erro ao carregar logs:", error);
      setError("Não foi possível carregar o histórico de atividades. Por favor, tente novamente mais tarde.");
      setLogs([]);
      setTotalLogs(0);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  };

  // Carregar logs ao montar o componente
  useEffect(() => {
    loadLogs(1);
  }, []);

  // Funções de manipulação
  const handleSearch = () => {
    loadLogs(1);
  };

  const handleClearFilters = () => {
    const clearedFilters = {
      search: "",
      startDate: format(subDays(new Date(), 7), "yyyy-MM-dd"),
      endDate: format(new Date(), "yyyy-MM-dd"),
      action: [],
      entityType: [],
      userId: []
    };
    setFilters(clearedFilters);
    // Resetar para a primeira página com os filtros limpos
    setTimeout(() => loadLogs(1), 100);
  };

  const handlePageChange = (page) => {
    loadLogs(page);
  };

  const toggleLogDetails = (logId) => {
    setExpandedLogId(expandedLogId === logId ? null : logId);
  };

  const exportLogs = async (format = 'xlsx') => {
    setIsExporting(true);
    setError(""); // Limpar erros anteriores
    
    try {
      // Mapear formatos do ExportMenu para formatos do exportService
      const formatMap = {
        'excel': 'xlsx',
        'xlsx': 'xlsx', 
        'pdf': 'pdf',
        'image': 'image',
        'png': 'image'
      };
      
      const exportFormat = formatMap[format] || 'xlsx';
      
      // Usar o novo método autenticado
      await auditLogService.exportLogs({
        startDate: filters.startDate,
        endDate: filters.endDate,
        action: filters.action.length > 0 ? filters.action : undefined,
        entityType: filters.entityType.length > 0 ? filters.entityType : undefined,
        userId: filters.userId.length > 0 ? filters.userId : undefined
      }, exportFormat);
      
    } catch (error) {
      console.error("Erro ao exportar logs:", error);
      const errorMessage = error.response?.data?.message || "Não foi possível exportar o histórico. Por favor, tente novamente mais tarde.";
      setError(errorMessage);
    } finally {
      setIsExporting(false);
    }
  };

  // Formatação de data para exibição
  const formatDate = (dateString) => {
    try {
      return format(parseISO(dateString), "dd 'de' MMMM 'de' yyyy 'às' HH:mm:ss", {
        locale: ptBR
      });
    } catch (e) {
      return dateString;
    }
  };

  // Função para obter o ícone e cor baseado na ação
  const getActionDisplay = (action) => {
    const mapping = actionMapping[action] || {
      icon: <FileText size={16} />,
      color: "bg-neutral-100 text-neutral-800 dark:bg-gray-700 dark:text-neutral-300",
      label: action.replace(/_/g, ' '),
      description: "Atividade no sistema"
    };

    return mapping;
  };

  // Função para criar descrição amigável da atividade
  const getActivityDescription = (log) => {
    const actionInfo = getActionDisplay(log.action);
    const entityName = entityMapping[log.entityType] || log.entityType;

    const actionMap = {
      CREATE: `criou ${entityName.toLowerCase()}`,
      UPDATE: `atualizou informações de ${entityName.toLowerCase()}`,
      DELETE: `removeu ${entityName.toLowerCase()}`,
      LOGIN: "entrou no sistema",
      LOGOUT: "saiu do sistema",
      UPDATE_PERMISSIONS: `alterou permissões de ${log.entityType === 'User' ? 'usuário' : entityName.toLowerCase()}`,
      UPDATE_MODULES: `alterou módulos de ${log.entityType === 'User' ? 'usuário' : entityName.toLowerCase()}`,
      API_ACCESS: "acessou o sistema via API",
      PASSWORD_RESET: "solicitou redefinição de senha",
      PASSWORD_CHANGE: "alterou senha"
    };

    const action = actionMap[log.action] || `realizou ação em ${entityName.toLowerCase()}`;

    return `${log.userName || "Usuário"} ${action}`;
  };

  return (
    <div className="space-y-6">
      {/* Cabeçalho da página */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <FileText size={24} className="mr-2 text-slate-600 dark:text-slate-400" />
          Histórico de Atividades
        </h1>

        <div className="flex items-center gap-2">
          <ExportMenu
            onExport={exportLogs}
            isExporting={isExporting}
            disabled={logs.length === 0}
            moduleColor="admin"
            filename="historico-atividades"
            className="text-slate-700 dark:text-slate-300"
          />
        </div>
      </div>

      {/* Filtros */}
      <ModuleHeader
        title="Filtros e Busca"
        icon={<Filter size={22} className="text-module-admin-icon dark:text-module-admin-icon-dark" />}
        description="Utilize os filtros abaixo para encontrar atividades específicas no histórico do sistema."
        moduleColor="admin"
        filters={
          <LogsFilters
            filters={filters}
            onFiltersChange={setFilters}
            onSearch={handleSearch}
            onClearFilters={handleClearFilters}
          />
        }
      />

      {/* Mensagem de erro */}
      {error && (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 text-red-600 dark:text-red-400 rounded-lg">
          {error}
        </div>
      )}

      {/* Tabela de Logs */}
      <ModuleTable
        moduleColor="admin"
        title="Histórico de Atividades"
        headerContent={
          <button
            onClick={() => loadLogs()}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-module-admin-primary dark:hover:text-module-admin-primary-dark transition-colors"
            title="Atualizar lista"
          >
            <RefreshCw size={18} />
          </button>
        }
        columns={[
          { header: 'Atividade', field: 'activity', width: '40%' },
          { header: 'Categoria', field: 'category', width: '15%' },
          { header: 'Data', field: 'date', width: '15%' },
          { header: 'Hora', field: 'time', width: '15%' },
          { header: 'Detalhes', field: 'details', width: '15%', sortable: false }
        ]}
        data={logs}
        isLoading={isLoading}
        emptyMessage="Nenhuma atividade encontrada"
        emptyIcon={<FileText size={24} />}
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalLogs}
        onPageChange={handlePageChange}
        showPagination={totalPages > 1}
        tableId="admin-logs-table"
        enableColumnToggle={true}
        defaultSortField="date"
        defaultSortDirection="desc"
        renderRow={(log, index, moduleColors, visibleColumns) => {
          const actionDisplay = getActionDisplay(log.action);
          const activityDescription = getActivityDescription(log);
          const dateParts = formatDate(log.createdAt).split(' às ');

          return (
            <React.Fragment key={log.id}>
              <tr
                className={`${moduleColors.hoverBg} cursor-pointer`}
                onClick={() => toggleLogDetails(log.id)}
              >
                {visibleColumns.includes('activity') && (
                  <td className="px-4 py-4">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-full ${actionDisplay.color} flex-shrink-0`}>
                        {actionDisplay.icon}
                      </div>
                      <div>
                        <div className="text-sm font-medium text-neutral-800 dark:text-neutral-100">
                          {activityDescription}
                        </div>
                        <div className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                          {actionDisplay.label}
                        </div>
                      </div>
                    </div>
                  </td>
                )}

                {visibleColumns.includes('category') && (
                  <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                    {entityMapping[log.entityType] || log.entityType}
                  </td>
                )}

                {visibleColumns.includes('date') && (
                  <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-neutral-400 dark:text-neutral-500 flex-shrink-0" />
                      <span>{dateParts[0]}</span>
                    </div>
                  </td>
                )}

                {visibleColumns.includes('time') && (
                  <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-neutral-400 dark:text-neutral-500 flex-shrink-0" />
                      <span>{dateParts[1]}</span>
                    </div>
                  </td>
                )}

                {visibleColumns.includes('details') && (
                  <td className="px-4 py-4 text-right">
                    <div className="flex justify-end">
                      {expandedLogId === log.id ? (
                        <ChevronDown className="h-5 w-5 text-neutral-500 dark:text-neutral-400" />
                      ) : (
                        <ChevronRight className="h-5 w-5 text-neutral-500 dark:text-neutral-400" />
                      )}
                    </div>
                  </td>
                )}
              </tr>

              {/* Detalhes expandidos */}
              {expandedLogId === log.id && (
                <tr>
                  <td colSpan={visibleColumns.length} className="p-0">
                    <div className="p-4 bg-neutral-50 dark:bg-gray-700 border-t border-neutral-200 dark:border-gray-600">
                      <div className="mb-3">
                        <p className="text-sm text-neutral-800 dark:text-neutral-200">
                          <span className="text-neutral-500 dark:text-neutral-400">O que aconteceu: </span>
                          {actionDisplay.description} {log.entityType === 'User' && log.action === 'CREATE' ? '(novo usuário foi adicionado)' : ''}
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <p className="text-xs text-neutral-500 dark:text-neutral-400">Quem realizou</p>
                          <p className="text-sm text-neutral-800 dark:text-neutral-200">{log.userName || 'Usuário do sistema'}</p>
                        </div>
                        <div>
                          <p className="text-xs text-neutral-500 dark:text-neutral-400">Quando</p>
                          <p className="text-sm text-neutral-800 dark:text-neutral-200">{formatDate(log.createdAt)}</p>
                        </div>
                        <div>
                          <p className="text-xs text-neutral-500 dark:text-neutral-400">Categoria</p>
                          <p className="text-sm text-neutral-800 dark:text-neutral-200">{entityMapping[log.entityType] || log.entityType}</p>
                        </div>
                        <div>
                          <p className="text-xs text-neutral-500 dark:text-neutral-400">Ação realizada</p>
                          <p className="text-sm text-neutral-800 dark:text-neutral-200">{actionDisplay.label}</p>
                        </div>
                      </div>

                      {log.details && (
                        <div>
                          <h5 className="font-medium text-neutral-800 dark:text-neutral-200 text-sm mb-3">O que foi alterado</h5>

                          {/* Versão mais legível dos detalhes */}
                          <div className="bg-white dark:bg-gray-800 border border-neutral-200 dark:border-gray-600 rounded-lg divide-y divide-neutral-200 dark:divide-gray-700">
                            {formatDetails(log.details).map((item, index) => (
                              <div key={index} className="px-4 py-3 text-sm">
                                {item.oldValue !== undefined && item.newValue !== undefined ? (
                                  <div className="flex flex-col">
                                    <span className="font-medium text-neutral-800 dark:text-neutral-200">{item.field}</span>
                                    <div className="flex flex-col sm:flex-row mt-1 gap-2">
                                      <div className="flex-1">
                                        <span className="text-xs text-neutral-500 dark:text-neutral-400">Antes:</span>
                                        <div className="mt-1 text-neutral-800 dark:text-neutral-300 bg-neutral-100 dark:bg-gray-700 px-2 py-1 rounded">
                                          {item.oldValue}
                                        </div>
                                      </div>
                                      <div className="flex-1">
                                        <span className="text-xs text-neutral-500 dark:text-neutral-400">Depois:</span>
                                        <div className="mt-1 text-neutral-800 dark:text-neutral-300 bg-neutral-100 dark:bg-gray-700 px-2 py-1 rounded">
                                          {item.newValue}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                ) : (
                                  <div>
                                    <span className="font-medium text-neutral-800 dark:text-neutral-200">{item.field}:</span>
                                    <span className="ml-2 text-neutral-700 dark:text-neutral-300">{item.value}</span>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </td>
                </tr>
              )}
            </React.Fragment>
          );
        }}
      />
    </div>
  );
};

export default LogsPage;