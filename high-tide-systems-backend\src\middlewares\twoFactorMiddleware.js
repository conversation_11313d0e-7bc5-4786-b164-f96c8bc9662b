// src/middlewares/twoFactorMiddleware.js
const prisma = require('../utils/prisma');
const securitySettingsController = require('../controllers/securitySettingsController');

/**
 * Middleware to check if 2FA is required for the user's company
 * and if the user has 2FA enabled
 */
const check2FARequirement = async (req, res, next) => {
  try {
    const { user } = req;

    // Skip for system admin or if user doesn't have company
    if (!user || !user.companyId || user.role === 'SYSTEM_ADMIN') {
      return next();
    }

    // Check if company requires 2FA
    const is2FARequired = await securitySettingsController.is2FARequired(user.companyId);

    if (is2FARequired) {
      // Check if user has 2FA enabled
      const userWith2FA = await prisma.user.findUnique({
        where: { id: user.id },
        select: { 
          twoFactorEnabled: true,
          twoFactorMethod: true
        }
      });

      if (!userWith2FA?.twoFactorEnabled) {
        // User is required to have 2FA but doesn't have it enabled
        return res.status(403).json({
          success: false,
          message: 'Sua empresa exige autenticação de dois fatores. Configure 2FA em sua conta para continuar.',
          code: '2FA_REQUIRED',
          requiresSetup: true
        });
      }
    }

    next();
  } catch (error) {
    console.error('Error checking 2FA requirement:', error);
    // Don't block the request if there's an error checking 2FA
    next();
  }
};

/**
 * Middleware to check 2FA requirement only for sensitive operations
 */
const check2FAForSensitiveOps = async (req, res, next) => {
  try {
    const { user } = req;

    if (!user || !user.companyId) {
      return next();
    }

    // Check if company requires 2FA
    const is2FARequired = await securitySettingsController.is2FARequired(user.companyId);

    if (is2FARequired) {
      // Check if user has 2FA enabled
      const userWith2FA = await prisma.user.findUnique({
        where: { id: user.id },
        select: { 
          twoFactorEnabled: true,
          twoFactorMethod: true
        }
      });

      if (!userWith2FA?.twoFactorEnabled) {
        return res.status(403).json({
          success: false,
          message: 'Esta operação requer autenticação de dois fatores.',
          code: '2FA_REQUIRED_FOR_OPERATION',
          requiresSetup: true
        });
      }

      // For sensitive operations, we could also require a recent 2FA verification
      // This could be implemented later if needed
    }

    next();
  } catch (error) {
    console.error('Error checking 2FA for sensitive operation:', error);
    next();
  }
};

/**
 * Check if user can enable/disable 2FA based on company policy
 */
const check2FAPolicy = async (req, res, next) => {
  try {
    const { user } = req;

    if (!user || !user.companyId) {
      return res.status(400).json({
        success: false,
        message: 'Usuário ou empresa não encontrada'
      });
    }

    // Check if company allows 2FA
    const is2FAAllowed = await securitySettingsController.is2FAAllowed(user.companyId);

    if (!is2FAAllowed) {
      return res.status(403).json({
        success: false,
        message: 'Sua empresa não permite o uso de autenticação de dois fatores.',
        code: '2FA_NOT_ALLOWED'
      });
    }

    // Check if 2FA is required and user is trying to disable it
    if (req.path.includes('/disable')) {
      const is2FARequired = await securitySettingsController.is2FARequired(user.companyId);
      
      if (is2FARequired) {
        return res.status(403).json({
          success: false,
          message: 'Sua empresa exige autenticação de dois fatores. Não é possível desativar.',
          code: '2FA_REQUIRED_CANNOT_DISABLE'
        });
      }
    }

    next();
  } catch (error) {
    console.error('Error checking 2FA policy:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
};

module.exports = {
  check2FARequirement,
  check2FAForSensitiveOps,
  check2FAPolicy
};