"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f5d5a654d81d\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUHJvZ3JhbSBGaWxlcyAoeDg2KVxcSGlnaCBUaWRlXFxoaWdoLXRpZGUtc3lzdGVtcy1mcm9udGVuZFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZjVkNWE2NTRkODFkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/exportService.js":
/*!*******************************************!*\
  !*** ./src/app/services/exportService.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   exportService: () => (/* binding */ exportService)\n/* harmony export */ });\n/* harmony import */ var file_saver__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! file-saver */ \"(app-pages-browser)/./node_modules/file-saver/dist/FileSaver.min.js\");\n/* harmony import */ var file_saver__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(file_saver__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var xlsx__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! xlsx */ \"(app-pages-browser)/./node_modules/xlsx/xlsx.mjs\");\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jspdf */ \"(app-pages-browser)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* harmony import */ var jspdf_autotable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jspdf-autotable */ \"(app-pages-browser)/./node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! html2canvas */ \"(app-pages-browser)/./node_modules/html2canvas/dist/html2canvas.js\");\n/* harmony import */ var html2canvas__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(html2canvas__WEBPACK_IMPORTED_MODULE_4__);\n// services/exportService.js\n\n\n\n // Alterado para importar como função\n\n\n\n\n// Formatação para campos comuns\nconst formatters = {\n    // Formata datas para o padrão brasileiro\n    date: (value)=>{\n        if (!value) return \"\";\n        try {\n            return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_5__.format)(new Date(value), \"dd/MM/yyyy\", {\n                locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_6__.ptBR\n            });\n        } catch (error) {\n            return value;\n        }\n    },\n    // Formata CPF (000.000.000-00)\n    cpf: (value)=>{\n        if (!value) return \"\";\n        const cpfNumbers = value.replace(/\\D/g, \"\");\n        return cpfNumbers.replace(/(\\d{3})(\\d{3})(\\d{3})(\\d{2})/, \"$1.$2.$3-$4\");\n    },\n    // Formata telefone (00) 00000-0000\n    phone: (value)=>{\n        if (!value) return \"\";\n        const phoneNumbers = value.replace(/\\D/g, \"\");\n        return phoneNumbers.replace(/(\\d{2})(\\d{5})(\\d{4})/, \"($1) $2-$3\");\n    },\n    // Formata valores booleanos\n    boolean: function(value) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {\n            trueText: \"Sim\",\n            falseText: \"Não\"\n        };\n        return value ? options.trueText : options.falseText;\n    },\n    // Formata valores monetários\n    currency: (value)=>{\n        if (value === null || value === undefined) return \"\";\n        return new Intl.NumberFormat(\"pt-BR\", {\n            style: \"currency\",\n            currency: \"BRL\"\n        }).format(value);\n    }\n};\nconst exportService = {\n    /**\r\n   * Exporta dados para XLSX ou PDF\r\n   * @param {Object[]|Object} data - Dados a serem exportados (array de objetos para tabela única ou objeto com arrays para múltiplas tabelas)\r\n   * @param {Object} options - Opções de exportação\r\n   * @param {string} options.format - Formato de exportação ('xlsx', 'pdf' ou 'image')\r\n   * @param {string} options.filename - Nome do arquivo sem extensão\r\n   * @param {Object[]} options.columns - Definição das colunas (para tabela única)\r\n   * @param {Object} options.formatOptions - Opções adicionais de formatação\r\n   * @param {string} options.title - Título principal do documento\r\n   * @param {string} options.subtitle - Subtítulo do documento\r\n   * @param {boolean} options.multiTable - Indica se os dados contêm múltiplas tabelas\r\n   * @param {Object[]} options.tables - Definição das tabelas para exportação múltipla\r\n   * @param {string} options.tables[].name - Nome da propriedade no objeto data que contém os dados da tabela\r\n   * @param {string} options.tables[].title - Título da tabela\r\n   * @param {Object[]} options.tables[].columns - Definição das colunas para esta tabela\r\n   * @returns {Promise<boolean>} - Sucesso da exportação\r\n   */ exportData: async function(data) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        try {\n            const { format = \"xlsx\", filename = \"export\", columns = [], formatOptions = {}, title = null, subtitle = null, multiTable = false, tables = [] } = options;\n            // Verifica se estamos lidando com múltiplas tabelas\n            if (multiTable && !Array.isArray(data)) {\n                // Caso de múltiplas tabelas (objeto com arrays)\n                if (format === \"xlsx\") {\n                    // Criar um workbook para múltiplas tabelas\n                    const workbook = xlsx__WEBPACK_IMPORTED_MODULE_7__.utils.book_new();\n                    // Data atual formatada para o cabeçalho\n                    const currentDate = new Date();\n                    const formattedDate = \"\".concat(currentDate.getDate().toString().padStart(2, '0'), \"/\").concat((currentDate.getMonth() + 1).toString().padStart(2, '0'), \"/\").concat(currentDate.getFullYear(), \" \\xe0s \").concat(currentDate.getHours().toString().padStart(2, '0'), \":\").concat(currentDate.getMinutes().toString().padStart(2, '0'));\n                    // Processar cada tabela definida\n                    for (const table of tables){\n                        const tableData = data[table.name];\n                        if (!tableData || !Array.isArray(tableData)) {\n                            console.warn(\"Dados para tabela \".concat(table.name, \" n\\xe3o encontrados ou n\\xe3o s\\xe3o um array\"));\n                            continue;\n                        }\n                        // Formatar os dados desta tabela\n                        const formattedTableData = tableData.map((item)=>{\n                            const formattedItem = {};\n                            table.columns.forEach((col)=>{\n                                let value = item[col.key];\n                                // Aplica formatação personalizada se especificada\n                                if (col.format && typeof col.format === 'function') {\n                                    formattedItem[col.key] = col.format(value, item);\n                                } else if (col.type && formatters[col.type]) {\n                                    formattedItem[col.key] = formatters[col.type](value, formatOptions[col.key]);\n                                } else {\n                                    formattedItem[col.key] = value !== null && value !== undefined ? value : '';\n                                }\n                            });\n                            return formattedItem;\n                        });\n                        // Preparar dados para a worksheet\n                        let worksheetData = [];\n                        // Adiciona título e subtítulo se existirem\n                        if (title) {\n                            worksheetData.push([\n                                title\n                            ]);\n                            if (table.title) {\n                                worksheetData.push([\n                                    table.title\n                                ]);\n                            }\n                            worksheetData.push([\n                                \"Gerado em: \".concat(formattedDate)\n                            ]);\n                            if (subtitle) {\n                                worksheetData.push([\n                                    subtitle\n                                ]);\n                            }\n                            worksheetData.push([]); // Linha em branco\n                        }\n                        // Adiciona os cabeçalhos\n                        const headers = table.columns.map((col)=>col.header || col.key);\n                        worksheetData.push(headers);\n                        // Adiciona os dados\n                        formattedTableData.forEach((item)=>{\n                            const row = table.columns.map((col)=>item[col.key]);\n                            worksheetData.push(row);\n                        });\n                        // Cria a worksheet\n                        const worksheet = xlsx__WEBPACK_IMPORTED_MODULE_7__.utils.aoa_to_sheet(worksheetData);\n                        // Configura os estilos\n                        if (title) {\n                            // Mescla células para o título\n                            worksheet['!merges'] = [\n                                {\n                                    s: {\n                                        r: 0,\n                                        c: 0\n                                    },\n                                    e: {\n                                        r: 0,\n                                        c: headers.length - 1\n                                    }\n                                }\n                            ];\n                            if (table.title) {\n                                worksheet['!merges'].push({\n                                    s: {\n                                        r: 1,\n                                        c: 0\n                                    },\n                                    e: {\n                                        r: 1,\n                                        c: headers.length - 1\n                                    }\n                                } // Subtítulo da tabela\n                                );\n                                worksheet['!merges'].push({\n                                    s: {\n                                        r: 2,\n                                        c: 0\n                                    },\n                                    e: {\n                                        r: 2,\n                                        c: headers.length - 1\n                                    }\n                                } // Data\n                                );\n                            } else {\n                                worksheet['!merges'].push({\n                                    s: {\n                                        r: 1,\n                                        c: 0\n                                    },\n                                    e: {\n                                        r: 1,\n                                        c: headers.length - 1\n                                    }\n                                } // Data\n                                );\n                            }\n                            if (subtitle) {\n                                worksheet['!merges'].push({\n                                    s: {\n                                        r: table.title ? 3 : 2,\n                                        c: 0\n                                    },\n                                    e: {\n                                        r: table.title ? 3 : 2,\n                                        c: headers.length - 1\n                                    }\n                                } // Subtítulo\n                                );\n                            }\n                            // Ajusta largura das colunas\n                            if (!worksheet['!cols']) worksheet['!cols'] = [];\n                            table.columns.forEach((col, idx)=>{\n                                // Calcula largura ideal para cada coluna\n                                const headerWidth = (col.header || col.key).length * 1.2;\n                                let maxDataWidth = 0;\n                                // Verifica o tamanho máximo dos dados em cada coluna\n                                formattedTableData.forEach((item)=>{\n                                    const cellValue = item[col.key];\n                                    const cellText = cellValue !== null && cellValue !== undefined ? cellValue.toString() : '';\n                                    maxDataWidth = Math.max(maxDataWidth, cellText.length);\n                                });\n                                worksheet['!cols'][idx] = {\n                                    wch: Math.max(10, Math.min(50, Math.max(headerWidth, maxDataWidth * 1.1)))\n                                };\n                            });\n                        }\n                        // Limita o nome da planilha a 31 caracteres (limite do Excel)\n                        let sheetName = table.title || table.name;\n                        // Se o nome for muito longo, cria um nome curto\n                        if (sheetName.length > 31) {\n                            // Tenta usar apenas a primeira palavra do título\n                            const firstWord = sheetName.split(' ')[0];\n                            if (firstWord.length <= 28) {\n                                sheetName = firstWord + \"...\";\n                            } else {\n                                // Se ainda for muito longo, trunca para 28 caracteres e adiciona \"...\"\n                                sheetName = sheetName.substring(0, 28) + \"...\";\n                            }\n                        }\n                        // Adiciona a worksheet ao workbook\n                        xlsx__WEBPACK_IMPORTED_MODULE_7__.utils.book_append_sheet(workbook, worksheet, sheetName);\n                    }\n                    try {\n                        // Verifica se há pelo menos uma planilha no workbook\n                        if (workbook.SheetNames && workbook.SheetNames.length > 0) {\n                            // Verifica se todos os nomes de planilhas estão dentro do limite\n                            let allNamesValid = true;\n                            for (const sheetName of workbook.SheetNames){\n                                if (sheetName.length > 31) {\n                                    console.error('Nome de planilha muito longo: \"'.concat(sheetName, '\" (').concat(sheetName.length, \" caracteres)\"));\n                                    allNamesValid = false;\n                                    break;\n                                }\n                            }\n                            if (!allNamesValid) {\n                                // Criar uma planilha padrão com mensagem de erro\n                                workbook.SheetNames = []; // Limpa as planilhas existentes\n                                workbook.Sheets = {}; // Limpa as planilhas existentes\n                                const worksheet = xlsx__WEBPACK_IMPORTED_MODULE_7__.utils.aoa_to_sheet([\n                                    [\n                                        \"Erro na exportação\"\n                                    ],\n                                    [\n                                        \"Um ou mais nomes de planilhas excedem o limite de 31 caracteres\"\n                                    ],\n                                    [\n                                        \"Por favor, contate o suporte técnico\"\n                                    ]\n                                ]);\n                                xlsx__WEBPACK_IMPORTED_MODULE_7__.utils.book_append_sheet(workbook, worksheet, \"Erro\");\n                            }\n                            // Converte para binário e salva\n                            const excelBuffer = (0,xlsx__WEBPACK_IMPORTED_MODULE_7__.write)(workbook, {\n                                bookType: \"xlsx\",\n                                type: \"array\",\n                                bookSST: false,\n                                compression: true\n                            });\n                            const blob = new Blob([\n                                excelBuffer\n                            ], {\n                                type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n                            });\n                            (0,file_saver__WEBPACK_IMPORTED_MODULE_0__.saveAs)(blob, \"\".concat(filename, \".xlsx\"));\n                            return allNamesValid;\n                        } else {\n                            console.error(\"Nenhuma planilha foi criada no workbook\");\n                            // Criar uma planilha padrão com mensagem de erro\n                            const worksheet = xlsx__WEBPACK_IMPORTED_MODULE_7__.utils.aoa_to_sheet([\n                                [\n                                    \"Erro na exportação\"\n                                ],\n                                [\n                                    \"Não foi possível gerar as planilhas com os dados fornecidos\"\n                                ],\n                                [\n                                    \"Por favor, tente novamente ou contate o suporte\"\n                                ]\n                            ]);\n                            xlsx__WEBPACK_IMPORTED_MODULE_7__.utils.book_append_sheet(workbook, worksheet, \"Erro\");\n                            const excelBuffer = (0,xlsx__WEBPACK_IMPORTED_MODULE_7__.write)(workbook, {\n                                bookType: \"xlsx\",\n                                type: \"array\",\n                                bookSST: false,\n                                compression: true\n                            });\n                            const blob = new Blob([\n                                excelBuffer\n                            ], {\n                                type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n                            });\n                            (0,file_saver__WEBPACK_IMPORTED_MODULE_0__.saveAs)(blob, \"\".concat(filename, \".xlsx\"));\n                            return false;\n                        }\n                    } catch (error) {\n                        console.error(\"Erro ao gerar arquivo Excel:\", error);\n                        try {\n                            // Tentar criar um arquivo de erro\n                            const workbook = xlsx__WEBPACK_IMPORTED_MODULE_7__.utils.book_new();\n                            const worksheet = xlsx__WEBPACK_IMPORTED_MODULE_7__.utils.aoa_to_sheet([\n                                [\n                                    \"Erro na exportação\"\n                                ],\n                                [\n                                    \"Ocorreu um erro ao gerar o arquivo Excel\"\n                                ],\n                                [\n                                    \"Detalhes do erro: \" + (error.message || \"Erro desconhecido\")\n                                ],\n                                [\n                                    \"Por favor, tente novamente ou contate o suporte\"\n                                ]\n                            ]);\n                            xlsx__WEBPACK_IMPORTED_MODULE_7__.utils.book_append_sheet(workbook, worksheet, \"Erro\");\n                            const excelBuffer = (0,xlsx__WEBPACK_IMPORTED_MODULE_7__.write)(workbook, {\n                                bookType: \"xlsx\",\n                                type: \"array\",\n                                bookSST: false,\n                                compression: true\n                            });\n                            const blob = new Blob([\n                                excelBuffer\n                            ], {\n                                type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n                            });\n                            (0,file_saver__WEBPACK_IMPORTED_MODULE_0__.saveAs)(blob, \"\".concat(filename, \"-erro.xlsx\"));\n                        } catch (fallbackError) {\n                            console.error(\"Erro ao gerar arquivo de erro:\", fallbackError);\n                        }\n                        return false;\n                    }\n                } else if (format === \"pdf\") {\n                    // Cria um novo documento PDF no tamanho A4\n                    const doc = new jspdf__WEBPACK_IMPORTED_MODULE_1__.jsPDF({\n                        orientation: \"portrait\",\n                        unit: \"mm\",\n                        format: \"a4\"\n                    });\n                    // Configurações da página\n                    const pageWidth = doc.internal.pageSize.width;\n                    const pageHeight = doc.internal.pageSize.height;\n                    const margin = 10;\n                    // Define cores do tema\n                    const themeColors = {\n                        primary: {\n                            light: [\n                                255,\n                                153,\n                                51\n                            ],\n                            dark: [\n                                255,\n                                127,\n                                0\n                            ],\n                            text: [\n                                255,\n                                255,\n                                255\n                            ] // #FFFFFF (texto branco)\n                        },\n                        secondary: {\n                            light: [\n                                255,\n                                237,\n                                213\n                            ],\n                            dark: [\n                                154,\n                                52,\n                                18\n                            ],\n                            border: [\n                                251,\n                                146,\n                                60\n                            ] // #FB923C (orange-400)\n                        }\n                    };\n                    // Desenha cabeçalho com título principal\n                    const headerHeight = subtitle ? 30 : 25;\n                    // Simulando um gradiente com múltiplos retângulos coloridos\n                    const gradientSteps = 20;\n                    const stepHeight = headerHeight / gradientSteps;\n                    for(let i = 0; i < gradientSteps; i++){\n                        const ratio = i / gradientSteps;\n                        // Interpola as cores para criar efeito de gradiente\n                        const r = themeColors.primary.light[0] + ratio * (themeColors.primary.dark[0] - themeColors.primary.light[0]);\n                        const g = themeColors.primary.light[1] + ratio * (themeColors.primary.dark[1] - themeColors.primary.light[1]);\n                        const b = themeColors.primary.light[2] + ratio * (themeColors.primary.dark[2] - themeColors.primary.light[2]);\n                        doc.setFillColor(r, g, b);\n                        doc.rect(0, i * stepHeight, pageWidth, stepHeight, 'F');\n                    }\n                    // Adiciona título no cabeçalho\n                    if (title) {\n                        doc.setFont(\"helvetica\", \"bold\");\n                        doc.setFontSize(18);\n                        doc.setTextColor(255, 255, 255); // Texto branco para o cabeçalho\n                        doc.text(title, pageWidth / 2, 15, {\n                            align: \"center\"\n                        });\n                    }\n                    // Adiciona subtítulo no cabeçalho se existir\n                    if (subtitle) {\n                        doc.setFont(\"helvetica\", \"normal\");\n                        doc.setFontSize(10);\n                        doc.setTextColor(255, 255, 255, 0.9); // Texto branco com transparência\n                        doc.text(subtitle, pageWidth / 2, 22, {\n                            align: \"center\"\n                        });\n                    }\n                    // Data de geração do relatório\n                    const currentDate = new Date();\n                    const formattedDate = \"\".concat(currentDate.getDate().toString().padStart(2, '0'), \"/\").concat((currentDate.getMonth() + 1).toString().padStart(2, '0'), \"/\").concat(currentDate.getFullYear(), \" \\xe0s \").concat(currentDate.getHours().toString().padStart(2, '0'), \":\").concat(currentDate.getMinutes().toString().padStart(2, '0'));\n                    doc.setFontSize(8);\n                    doc.text(\"Gerado em: \".concat(formattedDate), pageWidth - margin - 2, headerHeight - 3, {\n                        align: \"right\"\n                    });\n                    // Posição vertical atual para adicionar tabelas\n                    let yPosition = headerHeight + 10;\n                    // Processar cada tabela\n                    for(let tableIndex = 0; tableIndex < tables.length; tableIndex++){\n                        const table = tables[tableIndex];\n                        const tableData = data[table.name];\n                        if (!tableData || !Array.isArray(tableData)) {\n                            console.warn(\"Dados para tabela \".concat(table.name, \" n\\xe3o encontrados ou n\\xe3o s\\xe3o um array\"));\n                            continue;\n                        }\n                        // Formatar os dados desta tabela\n                        const formattedTableData = tableData.map((item)=>{\n                            const formattedItem = {};\n                            table.columns.forEach((col)=>{\n                                let value = item[col.key];\n                                // Aplica formatação personalizada se especificada\n                                if (col.format && typeof col.format === 'function') {\n                                    formattedItem[col.key] = col.format(value, item);\n                                } else if (col.type && formatters[col.type]) {\n                                    formattedItem[col.key] = formatters[col.type](value, formatOptions[col.key]);\n                                } else {\n                                    formattedItem[col.key] = value !== null && value !== undefined ? value : '';\n                                }\n                            });\n                            return formattedItem;\n                        });\n                        // Adiciona título da tabela\n                        if (table.title) {\n                            // Verifica se precisa adicionar uma nova página\n                            if (yPosition > pageHeight - 40) {\n                                doc.addPage();\n                                yPosition = 20;\n                            }\n                            doc.setFont(\"helvetica\", \"bold\");\n                            doc.setFontSize(14);\n                            doc.setTextColor(50, 50, 50);\n                            doc.text(table.title, margin, yPosition);\n                            yPosition += 10;\n                        }\n                        // Prepara os dados para a tabela\n                        const headers = table.columns.map((col)=>col.header || col.key);\n                        const rows = formattedTableData.map((item)=>{\n                            return table.columns.map((col)=>item[col.key]);\n                        });\n                        // Prepara cabeçalhos mais curtos para prevenir quebras\n                        const shortHeaders = headers.map((header)=>{\n                            // Substituições específicas para cabeçalhos problemáticos\n                            const replacements = {\n                                'Nome Completo': 'Nome',\n                                'Data de Nascimento': 'Nascimento',\n                                'Data de Cadastro': 'Cadastro',\n                                'Relacionamento': 'Relação',\n                                'Telefone': 'Telefone'\n                            };\n                            return replacements[header] || header;\n                        });\n                        // Adiciona a tabela\n                        (0,jspdf_autotable__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(doc, {\n                            startY: yPosition,\n                            head: [\n                                shortHeaders\n                            ],\n                            body: rows,\n                            theme: \"grid\",\n                            headStyles: {\n                                fillColor: themeColors.primary.dark,\n                                textColor: themeColors.primary.text,\n                                fontStyle: \"bold\",\n                                halign: \"center\",\n                                fontSize: 9,\n                                cellPadding: {\n                                    top: 3,\n                                    right: 2,\n                                    bottom: 3,\n                                    left: 2\n                                },\n                                lineWidth: 0.1,\n                                minCellWidth: 15,\n                                overflow: 'linebreak'\n                            },\n                            styles: {\n                                fontSize: 9,\n                                cellPadding: {\n                                    top: 2,\n                                    right: 2,\n                                    bottom: 2,\n                                    left: 2\n                                },\n                                overflow: \"linebreak\",\n                                lineColor: [\n                                    220,\n                                    220,\n                                    220\n                                ],\n                                lineWidth: 0.1\n                            },\n                            tableWidth: 'auto',\n                            bodyStyles: {\n                                minCellHeight: 10\n                            },\n                            alternateRowStyles: {\n                                fillColor: [\n                                    252,\n                                    252,\n                                    252\n                                ]\n                            },\n                            columnStyles: table.columns.reduce((styles, col, index)=>{\n                                // Definir larguras mínimas específicas para evitar quebras nos títulos\n                                const minWidths = {\n                                    'fullName': 30,\n                                    'cpf': 20,\n                                    'email': 28,\n                                    'phone': 20,\n                                    'birthDate': 20,\n                                    'gender': 15,\n                                    'relationship': 20,\n                                    'createdAt': 20,\n                                    'active': 15\n                                };\n                                // Define a largura baseada na configuração ou no mínimo predefinido\n                                if (col.key && minWidths[col.key]) {\n                                    styles[index] = {\n                                        ...styles[index],\n                                        cellWidth: minWidths[col.key],\n                                        overflow: 'linebreak'\n                                    };\n                                } else if (col.width) {\n                                    styles[index] = {\n                                        ...styles[index],\n                                        cellWidth: col.width\n                                    };\n                                }\n                                // Aplica o alinhamento se definido\n                                if (col.align) {\n                                    styles[index] = {\n                                        ...styles[index],\n                                        halign: col.align\n                                    };\n                                }\n                                return styles;\n                            }, {}),\n                            margin: {\n                                top: yPosition,\n                                left: margin,\n                                right: margin,\n                                bottom: margin + 15\n                            },\n                            didDrawPage: function(data) {\n                                // Adiciona rodapé colorido em cada página\n                                doc.setFillColor(240, 240, 240);\n                                doc.rect(0, pageHeight - 15, pageWidth, 15, 'F');\n                                // Linha sutil acima do rodapé\n                                doc.setDrawColor(200, 200, 200);\n                                doc.line(0, pageHeight - 15, pageWidth, pageHeight - 15);\n                                // Adiciona numeração de páginas no rodapé\n                                doc.setFontSize(8);\n                                doc.setTextColor(100, 100, 100);\n                                doc.text(\"P\\xe1gina \".concat(data.pageNumber, \" de \").concat(data.pageCount), pageWidth - margin - 2, pageHeight - 5, {\n                                    align: \"right\"\n                                });\n                                // Adiciona nome do sistema no rodapé\n                                doc.setTextColor(80, 80, 80);\n                                doc.setFontSize(8);\n                                doc.text(\"High Tide Systems\", margin + 2, pageHeight - 5);\n                            }\n                        });\n                        // Atualiza a posição Y para a próxima tabela\n                        // Obtém a última posição Y após desenhar a tabela\n                        yPosition = doc.lastAutoTable.finalY + 15;\n                    }\n                    // Salva o documento\n                    doc.save(\"\".concat(filename, \".pdf\"));\n                    return true;\n                } else if (format === \"image\") {\n                    try {\n                        // Criar um elemento temporário para renderizar todas as tabelas\n                        const tempContainer = document.createElement('div');\n                        tempContainer.style.position = 'absolute';\n                        tempContainer.style.left = '-9999px';\n                        tempContainer.style.top = '-9999px';\n                        tempContainer.style.width = '1000px'; // Largura fixa para melhor qualidade\n                        tempContainer.style.backgroundColor = '#ffffff';\n                        tempContainer.style.padding = '20px';\n                        tempContainer.style.fontFamily = 'Arial, sans-serif';\n                        tempContainer.style.color = '#333333';\n                        tempContainer.style.boxSizing = 'border-box';\n                        // Adicionar título principal e subtítulo\n                        if (title) {\n                            const titleElement = document.createElement('h1');\n                            titleElement.textContent = title;\n                            titleElement.style.color = '#FF7F00'; // Cor laranja do tema\n                            titleElement.style.marginBottom = '5px';\n                            titleElement.style.fontSize = '28px';\n                            titleElement.style.fontWeight = 'bold';\n                            titleElement.style.textAlign = 'center';\n                            tempContainer.appendChild(titleElement);\n                        }\n                        if (subtitle) {\n                            const subtitleElement = document.createElement('p');\n                            subtitleElement.textContent = subtitle;\n                            subtitleElement.style.color = '#666666';\n                            subtitleElement.style.marginBottom = '20px';\n                            subtitleElement.style.fontSize = '16px';\n                            subtitleElement.style.textAlign = 'center';\n                            tempContainer.appendChild(subtitleElement);\n                        }\n                        // Data atual formatada\n                        const currentDate = new Date();\n                        const formattedDate = \"\".concat(currentDate.getDate().toString().padStart(2, '0'), \"/\").concat((currentDate.getMonth() + 1).toString().padStart(2, '0'), \"/\").concat(currentDate.getFullYear(), \" \\xe0s \").concat(currentDate.getHours().toString().padStart(2, '0'), \":\").concat(currentDate.getMinutes().toString().padStart(2, '0'));\n                        const dateElement = document.createElement('p');\n                        dateElement.textContent = \"Gerado em: \".concat(formattedDate);\n                        dateElement.style.color = '#666666';\n                        dateElement.style.marginBottom = '30px';\n                        dateElement.style.fontSize = '12px';\n                        dateElement.style.textAlign = 'center';\n                        tempContainer.appendChild(dateElement);\n                        // Processar cada tabela\n                        for (const table of tables){\n                            const tableData = data[table.name];\n                            if (!tableData || !Array.isArray(tableData)) {\n                                console.warn(\"Dados para tabela \".concat(table.name, \" n\\xe3o encontrados ou n\\xe3o s\\xe3o um array\"));\n                                continue;\n                            }\n                            // Adicionar título da seção\n                            const sectionTitle = document.createElement('h2');\n                            sectionTitle.textContent = table.title || table.name;\n                            sectionTitle.style.color = '#FF7F00'; // Cor laranja do tema\n                            sectionTitle.style.marginTop = '30px';\n                            sectionTitle.style.marginBottom = '15px';\n                            sectionTitle.style.fontSize = '20px';\n                            sectionTitle.style.fontWeight = 'bold';\n                            tempContainer.appendChild(sectionTitle);\n                            // Criar a tabela\n                            const tableElement = document.createElement('table');\n                            tableElement.style.width = '100%';\n                            tableElement.style.borderCollapse = 'collapse';\n                            tableElement.style.marginBottom = '30px';\n                            tableElement.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';\n                            // Criar o cabeçalho da tabela\n                            const thead = document.createElement('thead');\n                            const headerRow = document.createElement('tr');\n                            headerRow.style.backgroundColor = '#FF7F00'; // Cor laranja do tema\n                            headerRow.style.color = '#ffffff';\n                            table.columns.forEach((col)=>{\n                                const th = document.createElement('th');\n                                th.textContent = col.header || col.key;\n                                th.style.padding = '10px';\n                                th.style.textAlign = col.align || 'left';\n                                th.style.fontWeight = 'bold';\n                                th.style.fontSize = '14px';\n                                th.style.borderBottom = '2px solid #FF9933';\n                                headerRow.appendChild(th);\n                            });\n                            thead.appendChild(headerRow);\n                            tableElement.appendChild(thead);\n                            // Criar o corpo da tabela\n                            const tbody = document.createElement('tbody');\n                            tableData.forEach((item, rowIndex)=>{\n                                const row = document.createElement('tr');\n                                row.style.backgroundColor = rowIndex % 2 === 0 ? '#ffffff' : '#f9f9f9';\n                                row.style.borderBottom = '1px solid #eeeeee';\n                                table.columns.forEach((col)=>{\n                                    const td = document.createElement('td');\n                                    // Formatar o valor da célula\n                                    let value = item[col.key];\n                                    // Aplica formatação personalizada se especificada\n                                    if (col.format && typeof col.format === 'function') {\n                                        value = col.format(value, item);\n                                    } else if (col.type && formatters[col.type]) {\n                                        value = formatters[col.type](value, {});\n                                    }\n                                    td.textContent = value !== undefined && value !== null ? value : '';\n                                    td.style.padding = '8px 10px';\n                                    td.style.fontSize = '13px';\n                                    td.style.textAlign = col.align || 'left';\n                                    // Estilização especial para status\n                                    if (col.key === 'active' || col.key === 'status') {\n                                        if (td.textContent === 'Ativo') {\n                                            td.style.color = '#10B981'; // Verde para ativo\n                                            td.style.fontWeight = 'bold';\n                                        } else if (td.textContent === 'Inativo') {\n                                            td.style.color = '#DC2626'; // Vermelho para inativo\n                                            td.style.fontWeight = 'bold';\n                                        } else if (td.textContent === 'Crítica') {\n                                            td.style.color = '#DC2626'; // Vermelho para crítica\n                                            td.style.fontWeight = 'bold';\n                                        } else if (td.textContent === 'Alta') {\n                                            td.style.color = '#F59E0B'; // Âmbar para alta\n                                            td.style.fontWeight = 'bold';\n                                        } else if (td.textContent === 'Média') {\n                                            td.style.color = '#10B981'; // Verde para média\n                                            td.style.fontWeight = 'bold';\n                                        } else if (td.textContent === 'Baixa') {\n                                            td.style.color = '#3B82F6'; // Azul para baixa\n                                            td.style.fontWeight = 'bold';\n                                        }\n                                    }\n                                    row.appendChild(td);\n                                });\n                                tbody.appendChild(row);\n                            });\n                            tableElement.appendChild(tbody);\n                            tempContainer.appendChild(tableElement);\n                        }\n                        // Adicionar rodapé com data de geração\n                        const footer = document.createElement('div');\n                        footer.style.fontSize = '12px';\n                        footer.style.color = '#666666';\n                        footer.style.textAlign = 'right';\n                        footer.style.marginTop = '20px';\n                        footer.style.borderTop = '1px solid #eeeeee';\n                        footer.style.paddingTop = '10px';\n                        footer.textContent = \"High Tide Systems\";\n                        tempContainer.appendChild(footer);\n                        // Adicionar o container temporário ao DOM\n                        document.body.appendChild(tempContainer);\n                        // Usar html2canvas para converter a tabela em uma imagem\n                        return new Promise((resolve)=>{\n                            // Adicionar um pequeno atraso para garantir que o DOM esteja pronto\n                            setTimeout(()=>{\n                                html2canvas__WEBPACK_IMPORTED_MODULE_4___default()(tempContainer, {\n                                    scale: 2,\n                                    useCORS: true,\n                                    allowTaint: true,\n                                    backgroundColor: '#ffffff',\n                                    logging: false,\n                                    letterRendering: true\n                                }).then((canvas)=>{\n                                    // Remover o container temporário\n                                    document.body.removeChild(tempContainer);\n                                    // Converter o canvas para blob e salvar\n                                    canvas.toBlob((blob)=>{\n                                        (0,file_saver__WEBPACK_IMPORTED_MODULE_0__.saveAs)(blob, \"\".concat(filename, \".png\"));\n                                        resolve(true);\n                                    }, 'image/png');\n                                }).catch((error)=>{\n                                    console.error(\"Erro ao converter tabelas para imagem:\", error);\n                                    // Remover o container temporário em caso de erro\n                                    if (document.body.contains(tempContainer)) {\n                                        document.body.removeChild(tempContainer);\n                                    }\n                                    resolve(false);\n                                });\n                            }, 100); // Pequeno atraso para garantir que o DOM esteja pronto\n                        });\n                    } catch (error) {\n                        console.error(\"Erro na exportação para imagem:\", error);\n                        return false;\n                    }\n                } else {\n                    console.error(\"Formato não suportado para exportação de múltiplas tabelas\");\n                    return false;\n                }\n            } else {\n                // Caso de tabela única (array de objetos)\n                // Se não forem fornecidas colunas, usa as chaves dos objetos\n                const tableColumns = columns.length > 0 ? columns : data.length > 0 ? Object.keys(data[0]).map((key)=>({\n                        key,\n                        header: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),\n                        format: null\n                    })) : [];\n                // Formata os dados com base nas colunas definidas\n                const formattedData = data.map((item)=>{\n                    const formattedItem = {};\n                    tableColumns.forEach((col)=>{\n                        let value = item[col.key];\n                        // Aplica formatação personalizada se especificada\n                        if (col.format && typeof col.format === 'function') {\n                            formattedItem[col.key] = col.format(value, item);\n                        } else if (col.type && formatters[col.type]) {\n                            formattedItem[col.key] = formatters[col.type](value, formatOptions[col.key]);\n                        } else {\n                            formattedItem[col.key] = value !== null && value !== undefined ? value : '';\n                        }\n                    });\n                    return formattedItem;\n                });\n                // Exportação em formato XLSX\n                if (format === \"xlsx\" || format === \"excel\") {\n                    return exportExcel(formattedData, tableColumns, filename, title);\n                } else if (format === \"pdf\") {\n                    return exportPdf(formattedData, tableColumns, filename, title, subtitle);\n                } else if (format === \"image\" || format === \"png\") {\n                    // Para exportação de imagem, precisamos de um elemento DOM\n                    // Vamos criar uma tabela temporária e convertê-la em imagem\n                    return exportImage(formattedData, tableColumns, filename, title, subtitle);\n                } else {\n                    console.error(\"Formato de exportação não suportado:\", format);\n                    return false;\n                }\n            }\n        } catch (error) {\n            console.error(\"Erro na exportação:\", error);\n            return false;\n        }\n    },\n    /**\r\n   * Exporta dados da API com os filtros atuais\r\n   * @param {string} endpoint - Endpoint da API\r\n   * @param {Object} filters - Filtros a serem aplicados\r\n   * @param {Object} options - Opções de exportação\r\n   * @returns {Promise<boolean>} - Sucesso da exportação\r\n   */ exportFromApi: async function(endpoint) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        try {\n            var _response_data, _response_data1;\n            const { format = \"xlsx\", filename = \"export\", columns = [] } = options;\n            // Se a API suporta exportação direta\n            if (options.useApiExport) {\n                const response = await _utils_api__WEBPACK_IMPORTED_MODULE_3__.api.get(\"\".concat(endpoint, \"/export\"), {\n                    params: {\n                        ...filters,\n                        format\n                    },\n                    responseType: format === \"pdf\" ? \"arraybuffer\" : \"blob\"\n                });\n                const blob = new Blob([\n                    response.data\n                ], {\n                    type: format === \"pdf\" ? \"application/pdf\" : \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n                });\n                (0,file_saver__WEBPACK_IMPORTED_MODULE_0__.saveAs)(blob, \"\".concat(filename, \".\").concat(format));\n                return true;\n            }\n            // Se a API não suporta exportação, faz uma consulta normal e exporta os dados no cliente\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_3__.api.get(endpoint, {\n                params: filters\n            });\n            // Tenta obter os dados de diferentes formatos de resposta\n            const data = ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.data) || ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.items) || response.data || [];\n            // Exporta os dados\n            return exportService.exportData(data, {\n                ...options,\n                format,\n                filename,\n                columns\n            });\n        } catch (error) {\n            console.error(\"Erro ao exportar dados da API:\", error);\n            return false;\n        }\n    }\n};\n// Funções auxiliares de exportação\n/**\r\n * Exporta dados para Excel com formatação aprimorada\r\n */ function exportExcel(data, columns, filename, title) {\n    try {\n        // Cria um novo workbook\n        const workbook = xlsx__WEBPACK_IMPORTED_MODULE_7__.utils.book_new();\n        // Se tiver título, adiciona como primeira linha\n        let worksheetData = [];\n        // Data atual formatada\n        const currentDate = new Date();\n        const formattedDate = \"\".concat(currentDate.getDate().toString().padStart(2, '0'), \"/\").concat((currentDate.getMonth() + 1).toString().padStart(2, '0'), \"/\").concat(currentDate.getFullYear(), \" \\xe0s \").concat(currentDate.getHours().toString().padStart(2, '0'), \":\").concat(currentDate.getMinutes().toString().padStart(2, '0'));\n        // Adiciona título e data\n        if (title) {\n            worksheetData.push([\n                title\n            ]);\n            worksheetData.push([\n                \"Gerado em: \".concat(formattedDate)\n            ]);\n            worksheetData.push([]); // Linha em branco\n        }\n        // Adiciona os cabeçalhos\n        const headers = columns.map((col)=>col.header || col.key);\n        worksheetData.push(headers);\n        // Adiciona os dados\n        data.forEach((item)=>{\n            const row = columns.map((col)=>item[col.key]);\n            worksheetData.push(row);\n        });\n        // Cria a worksheet\n        const worksheet = xlsx__WEBPACK_IMPORTED_MODULE_7__.utils.aoa_to_sheet(worksheetData);\n        // Configura os estilos (apenas as propriedades básicas são suportadas no xlsx)\n        if (title) {\n            // Mescla células para o título\n            worksheet['!merges'] = [\n                {\n                    s: {\n                        r: 0,\n                        c: 0\n                    },\n                    e: {\n                        r: 0,\n                        c: headers.length - 1\n                    }\n                },\n                {\n                    s: {\n                        r: 1,\n                        c: 0\n                    },\n                    e: {\n                        r: 1,\n                        c: headers.length - 1\n                    }\n                }\n            ];\n            // Ajusta largura das colunas\n            if (!worksheet['!cols']) worksheet['!cols'] = [];\n            columns.forEach((col, idx)=>{\n                // Calcula largura ideal para cada coluna\n                const headerWidth = (col.header || col.key).length * 1.2;\n                let maxDataWidth = 0;\n                // Verifica o tamanho máximo dos dados em cada coluna\n                data.forEach((item)=>{\n                    const cellValue = item[col.key];\n                    const cellText = cellValue !== null && cellValue !== undefined ? cellValue.toString() : '';\n                    maxDataWidth = Math.max(maxDataWidth, cellText.length);\n                });\n                worksheet['!cols'][idx] = {\n                    wch: Math.max(10, Math.min(50, Math.max(headerWidth, maxDataWidth * 1.1)))\n                };\n            });\n        }\n        // Adiciona a worksheet ao workbook\n        xlsx__WEBPACK_IMPORTED_MODULE_7__.utils.book_append_sheet(workbook, worksheet, \"Dados\");\n        // Converte para binário e salva\n        const excelBuffer = (0,xlsx__WEBPACK_IMPORTED_MODULE_7__.write)(workbook, {\n            bookType: \"xlsx\",\n            type: \"array\",\n            bookSST: false,\n            compression: true\n        });\n        const blob = new Blob([\n            excelBuffer\n        ], {\n            type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"\n        });\n        (0,file_saver__WEBPACK_IMPORTED_MODULE_0__.saveAs)(blob, \"\".concat(filename, \".xlsx\"));\n        return true;\n    } catch (error) {\n        console.error(\"Erro na exportação Excel:\", error);\n        return false;\n    }\n}\n/**\r\n * Exporta dados para PDF com design aprimorado\r\n */ function exportPdf(data, columns, filename, title, subtitle) {\n    try {\n        // Cria um novo documento PDF no tamanho A4\n        const doc = new jspdf__WEBPACK_IMPORTED_MODULE_1__.jsPDF({\n            orientation: \"portrait\",\n            unit: \"mm\",\n            format: \"a4\"\n        });\n        // Configurações da página\n        const pageWidth = doc.internal.pageSize.width;\n        const pageHeight = doc.internal.pageSize.height;\n        const margin = 10;\n        const contentWidth = pageWidth - margin * 2;\n        // Define cores do tema baseadas no estilo do módulo people (laranja)\n        const themeColors = {\n            primary: {\n                light: [\n                    255,\n                    153,\n                    51\n                ],\n                dark: [\n                    255,\n                    127,\n                    0\n                ],\n                text: [\n                    255,\n                    255,\n                    255\n                ] // #FFFFFF (texto branco)\n            },\n            secondary: {\n                light: [\n                    255,\n                    237,\n                    213\n                ],\n                dark: [\n                    154,\n                    52,\n                    18\n                ],\n                border: [\n                    251,\n                    146,\n                    60\n                ] // #FB923C (orange-400)\n            }\n        };\n        // ===== CABEÇALHO COM GRADIENTE =====\n        // Desenha um retângulo para o cabeçalho com gradiente\n        const headerHeight = subtitle ? 30 : 25;\n        // Simulando um gradiente com múltiplos retângulos coloridos\n        const gradientSteps = 20;\n        const stepHeight = headerHeight / gradientSteps;\n        for(let i = 0; i < gradientSteps; i++){\n            const ratio = i / gradientSteps;\n            // Interpola as cores para criar efeito de gradiente\n            const r = themeColors.primary.light[0] + ratio * (themeColors.primary.dark[0] - themeColors.primary.light[0]);\n            const g = themeColors.primary.light[1] + ratio * (themeColors.primary.dark[1] - themeColors.primary.light[1]);\n            const b = themeColors.primary.light[2] + ratio * (themeColors.primary.dark[2] - themeColors.primary.light[2]);\n            doc.setFillColor(r, g, b);\n            doc.rect(0, i * stepHeight, pageWidth, stepHeight, 'F');\n        }\n        // Adiciona um pequeno ícone ou logotipo\n        doc.setDrawColor(255, 255, 255);\n        doc.setFillColor(255, 255, 255);\n        // Adiciona título no cabeçalho\n        if (title) {\n            doc.setFont(\"helvetica\", \"bold\");\n            doc.setFontSize(18);\n            doc.setTextColor(255, 255, 255); // Texto branco para o cabeçalho\n            doc.text(title, pageWidth / 2, 15, {\n                align: \"center\"\n            });\n        }\n        // Adiciona subtítulo no cabeçalho se existir\n        if (subtitle) {\n            doc.setFont(\"helvetica\", \"normal\");\n            doc.setFontSize(10);\n            doc.setTextColor(255, 255, 255, 0.9); // Texto branco com transparência\n            doc.text(subtitle, pageWidth / 2, 22, {\n                align: \"center\"\n            });\n        }\n        // Data de geração do relatório\n        const currentDate = new Date();\n        const formattedDate = \"\".concat(currentDate.getDate().toString().padStart(2, '0'), \"/\").concat((currentDate.getMonth() + 1).toString().padStart(2, '0'), \"/\").concat(currentDate.getFullYear(), \" \\xe0s \").concat(currentDate.getHours().toString().padStart(2, '0'), \":\").concat(currentDate.getMinutes().toString().padStart(2, '0'));\n        doc.setFontSize(8);\n        doc.text(\"Gerado em: \".concat(formattedDate), pageWidth - margin - 2, headerHeight - 3, {\n            align: \"right\"\n        });\n        // ===== CONTEÚDO DA TABELA =====\n        // Prepara os dados para a tabela\n        const headers = columns.map((col)=>col.header || col.key);\n        const rows = data.map((item)=>{\n            return columns.map((col)=>item[col.key]);\n        });\n        // Prepara cabeçalhos mais curtos para prevenir quebras\n        const shortHeaders = headers.map((header)=>{\n            // Substituições específicas para cabeçalhos problemáticos\n            const replacements = {\n                'Nome Completo': 'Nome',\n                'Data de Nascimento': 'Nascimento',\n                'Data de Cadastro': 'Cadastro',\n                'Relacionamento': 'Relação',\n                'Telefone': 'Telefone'\n            };\n            return replacements[header] || header;\n        });\n        // Adiciona a tabela com estilo aprimorado\n        (0,jspdf_autotable__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(doc, {\n            startY: headerHeight + 5,\n            head: [\n                shortHeaders\n            ],\n            body: rows,\n            theme: \"grid\",\n            headStyles: {\n                fillColor: themeColors.primary.dark,\n                textColor: themeColors.primary.text,\n                fontStyle: \"bold\",\n                halign: \"center\",\n                fontSize: 9,\n                cellPadding: {\n                    top: 3,\n                    right: 2,\n                    bottom: 3,\n                    left: 2\n                },\n                lineWidth: 0.1,\n                minCellWidth: 15,\n                overflow: 'linebreak' // Evita quebra de linha nos cabeçalhos\n            },\n            styles: {\n                fontSize: 9,\n                cellPadding: {\n                    top: 2,\n                    right: 2,\n                    bottom: 2,\n                    left: 2\n                },\n                overflow: \"linebreak\",\n                lineColor: [\n                    220,\n                    220,\n                    220\n                ],\n                lineWidth: 0.1\n            },\n            tableWidth: 'auto',\n            bodyStyles: {\n                minCellHeight: 10\n            },\n            alternateRowStyles: {\n                fillColor: [\n                    252,\n                    252,\n                    252\n                ]\n            },\n            columnStyles: columns.reduce((styles, col, index)=>{\n                // Definir larguras mínimas específicas para evitar quebras nos títulos\n                const minWidths = {\n                    'fullName': 30,\n                    'cpf': 20,\n                    'email': 28,\n                    'phone': 20,\n                    'birthDate': 20,\n                    'gender': 15,\n                    'relationship': 20,\n                    'createdAt': 20,\n                    'active': 15\n                };\n                // Define a largura baseada na configuração ou no mínimo predefinido\n                if (col.key && minWidths[col.key]) {\n                    styles[index] = {\n                        ...styles[index],\n                        cellWidth: minWidths[col.key],\n                        overflow: 'linebreak'\n                    };\n                } else if (col.width) {\n                    styles[index] = {\n                        ...styles[index],\n                        cellWidth: col.width\n                    };\n                }\n                // Aplica o alinhamento se definido\n                if (col.align) {\n                    styles[index] = {\n                        ...styles[index],\n                        halign: col.align\n                    };\n                }\n                return styles;\n            }, {}),\n            margin: {\n                top: headerHeight + 5,\n                left: margin,\n                right: margin,\n                bottom: margin + 15\n            },\n            didDrawPage: function(data) {\n                // Adiciona rodapé colorido em cada página\n                doc.setFillColor(240, 240, 240);\n                doc.rect(0, pageHeight - 15, pageWidth, 15, 'F');\n                // Linha sutil acima do rodapé\n                doc.setDrawColor(200, 200, 200);\n                doc.line(0, pageHeight - 15, pageWidth, pageHeight - 15);\n                // Adiciona numeração de páginas no rodapé\n                doc.setFontSize(8);\n                doc.setTextColor(100, 100, 100);\n                doc.text(\"P\\xe1gina \".concat(data.pageNumber, \" de \").concat(data.pageCount), pageWidth - margin - 2, pageHeight - 5, {\n                    align: \"right\"\n                });\n                // Adiciona nome do sistema no rodapé\n                doc.setTextColor(80, 80, 80);\n                doc.setFontSize(8);\n                doc.text(\"High Tide Systems\", margin + 2, pageHeight - 5);\n            },\n            didParseCell: function(data) {\n                // Aplicar formatação específica para células com status\n                const col = columns[data.column.index];\n                if (col && col.key === 'active') {\n                    if (data.cell.section === 'body') {\n                        if (data.cell.raw === 'Ativo') {\n                            data.cell.styles.fontStyle = 'bold';\n                            data.cell.styles.textColor = [\n                                16,\n                                185,\n                                129\n                            ]; // Cor verde para Ativo\n                        } else if (data.cell.raw === 'Inativo') {\n                            data.cell.styles.fontStyle = 'bold';\n                            data.cell.styles.textColor = [\n                                220,\n                                38,\n                                38\n                            ]; // Cor vermelha para Inativo\n                        }\n                    }\n                }\n            }\n        });\n        // Salva o documento\n        doc.save(\"\".concat(filename, \".pdf\"));\n        return true;\n    } catch (error) {\n        console.error(\"Erro na exportação PDF:\", error);\n        return false;\n    }\n}\n/**\r\n * Exporta dados para imagem (PNG) criando uma tabela HTML temporária\r\n */ function exportImage(data, columns, filename, title, subtitle) {\n    try {\n        // Criar um elemento temporário para renderizar a tabela\n        const tempContainer = document.createElement('div');\n        tempContainer.style.position = 'absolute';\n        tempContainer.style.left = '-9999px';\n        tempContainer.style.top = '-9999px';\n        tempContainer.style.width = '1000px'; // Largura fixa para melhor qualidade\n        tempContainer.style.backgroundColor = '#ffffff';\n        tempContainer.style.padding = '20px';\n        tempContainer.style.fontFamily = 'Arial, sans-serif';\n        tempContainer.style.color = '#333333';\n        tempContainer.style.boxSizing = 'border-box';\n        // Adicionar título e subtítulo\n        if (title) {\n            const titleElement = document.createElement('h2');\n            titleElement.textContent = title;\n            titleElement.style.color = '#FF7F00'; // Cor laranja do tema\n            titleElement.style.marginBottom = '5px';\n            titleElement.style.fontSize = '24px';\n            titleElement.style.fontWeight = 'bold';\n            titleElement.style.textAlign = 'center';\n            tempContainer.appendChild(titleElement);\n        }\n        if (subtitle) {\n            const subtitleElement = document.createElement('p');\n            subtitleElement.textContent = subtitle;\n            subtitleElement.style.color = '#666666';\n            subtitleElement.style.marginBottom = '20px';\n            subtitleElement.style.fontSize = '14px';\n            subtitleElement.style.textAlign = 'center';\n            tempContainer.appendChild(subtitleElement);\n        }\n        // Criar a tabela\n        const table = document.createElement('table');\n        table.style.width = '100%';\n        table.style.borderCollapse = 'collapse';\n        table.style.marginBottom = '20px';\n        table.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';\n        // Criar o cabeçalho da tabela\n        const thead = document.createElement('thead');\n        const headerRow = document.createElement('tr');\n        headerRow.style.backgroundColor = '#FF7F00'; // Cor laranja do tema\n        headerRow.style.color = '#ffffff';\n        columns.forEach((col)=>{\n            const th = document.createElement('th');\n            th.textContent = col.header || col.key;\n            th.style.padding = '10px';\n            th.style.textAlign = 'left';\n            th.style.fontWeight = 'bold';\n            th.style.fontSize = '14px';\n            th.style.borderBottom = '2px solid #FF9933';\n            headerRow.appendChild(th);\n        });\n        thead.appendChild(headerRow);\n        table.appendChild(thead);\n        // Criar o corpo da tabela\n        const tbody = document.createElement('tbody');\n        data.forEach((item, rowIndex)=>{\n            const row = document.createElement('tr');\n            row.style.backgroundColor = rowIndex % 2 === 0 ? '#ffffff' : '#f9f9f9';\n            row.style.borderBottom = '1px solid #eeeeee';\n            columns.forEach((col)=>{\n                const td = document.createElement('td');\n                td.textContent = item[col.key] !== undefined && item[col.key] !== null ? item[col.key] : '';\n                td.style.padding = '8px 10px';\n                td.style.fontSize = '13px';\n                // Estilização especial para status\n                if (col.key === 'active') {\n                    if (item[col.key] === 'Ativo') {\n                        td.style.color = '#10B981'; // Verde para ativo\n                        td.style.fontWeight = 'bold';\n                    } else if (item[col.key] === 'Inativo') {\n                        td.style.color = '#DC2626'; // Vermelho para inativo\n                        td.style.fontWeight = 'bold';\n                    }\n                }\n                row.appendChild(td);\n            });\n            tbody.appendChild(row);\n        });\n        table.appendChild(tbody);\n        tempContainer.appendChild(table);\n        // Adicionar rodapé com data de geração\n        const footer = document.createElement('div');\n        footer.style.fontSize = '12px';\n        footer.style.color = '#666666';\n        footer.style.textAlign = 'right';\n        footer.style.marginTop = '10px';\n        const currentDate = new Date();\n        const formattedDate = \"\".concat(currentDate.getDate().toString().padStart(2, '0'), \"/\").concat((currentDate.getMonth() + 1).toString().padStart(2, '0'), \"/\").concat(currentDate.getFullYear(), \" \\xe0s \").concat(currentDate.getHours().toString().padStart(2, '0'), \":\").concat(currentDate.getMinutes().toString().padStart(2, '0'));\n        footer.textContent = \"Gerado em: \".concat(formattedDate, \" | High Tide Systems\");\n        tempContainer.appendChild(footer);\n        // Adicionar o container temporário ao DOM\n        document.body.appendChild(tempContainer);\n        // Usar html2canvas para converter a tabela em uma imagem\n        return new Promise((resolve)=>{\n            // Adicionar um pequeno atraso para garantir que o DOM esteja pronto\n            setTimeout(()=>{\n                html2canvas__WEBPACK_IMPORTED_MODULE_4___default()(tempContainer, {\n                    scale: 2,\n                    useCORS: true,\n                    allowTaint: true,\n                    backgroundColor: '#ffffff',\n                    logging: false,\n                    letterRendering: true\n                }).then((canvas)=>{\n                    // Remover o container temporário\n                    document.body.removeChild(tempContainer);\n                    // Converter o canvas para blob e salvar\n                    canvas.toBlob((blob)=>{\n                        (0,file_saver__WEBPACK_IMPORTED_MODULE_0__.saveAs)(blob, \"\".concat(filename, \".png\"));\n                        resolve(true);\n                    }, 'image/png');\n                }).catch((error)=>{\n                    console.error(\"Erro ao converter tabela para imagem:\", error);\n                    // Remover o container temporário em caso de erro\n                    if (document.body.contains(tempContainer)) {\n                        document.body.removeChild(tempContainer);\n                    }\n                    resolve(false);\n                });\n            }, 100); // Pequeno atraso para garantir que o DOM esteja pronto\n        });\n    } catch (error) {\n        console.error(\"Erro na exportação para imagem:\", error);\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/exportService.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/ModuleMaskedInput.js":
/*!************************************************!*\
  !*** ./src/components/ui/ModuleMaskedInput.js ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_input_mask__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-input/mask */ \"(app-pages-browser)/./node_modules/@react-input/mask/module/helpers-BtaZ0NTN.js\");\n/* harmony import */ var _react_input_mask__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-input/mask */ \"(app-pages-browser)/./node_modules/@react-input/mask/module/InputMask.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n/**\r\n * Componente de input com máscara que segue o design system dos módulos\r\n * @param {string} props.moduleColor - Cor do módulo (default, people, scheduler, admin, financial)\r\n * @param {string} props.mask - Máscara a ser aplicada (ex: \"999.999.999-99\")\r\n * @param {object} props.replacement - Objeto de substituição para a máscara (ex: { 9: /[0-9]/ })\r\n * @param {boolean} props.error - Se o input está em estado de erro\r\n * @param {string} props.className - Classes adicionais\r\n */ const ModuleMaskedInput = (param)=>{\n    let { moduleColor = \"default\", mask, replacement, error, className, value, onChange, ...props } = param;\n    _s();\n    // Estado interno para armazenar o valor formatado\n    const [formattedValue, setFormattedValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Efeito para formatar o valor inicial\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModuleMaskedInput.useEffect\": ()=>{\n            const safeValue = value || '';\n            try {\n                // Verificar se replacement é válido\n                if (replacement && typeof replacement === 'object') {\n                    const formatted = (0,_react_input_mask__WEBPACK_IMPORTED_MODULE_3__.f)(safeValue, mask, replacement);\n                    setFormattedValue(formatted);\n                } else {\n                    setFormattedValue(safeValue);\n                }\n            } catch (error) {\n                // Se falhar, usar o valor original\n                console.warn(\"Erro ao formatar valor inicial:\", error);\n                setFormattedValue(safeValue);\n            }\n        }\n    }[\"ModuleMaskedInput.useEffect\"], [\n        value,\n        mask,\n        replacement\n    ]);\n    // Função para lidar com mudanças no input\n    const handleChange = (e)=>{\n        setFormattedValue(e.target.value);\n        if (onChange) {\n            onChange(e);\n        }\n    };\n    // Mapeamento de cores por módulo\n    const moduleColors = {\n        default: {\n            focusRing: 'focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-primary-500 focus-visible:!border-primary-500 dark:focus-visible:!ring-primary-400 dark:focus-visible:!border-primary-400',\n            errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',\n            errorBorder: 'border-red-300 dark:border-red-700'\n        },\n        people: {\n            focusRing: 'focus:outline-none focus:ring-1 focus:ring-module-people-border focus:border-module-people-border dark:focus:ring-module-people-border-dark dark:focus:border-module-people-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-people-border focus-visible:!border-module-people-border dark:focus-visible:!ring-module-people-border-dark dark:focus-visible:!border-module-people-border-dark',\n            errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',\n            errorBorder: 'border-red-300 dark:border-red-700'\n        },\n        scheduler: {\n            focusRing: 'focus:outline-none focus:ring-1 focus:ring-module-scheduler-border focus:border-module-scheduler-border dark:focus:ring-module-scheduler-border-dark dark:focus:border-module-scheduler-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-scheduler-border focus-visible:!border-module-scheduler-border dark:focus-visible:!ring-module-scheduler-border-dark dark:focus-visible:!border-module-scheduler-border-dark',\n            errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',\n            errorBorder: 'border-red-300 dark:border-red-700'\n        },\n        admin: {\n            focusRing: 'focus:outline-none focus:ring-1 focus:ring-module-admin-border focus:border-module-admin-border dark:focus:ring-module-admin-border-dark dark:focus:border-module-admin-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-admin-border focus-visible:!border-module-admin-border dark:focus-visible:!ring-module-admin-border-dark dark:focus-visible:!border-module-admin-border-dark',\n            errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',\n            errorBorder: 'border-red-300 dark:border-red-700'\n        },\n        financial: {\n            focusRing: 'focus:outline-none focus:ring-1 focus:ring-module-financial-border focus:border-module-financial-border dark:focus:ring-module-financial-border-dark dark:focus:border-module-financial-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-financial-border focus-visible:!border-module-financial-border dark:focus-visible:!ring-module-financial-border-dark dark:focus-visible:!border-module-financial-border-dark',\n            errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',\n            errorBorder: 'border-red-300 dark:border-red-700'\n        }\n    };\n    // Classes base para todos os inputs\n    const baseClasses = 'w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 text-sm transition-colors duration-200 bg-white dark:bg-gray-700 dark:text-gray-200 outline-none';\n    // Selecionar as classes de cor com base no módulo\n    const colorClasses = moduleColors[moduleColor] || moduleColors.default;\n    // Construir as classes finais\n    const finalClasses = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, error ? colorClasses.errorBorder : '', error ? colorClasses.errorRing : colorClasses.focusRing, className);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_react_input_mask__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        mask: mask,\n        replacement: replacement,\n        className: finalClasses,\n        value: formattedValue,\n        onChange: handleChange,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleMaskedInput.js\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ModuleMaskedInput, \"pUruQ5vXlMynWPfFE0U698+h+Mk=\");\n_c = ModuleMaskedInput;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ModuleMaskedInput);\nvar _c;\n$RefreshReg$(_c, \"ModuleMaskedInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/ModuleMaskedInput.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/Toast.js":
/*!************************************!*\
  !*** ./src/components/ui/Toast.js ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* __next_internal_client_entry_do_not_use__ Toast auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Configurações de ícones e cores por tipo de toast\nconst TOAST_CONFIG = {\n    [_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.TOAST_TYPES.SUCCESS]: {\n        icon: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        bgClass: 'bg-success-50 dark:bg-success-700/30 border-success-200 dark:border-success-700/50',\n        iconClass: 'text-success-500 dark:text-success-300',\n        textClass: 'text-success-800 dark:text-success-50',\n        progressClass: 'bg-success-500 dark:bg-success-400'\n    },\n    [_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.TOAST_TYPES.ERROR]: {\n        icon: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        bgClass: 'bg-error-50 dark:bg-error-700/30 border-error-200 dark:border-error-700/50',\n        iconClass: 'text-error-500 dark:text-error-300',\n        textClass: 'text-error-800 dark:text-error-50',\n        progressClass: 'bg-error-500 dark:bg-error-400'\n    },\n    [_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.TOAST_TYPES.WARNING]: {\n        icon: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        bgClass: 'bg-warning-50 dark:bg-warning-700/30 border-warning-200 dark:border-warning-700/50',\n        iconClass: 'text-warning-500 dark:text-warning-300',\n        textClass: 'text-warning-800 dark:text-warning-50',\n        progressClass: 'bg-warning-500 dark:bg-warning-400'\n    },\n    [_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.TOAST_TYPES.INFO]: {\n        icon: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        bgClass: 'bg-primary-50 dark:bg-primary-700/30 border-primary-200 dark:border-primary-700/50',\n        iconClass: 'text-primary-500 dark:text-primary-300',\n        textClass: 'text-primary-800 dark:text-primary-50',\n        progressClass: 'bg-primary-500 dark:bg-primary-400'\n    }\n};\nconst Toast = (param)=>{\n    let { toast, onRemove } = param;\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [exiting, setExiting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const progressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const timerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Configuração com base no tipo\n    const config = TOAST_CONFIG[toast.type] || TOAST_CONFIG[_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_2__.TOAST_TYPES.INFO];\n    const Icon = config.icon;\n    // Efeito para animação de entrada\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Toast.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"Toast.useEffect.timer\": ()=>{\n                    setIsVisible(true);\n                }\n            }[\"Toast.useEffect.timer\"], 10);\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timer)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], []);\n    // Efeito para animação da barra de progresso usando CSS transitions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Toast.useEffect\": ()=>{\n            if (toast.duration > 0 && progressRef.current) {\n                // Configura a transição CSS para animação suave\n                progressRef.current.style.transition = \"width \".concat(toast.duration, \"ms linear\");\n                // Force reflow para garantir que a transição inicie corretamente\n                void progressRef.current.getBoundingClientRect();\n                // Inicia a animação\n                progressRef.current.style.width = '0%';\n                // Timer para remoção automática do toast - usando requestAnimationFrame para evitar conflitos\n                timerRef.current = setTimeout({\n                    \"Toast.useEffect\": ()=>{\n                        requestAnimationFrame({\n                            \"Toast.useEffect\": ()=>{\n                                handleClose();\n                            }\n                        }[\"Toast.useEffect\"]);\n                    }\n                }[\"Toast.useEffect\"], toast.duration);\n            }\n            return ({\n                \"Toast.useEffect\": ()=>{\n                    if (timerRef.current) {\n                        clearTimeout(timerRef.current);\n                        timerRef.current = null;\n                    }\n                }\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        toast.duration\n    ]);\n    // Função para fechar o toast\n    const handleClose = ()=>{\n        // Limpar timer se existir para evitar múltiplas execuções\n        if (timerRef.current) {\n            clearTimeout(timerRef.current);\n            timerRef.current = null;\n        }\n        setExiting(true);\n        setTimeout(()=>{\n            onRemove(toast.id);\n        }, 300);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\\n        max-w-md w-full rounded-lg border px-4 py-3 shadow-md dark:shadow-black/50 relative \\n        transform transition-all duration-300 ease-out\\n        \".concat(config.bgClass, \"\\n        \").concat(isVisible && !exiting ? 'translate-y-0 opacity-100 scale-100' : exiting ? 'translate-y-2 opacity-0 scale-95' : 'translate-y-2 opacity-0 scale-95', \"\\n      \"),\n        style: {\n            backdropFilter: 'blur(8px)',\n            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)'\n        },\n        role: \"alert\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 \".concat(config.iconClass),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: \"h-5 w-5\",\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\Toast.js\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\Toast.js\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-3 flex-grow \".concat(config.textClass),\n                        children: [\n                            toast.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-semibold\",\n                                children: toast.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\Toast.js\",\n                                lineNumber: 126,\n                                columnNumber: 27\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm \".concat(toast.title ? 'mt-0.5 font-normal' : 'font-medium'),\n                                children: toast.message\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\Toast.js\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\Toast.js\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-4 flex-shrink-0 flex\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"inline-flex rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-primary-500 \".concat(config.iconClass, \" hover:opacity-75 transition-opacity duration-150\"),\n                            onClick: handleClose,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"sr-only\",\n                                    children: \"Fechar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\Toast.js\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-5 w-5\",\n                                    \"aria-hidden\": \"true\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\Toast.js\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\Toast.js\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\Toast.js\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\Toast.js\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, undefined),\n            toast.duration > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 right-0 h-1 bg-gray-200/30 dark:bg-gray-700/40 rounded-b-lg overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: progressRef,\n                    className: \"h-full \".concat(config.progressClass),\n                    style: {\n                        width: '100%'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\Toast.js\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\Toast.js\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\Toast.js\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Toast, \"eSmjBOPcNxCr0JG1aKOPyBHqUT0=\");\n_c = Toast;\nvar _c;\n$RefreshReg$(_c, \"Toast\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/Toast.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/ToastContext.js":
/*!**************************************!*\
  !*** ./src/contexts/ToastContext.js ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TOAST_TYPES: () => (/* binding */ TOAST_TYPES),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* __next_internal_client_entry_do_not_use__ TOAST_TYPES,ToastProvider,useToast auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n// Tipos de Toast\nconst TOAST_TYPES = {\n    SUCCESS: 'success',\n    ERROR: 'error',\n    WARNING: 'warning',\n    INFO: 'info'\n};\n// Estado inicial\nconst initialState = {\n    toasts: []\n};\n// Ações\nconst ACTIONS = {\n    ADD_TOAST: 'ADD_TOAST',\n    REMOVE_TOAST: 'REMOVE_TOAST',\n    REMOVE_ALL_TOASTS: 'REMOVE_ALL_TOASTS'\n};\n// Reducer para manipular os toasts\nconst toastReducer = (state, action)=>{\n    switch(action.type){\n        case ACTIONS.ADD_TOAST:\n            return {\n                ...state,\n                toasts: [\n                    ...state.toasts,\n                    action.payload\n                ]\n            };\n        case ACTIONS.REMOVE_TOAST:\n            return {\n                ...state,\n                toasts: state.toasts.filter((toast)=>toast.id !== action.payload)\n            };\n        case ACTIONS.REMOVE_ALL_TOASTS:\n            return {\n                ...state,\n                toasts: []\n            };\n        default:\n            return state;\n    }\n};\n// Criar o contexto\nconst ToastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst ToastProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(toastReducer, initialState);\n    // Função para adicionar um toast\n    const addToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ToastProvider.useCallback[addToast]\": function(messageOrOptions) {\n            let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : TOAST_TYPES.INFO, duration = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 5000;\n            const id = (0,uuid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n            // Definir o toast baseado no tipo de entrada\n            let toast;\n            if (typeof messageOrOptions === 'object' && messageOrOptions !== null) {\n                // É um objeto de opções\n                toast = {\n                    id,\n                    title: messageOrOptions.title || null,\n                    message: messageOrOptions.message || '',\n                    type: messageOrOptions.type || type,\n                    duration: typeof messageOrOptions.duration === 'number' ? messageOrOptions.duration : duration,\n                    createdAt: new Date()\n                };\n            } else {\n                // É uma string simples\n                toast = {\n                    id,\n                    title: null,\n                    message: String(messageOrOptions),\n                    type,\n                    duration,\n                    createdAt: new Date()\n                };\n            }\n            // Adicionar o toast\n            dispatch({\n                type: ACTIONS.ADD_TOAST,\n                payload: toast\n            });\n            // Configurar remoção automática se duration > 0\n            if (toast.duration > 0) {\n                setTimeout({\n                    \"ToastProvider.useCallback[addToast]\": ()=>{\n                        removeToast(id);\n                    }\n                }[\"ToastProvider.useCallback[addToast]\"], toast.duration);\n            }\n            return id;\n        }\n    }[\"ToastProvider.useCallback[addToast]\"], []);\n    // Função para remover um toast específico\n    const removeToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ToastProvider.useCallback[removeToast]\": (id)=>{\n            dispatch({\n                type: ACTIONS.REMOVE_TOAST,\n                payload: id\n            });\n        }\n    }[\"ToastProvider.useCallback[removeToast]\"], []);\n    // Função para remover todos os toasts\n    const removeAllToasts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ToastProvider.useCallback[removeAllToasts]\": ()=>{\n            dispatch({\n                type: ACTIONS.REMOVE_ALL_TOASTS\n            });\n        }\n    }[\"ToastProvider.useCallback[removeAllToasts]\"], []);\n    // Funções de conveniência para cada tipo de toast\n    const toast_success = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ToastProvider.useCallback[toast_success]\": function(messageOrOptions) {\n            let duration = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3000;\n            // Reduzir duração padrão dos toasts de sucesso para evitar interferências\n            return addToast(messageOrOptions, TOAST_TYPES.SUCCESS, duration);\n        }\n    }[\"ToastProvider.useCallback[toast_success]\"], [\n        addToast\n    ]);\n    const toast_error = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ToastProvider.useCallback[toast_error]\": (messageOrOptions, duration)=>{\n            return addToast(messageOrOptions, TOAST_TYPES.ERROR, duration);\n        }\n    }[\"ToastProvider.useCallback[toast_error]\"], [\n        addToast\n    ]);\n    const toast_warning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ToastProvider.useCallback[toast_warning]\": (messageOrOptions, duration)=>{\n            return addToast(messageOrOptions, TOAST_TYPES.WARNING, duration);\n        }\n    }[\"ToastProvider.useCallback[toast_warning]\"], [\n        addToast\n    ]);\n    const toast_info = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ToastProvider.useCallback[toast_info]\": (messageOrOptions, duration)=>{\n            return addToast(messageOrOptions, TOAST_TYPES.INFO, duration);\n        }\n    }[\"ToastProvider.useCallback[toast_info]\"], [\n        addToast\n    ]);\n    // Valores que serão expostos pelo contexto\n    const contextValue = {\n        toasts: state.toasts,\n        addToast,\n        removeToast,\n        removeAllToasts,\n        toast_success,\n        toast_error,\n        toast_warning,\n        toast_info\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\contexts\\\\ToastContext.js\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ToastProvider, \"enPXEUnchue9S0ZY4aZmLtWi7tU=\");\n_c = ToastProvider;\nconst useToast = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (!context) {\n        throw new Error('useToast deve ser usado dentro de um ToastProvider');\n    }\n    return context;\n};\n_s1(useToast, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ToastProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ToastContext.js\n"));

/***/ })

});