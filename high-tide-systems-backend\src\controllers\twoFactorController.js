// src/controllers/twoFactorController.js
const { validationResult } = require('express-validator');
const twoFactorService = require('../services/twoFactorService');
const securitySettingsController = require('./securitySettingsController');

class TwoFactorController {
  /**
   * Get user's 2FA settings
   */
  static async getSettings(req, res) {
    try {
      const userId = req.user.id;
      const companyId = req.user.companyId;

      const settings = await twoFactorService.get2FASettings(userId);
      
      // Add company policy information
      let companyPolicy = {};
      if (companyId) {
        const is2FARequired = await securitySettingsController.is2FARequired(companyId);
        const is2FAAllowed = await securitySettingsController.is2FAAllowed(companyId);
        const allowedMethods = await securitySettingsController.getAllowed2FAMethods(companyId);
        
        companyPolicy = {
          required: is2FARequired,
          allowed: is2FAAllowed,
          allowedMethods
        };
      }

      res.json({
        success: true,
        data: {
          ...settings,
          companyPolicy
        }
      });
    } catch (error) {
      console.error('Error getting 2FA settings:', error);
      res.status(500).json({ 
        success: false,
        message: 'Erro interno do servidor' 
      });
    }
  }

  /**
   * Enable 2FA for the user
   */
  static async enable(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const userId = req.user.id;
      const { method = 'email' } = req.body;

      // Check if 2FA is already enabled
      const currentStatus = await twoFactorService.is2FAEnabled(userId);
      if (currentStatus.enabled) {
        return res.status(400).json({
          success: false,
          message: 'Autenticação de dois fatores já está ativada'
        });
      }

      // Enable 2FA
      const result = await twoFactorService.enable2FA(userId, method);

      // Log the action
      await securitySettingsController.createSecurityLog({
        companyId: req.user.companyId,
        userId,
        action: 'TWO_FACTOR_ENABLED',
        status: 'SUCCESS',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        details: { 
          method,
          enabledBy: 'user'
        }
      });

      res.json({
        success: true,
        message: result.message,
        data: {
          enabled: true,
          method
        }
      });
    } catch (error) {
      console.error('Error enabling 2FA:', error);
      res.status(500).json({ 
        success: false,
        message: 'Erro ao ativar autenticação de dois fatores' 
      });
    }
  }

  /**
   * Disable 2FA for the user
   */
  static async disable(req, res) {
    try {
      const userId = req.user.id;

      // Check if 2FA is enabled
      const currentStatus = await twoFactorService.is2FAEnabled(userId);
      if (!currentStatus.enabled) {
        return res.status(400).json({
          success: false,
          message: 'Autenticação de dois fatores não está ativada'
        });
      }

      // Disable 2FA
      const result = await twoFactorService.disable2FA(userId);

      // Log the action
      await securitySettingsController.createSecurityLog({
        companyId: req.user.companyId,
        userId,
        action: 'TWO_FACTOR_DISABLED',
        status: 'SUCCESS',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        details: { 
          method: currentStatus.method,
          disabledBy: 'user'
        }
      });

      res.json({
        success: true,
        message: result.message,
        data: {
          enabled: false,
          method: null
        }
      });
    } catch (error) {
      console.error('Error disabling 2FA:', error);
      res.status(500).json({ 
        success: false,
        message: 'Erro ao desativar autenticação de dois fatores' 
      });
    }
  }

  /**
   * Send a test 2FA token to verify setup
   */
  static async sendTestToken(req, res) {
    try {
      const userId = req.user.id;

      // Check if 2FA is enabled
      const currentStatus = await twoFactorService.is2FAEnabled(userId);
      if (!currentStatus.enabled) {
        return res.status(400).json({
          success: false,
          message: 'Autenticação de dois fatores não está ativada'
        });
      }

      // Send test token
      const tokenResult = await twoFactorService.sendEmailToken(
        userId,
        req.ip || req.connection.remoteAddress,
        req.headers['user-agent']
      );

      // Log the action
      await securitySettingsController.createSecurityLog({
        companyId: req.user.companyId,
        userId,
        action: 'TWO_FACTOR_TEST_TOKEN_SENT',
        status: 'SUCCESS',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        details: { 
          reason: 'User requested test token'
        }
      });

      res.json({
        success: true,
        message: tokenResult.message,
        expiresAt: tokenResult.expiresAt
      });
    } catch (error) {
      console.error('Error sending test 2FA token:', error);
      res.status(500).json({ 
        success: false,
        message: 'Erro ao enviar token de teste' 
      });
    }
  }

  /**
   * Verify test 2FA token (for setup verification)
   */
  static async verifyTestToken(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const userId = req.user.id;
      const { token } = req.body;

      // Verify token
      const verificationResult = await twoFactorService.verifyToken(userId, token);

      if (!verificationResult.success) {
        // Log failed verification
        await securitySettingsController.createSecurityLog({
          companyId: req.user.companyId,
          userId,
          action: 'TWO_FACTOR_TEST_VERIFICATION_FAILED',
          status: 'FAILED',
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
          details: { 
            reason: verificationResult.message,
            token: token.substring(0, 2) + '****'
          }
        });

        return res.status(400).json({
          success: false,
          message: verificationResult.message
        });
      }

      // Log successful verification
      await securitySettingsController.createSecurityLog({
        companyId: req.user.companyId,
        userId,
        action: 'TWO_FACTOR_TEST_VERIFICATION_SUCCESS',
        status: 'SUCCESS',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        details: { 
          tokenId: verificationResult.tokenId
        }
      });

      res.json({
        success: true,
        message: verificationResult.message
      });
    } catch (error) {
      console.error('Error verifying test 2FA token:', error);
      res.status(500).json({ 
        success: false,
        message: 'Erro ao verificar token de teste' 
      });
    }
  }

  /**
   * Get 2FA status for current user
   */
  static async getStatus(req, res) {
    try {
      const userId = req.user.id;

      const status = await twoFactorService.is2FAEnabled(userId);

      res.json({
        success: true,
        data: status
      });
    } catch (error) {
      console.error('Error getting 2FA status:', error);
      res.status(500).json({ 
        success: false,
        message: 'Erro interno do servidor' 
      });
    }
  }
}

module.exports = TwoFactorController;