// src/controllers/authController.js
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { validationResult } = require('express-validator');
const prisma = require('../utils/prisma');
const authMiddleware = require('../middlewares/auth');
const { isTokenBlacklisted, addToBlacklist } = require('../middlewares/auth');
const emailService = require('../services/emailService');
const { getAllModules, getAllPermissions } = require('../utils/permissionHelper');
const securitySettingsController = require('./securitySettingsController');
const twoFactorService = require('../services/twoFactorService');

// Constantes
const SALT_ROUNDS = 12; // Número maior = mais seguro mas mais lento
const JWT_EXPIRY = '24h'; // Tempo de expiração do token
const MAX_LOGIN_ATTEMPTS = 5; // Máximo de tentativas de login falhas
const PASSWORD_RESET_EXPIRY = 60 * 60 * 1000; // 1 hora em milissegundos
const VALID_MODULES = ["ADMIN", "RH", "FINANCIAL", "SCHEDULING", "BASIC"];

class AuthController {
  /**
   * Registra um novo usuário no sistema
   * Por padrão, o usuário é criado com o módulo BASIC
   */
  static async register(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        login,
        email,
        fullName,
        password,
        cpf,
        cnpj,
        birthDate,
        address,
        phone,
        companyId,
        affiliateCode, // Código do afiliado
        // Dados da empresa (opcionais)
        companyName,
        companyTradingName,
        companyCnpj,
        companyPhone,
        companyAddress,
        companyCity,
        companyState,
        companyPostalCode
      } = req.body;

      // Verifica se o email foi confirmado
      const emailVerification = await prisma.emailVerification.findUnique({ where: { email } });
      if (!emailVerification || !emailVerification.verified) {
        return res.status(400).json({ message: 'Email não confirmado. Confirme o email antes de prosseguir.' });
      }

      // Validar código de afiliado se fornecido
      let affiliate = null;
      if (affiliateCode) {
        affiliate = await prisma.affiliate.findUnique({
          where: { code: affiliateCode.toUpperCase() }
        });
        
        if (!affiliate || !affiliate.active) {
          return res.status(400).json({ message: 'Código de afiliado inválido' });
        }
      }

      // Check if user already exists (email or login)
      const userExists = await prisma.user.findFirst({
        where: {
          OR: [
            { email },
            { login },
            ...(cpf ? [{ cpf }] : []),
            ...(cnpj ? [{ cnpj }] : []),
          ],
        },
      });

      if (userExists) {
        // Don't reveal which field caused the conflict for security
        return res.status(400).json({ message: 'User with these credentials already exists' });
      }

      // Check password strength
      if (!AuthController.isStrongPassword(password)) {
        return res.status(400).json({
          message: 'Password is too weak. It must have at least 8 characters, including uppercase, lowercase, number, and special character.'
        });
      }

      // Hash password with stronger salt
      const hashedPassword = await bcrypt.hash(password, SALT_ROUNDS);

      let finalCompanyId = companyId;

      // Se dados da empresa foram fornecidos, criar a empresa primeiro
      if (companyName && companyCnpj) {
        const cleanedCompanyCnpj = companyCnpj.replace(/\D/g, "");

        // Verificar se CNPJ já existe
        const existingCompany = await prisma.company.findUnique({
          where: { cnpj: cleanedCompanyCnpj },
        });

        if (existingCompany) {
          return res.status(400).json({ message: "CNPJ da empresa já cadastrado" });
        }

        // Criar a empresa
        const now = new Date();
        const trialEnd = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
        const company = await prisma.company.create({
          data: {
            name: companyName,
            tradingName: companyTradingName || companyName,
            cnpj: cleanedCompanyCnpj,
            phone: companyPhone ? companyPhone.replace(/\D/g, "") : null,
            address: companyAddress,
            city: companyCity,
            state: companyState,
            postalCode: companyPostalCode ? companyPostalCode.replace(/\D/g, "") : null,
            trialStart: now,
            trialEnd: trialEnd,
            isTrial: true
          },
        });

        // Criação automática de assinatura trial e módulos básicos
        await prisma.subscription.create({
          data: {
            companyId: company.id,
            billingCycle: 'MONTHLY',
            active: true,
            status: 'TRIAL',
            startDate: now,
            trialEndDate: trialEnd,
            pricePerMonth: 0,
            userLimit: 5, // limite de usuários para trial
            modules: {
              create: [
                { moduleType: 'ADMIN', active: true, pricePerMonth: 0 },
                { moduleType: 'SCHEDULING', active: true, pricePerMonth: 0 },
                { moduleType: 'BASIC', active: true, pricePerMonth: 0 },
              ]
            }
          }
        });

        finalCompanyId = company.id;
      }

      // Create user
      // Se for COMPANY_ADMIN, atribuir todos os módulos e permissões válidos
      let userModules = ['BASIC'];
      let userPermissions = [];
      let userRole = finalCompanyId && !companyId ? 'COMPANY_ADMIN' : 'EMPLOYEE';
      if (userRole === 'COMPANY_ADMIN') {
        userModules = getAllModules().filter(m => VALID_MODULES.includes(m));
        userPermissions = getAllPermissions();
      }
      const user = await prisma.user.create({
        data: {
          login,
          email,
          fullName,
          password: hashedPassword,
          cpf,
          cnpj,
          birthDate: birthDate ? new Date(birthDate) : null,
          address,
          phone,
          modules: userModules, // Corrigido para todos os módulos se for admin
          permissions: userPermissions, // Corrigido para todas as permissões se for admin
          active: true,
          companyId: finalCompanyId,
          createdById: req.user?.id || null,
          // Se criou empresa, tornar o usuário admin da empresa
          role: userRole
        },
        select: {
          id: true,
          login: true,
          email: true,
          fullName: true,
          cpf: true,
          cnpj: true,
          birthDate: true,
          address: true,
          phone: true,
          modules: true,
          permissions: true,
          role: true,
          active: true,
          createdAt: true,
          companyId: true
        },
      });

      // Registrar venda do afiliado se houver código válido
      if (affiliate && user.companyId) {
        try {
          // Buscar a assinatura da empresa para calcular o valor
          const subscription = await prisma.subscription.findUnique({
            where: { companyId: user.companyId }
          });

          if (subscription) {
            // Calcular valor da venda (assumindo que é o valor mensal da assinatura)
            const saleAmount = parseFloat(subscription.pricePerMonth || 0);
            const commission = (saleAmount * affiliate.commission) / 100;

            // Registrar a venda
            await prisma.affiliateSale.create({
              data: {
                affiliateId: affiliate.id,
                companyId: user.companyId,
                subscriptionId: subscription.id,
                amount: saleAmount,
                commission: commission,
                commissionPercentage: affiliate.commission,
                monthsCount: 1, // Assumindo 1 mês para registro inicial
                status: 'PENDING'
              }
            });

            // Atualizar estatísticas do afiliado
            await prisma.affiliate.update({
              where: { id: affiliate.id },
              data: {
                totalSales: {
                  increment: 1
                }
              }
            });

            console.log(`[AUTH] Venda registrada para afiliado ${affiliate.name}: R$ ${commission.toFixed(2)}`);
          }
        } catch (error) {
          console.error('[AUTH] Erro ao registrar venda do afiliado:', error);
          // Não falha o registro do usuário se houver erro no registro do afiliado
        }
      }

      // Log to audit trail
      await prisma.auditLog.create({
        data: {
          userId: req.user?.id || user.id,
          action: 'CREATE',
          entityType: 'User',
          entityId: user.id,
          details: { method: 'register' },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: user.companyId
        }
      });

      // Generate token
      const token = jwt.sign(
        {
          id: user.id,
          modules: user.modules,
          role: user.role,
          iat: Math.floor(Date.now() / 1000)
        },
        process.env.JWT_SECRET,
        { expiresIn: JWT_EXPIRY }
      );

      res.status(201).json({
        user,
        token
      });
    } catch (error) {
      console.error('Registration error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }

  /**
   * Authenticate a user or client in the system
   * Validates credentials and checks if account is active
   */
  static async login(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { email, username, password, loginType = 'email' } = req.body;

      // Determine which identifier to use (email or username)
      const identifier = loginType === 'email' ? email : username;

      if (!identifier) {
        return res.status(400).json({ message: 'Email or username is required' });
      }

      let user = null;
      let isClient = false;

      // First, try to find a user
      if (loginType === 'email') {
        user = await prisma.user.findUnique({
          where: { email: identifier }
        });
      } else {
        user = await prisma.user.findUnique({
          where: { login: identifier }
        });
      }

      // If user not found, try to find a client
      if (!user) {
        if (loginType === 'email') {
          const client = await prisma.client.findUnique({
            where: { email: identifier }
          });

          if (client && client.active) {
            user = client;
            isClient = true;
          }
        } else {
          const client = await prisma.client.findUnique({
            where: { login: identifier }
          });

          if (client && client.active) {
            user = client;
            isClient = true;
          }
        }
      }

      // If neither user nor client found, or account is inactive, return generic message
      if (!user || !user.active) {
        return res.status(400).json({ message: 'Invalid credentials' });
      }

      // Check for too many failed login attempts (only for User model)
      if (!isClient && user.failedLoginAttempts >= MAX_LOGIN_ATTEMPTS) {
        return res.status(429).json({
          message: 'Account temporarily locked due to too many failed login attempts. Please reset your password or try again later.'
        });
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password);

      if (!isValidPassword) {
        // Increment failed login attempts (only for User model)
        if (!isClient) {
          await prisma.user.update({
            where: { id: user.id },
            data: {
              failedLoginAttempts: user.failedLoginAttempts + 1
            }
          });
        }

        return res.status(400).json({ message: 'Invalid credentials' });
      }

      // For User model: Reset failed login attempts on successful login
      if (!isClient) {
        await prisma.user.update({
          where: { id: user.id },
          data: {
            failedLoginAttempts: 0,
            lastLoginAt: new Date(),
            lastLoginIp: req.ip || req.connection.remoteAddress
          }
        });

        // Check if 2FA is enabled for this user OR if it's required by company policy
        const is2FARequired = await securitySettingsController.is2FARequired(user.companyId);
        
        if (user.twoFactorEnabled || is2FARequired) {
          // If 2FA is required but user doesn't have it enabled, force them to enable it
          if (is2FARequired && !user.twoFactorEnabled) {
            return res.status(200).json({
              requiresTwoFactorSetup: true,
              message: 'Sua empresa exige autenticação de dois fatores. Configure 2FA para continuar.',
              userId: user.id
            });
          }
          try {
            // Send 2FA token
            const tokenResult = await twoFactorService.sendEmailToken(
              user.id, 
              req.ip || req.connection.remoteAddress,
              req.headers['user-agent']
            );

            // Log 2FA token sent
            await prisma.auditLog.create({
              data: {
                userId: user.id,
                action: 'TWO_FACTOR_TOKEN_SENT',
                entityType: 'User',
                entityId: user.id,
                ipAddress: req.ip || req.connection.remoteAddress,
                userAgent: req.headers['user-agent'],
                companyId: user.companyId
              }
            });

            return res.status(200).json({
              requiresTwoFactor: true,
              message: 'Token de verificação enviado para seu email',
              userId: user.id,
              expiresAt: tokenResult.expiresAt
            });
          } catch (error) {
            console.error('Error sending 2FA token:', error);
            return res.status(500).json({
              message: 'Erro ao enviar token de verificação. Tente novamente.'
            });
          }
        }

        // Log successful login for User (only if no 2FA)
        await prisma.auditLog.create({
          data: {
            userId: user.id,
            action: 'LOGIN',
            entityType: 'User',
            entityId: user.id,
            ipAddress: req.ip || req.connection.remoteAddress,
            userAgent: req.headers['user-agent'],
            companyId: user.companyId
          }
        });
      }

      // Se for usuário do sistema (não client), checar limite de usuários ativos
      if (!isClient && user.companyId && user.role !== 'SYSTEM_ADMIN' && user.role !== 'COMPANY_ADMIN') {
        // Buscar assinatura da empresa
        const subscription = await prisma.subscription.findUnique({
          where: { companyId: user.companyId },
          include: {
            company: {
              select: {
                users: {
                  where: { active: true },
                  select: { id: true }
                }
              }
            }
          }
        });
        if (subscription) {
          const activeUsers = subscription.company.users.length;
          if (activeUsers > subscription.userLimit) {
            return res.status(403).json({
              message: `O número de usuários ativos (${activeUsers}) excede o limite do plano (${subscription.userLimit}). Contate o administrador da empresa para regularizar o acesso.`,
              code: 'USER_LIMIT_EXCEEDED',
              details: {
                currentUsers: activeUsers,
                userLimit: subscription.userLimit
              }
            });
          }
        }
      }

      // Generate token with appropriate data based on user type
      const tokenData = {
        id: user.id,
        iat: Math.floor(Date.now() / 1000),
        isClient: isClient
      };

      // Add modules and role for User type
      if (!isClient) {
        tokenData.modules = user.modules;
        tokenData.role = user.role;
      }

      const token = jwt.sign(
        tokenData,
        process.env.JWT_SECRET,
        { expiresIn: JWT_EXPIRY }
      );

      // Remove sensitive fields
      let userWithoutSensitiveData;
      if (isClient) {
        const { password: _, ...clientData } = user;
        userWithoutSensitiveData = {
          ...clientData,
          isClient: true
        };
      } else {
        const { password: _, failedLoginAttempts: __, ...userData } = user;
        userWithoutSensitiveData = userData;
      }

      // Log successful login
      await securitySettingsController.createSecurityLog({
        companyId: user.companyId,
        userId: user.id,
        action: 'LOGIN_ATTEMPT',
        status: 'SUCCESS',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        details: { 
          identifier: email || username,
          loginType: isClient ? 'client' : 'user',
          userFound: true
        }
      });

      res.json({
        user: userWithoutSensitiveData,
        token
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }

  /**
   * Logout user and invalidate token
   */
  static async logout(req, res) {
    try {
      const authHeader = req.headers.authorization;

      if (authHeader) {
        const [scheme, token] = authHeader.split(' ');

        if (token && scheme === 'Bearer') {
          // Add token to blacklist
          await authMiddleware.revokeToken(token);

          console.log(`[LOGOUT] Token adicionado à lista negra para o usuário ${req.user.id}`);

          // Desconectar socket do usuário se existir
          try {
            const socketService = require('../socket/socketService');
            const io = socketService.getIO();
            
            if (io) {
              // Buscar todos os sockets do usuário
              const userSockets = await io.in(`user:${req.user.id}`).fetchSockets();
              
              // Desconectar cada socket do usuário
              for (const socket of userSockets) {
                console.log(`[LOGOUT] Desconectando socket ${socket.id} do usuário ${req.user.id}`);
                socket.disconnect(true);
              }
              
              console.log(`[LOGOUT] ${userSockets.length} sockets desconectados para o usuário ${req.user.id}`);
            }
          } catch (socketError) {
            console.warn('[LOGOUT] Erro ao desconectar sockets:', socketError.message);
          }

          // Log logout
          await prisma.auditLog.create({
            data: {
              userId: req.user.id,
              action: 'LOGOUT',
              entityType: 'User',
              entityId: req.user.id,
              ipAddress: req.ip || req.connection.remoteAddress,
              userAgent: req.headers['user-agent'],
              companyId: req.user.companyId
            }
          });

          // Security log
          await securitySettingsController.createSecurityLog({
            companyId: req.user.companyId,
            userId: req.user.id,
            action: 'LOGOUT',
            status: 'SUCCESS',
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            details: { 
              manual: true,
              tokenRevoked: true
            }
          });
        }
      }

      res.status(200).json({ message: 'Logout successful' });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }

  /**
   * Return authenticated user or client data
   */
  static async me(req, res) {
    try {
      const userId = req.user.id;
      const isClient = req.user.isClient === true;

      if (isClient) {
        // Return client data
        const client = await prisma.client.findUnique({
          where: { id: userId },
          select: {
            id: true,
            login: true,
            email: true,
            active: true,
            createdAt: true,
            companyId: true,
            Company: {
              select: {
                id: true,
                name: true,
                tradingName: true
              }
            },
            // Include related persons through clientPersons
            clientPersons: {
              where: {
                person: {
                  active: true
                }
              },
              select: {
                person: {
                  select: {
                    id: true,
                    fullName: true,
                    email: true,
                    phone: true,
                    profileImageUrl: true
                  }
                }
              }
            }
          },
        });

        if (!client) {
          return res.status(404).json({ message: 'Client not found' });
        }

        // Format response
        const clientData = {
          ...client,
          isClient: true,
          role: 'CLIENT',
          company: client.Company,
          // Transform clientPersons to persons for compatibility
          persons: client.clientPersons?.map(cp => cp.person) || [],
          // Add empty arrays for compatibility with frontend
          modules: [],
          permissions: []
        };

        // Remove Company and clientPersons fields (already mapped)
        delete clientData.Company;
        delete clientData.clientPersons;

        res.json(clientData);
      } else {
        // Return user data
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: {
            id: true,
            login: true,
            email: true,
            fullName: true,
            cpf: true,
            cnpj: true,
            birthDate: true,
            address: true,
            phone: true,
            modules: true,
            permissions: true,
            role: true,
            active: true,
            createdAt: true,
            lastLoginAt: true,
            companyId: true,
            profileImageUrl: true,
            company: {
              select: {
                id: true,
                name: true,
                tradingName: true
              }
            }
          },
        });

        if (!user) {
          return res.status(404).json({ message: 'User not found' });
        }

        // Adicionar URL completa da imagem de perfil, se existir
        if (user.profileImageUrl) {
          // Obter a URL base da API a partir do request
          const baseUrl = `${req.protocol}://${req.get('host')}`;
          user.profileImageFullUrl = `${baseUrl}/uploads/${user.profileImageUrl}`;
        }

        res.json(user);
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }

  /**
   * Request password reset
   */
  static async requestPasswordReset(req, res) {
    try {
      const { email } = req.body;

      // Find user by email
      const user = await prisma.user.findUnique({
        where: { email }
      });

      // Always return success even if user not found (security)
      if (!user) {
        return res.status(200).json({
          message: 'If your email is registered, you will receive password reset instructions shortly'
        });
      }

      // Generate unique token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const tokenExpiry = new Date(Date.now() + PASSWORD_RESET_EXPIRY);

      // Save token to database
      await prisma.passwordReset.create({
        data: {
          userId: user.id,
          token: resetToken,
          expiresAt: tokenExpiry
        }
      });

      // IMPLEMENTAR ENVIO DE EMAIL COM LINK DE REDEFINIÇÃO DE SENHA

      // Log password reset request
      await prisma.auditLog.create({
        data: {
          userId: user.id,
          action: 'PASSWORD_RESET_REQUEST',
          entityType: 'User',
          entityId: user.id,
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: user.companyId
        }
      });

      res.status(200).json({
        message: 'If your email is registered, you will receive password reset instructions shortly'
      });
    } catch (error) {
      console.error('Password reset request error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }

  /**
   * Reset password with token
   */
  static async resetPassword(req, res) {
    try {
      const { token, newPassword } = req.body;

      // Validate new password
      if (!AuthController.isStrongPassword(newPassword)) {
        return res.status(400).json({
          message: 'Password is too weak. It must have at least 8 characters, including uppercase, lowercase, number, and special character.'
        });
      }

      // Find token in database
      const resetRecord = await prisma.passwordReset.findUnique({
        where: { token }
      });

      // Check if token exists and is valid
      if (!resetRecord || resetRecord.expiresAt < new Date() || resetRecord.usedAt) {
        return res.status(400).json({ message: 'Invalid or expired token' });
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, SALT_ROUNDS);

      // Update user password
      await prisma.user.update({
        where: { id: resetRecord.userId },
        data: {
          password: hashedPassword,
          passwordChangedAt: new Date(),
          failedLoginAttempts: 0
        }
      });

      // Mark token as used
      await prisma.passwordReset.update({
        where: { id: resetRecord.id },
        data: { usedAt: new Date() }
      });

      // Log password reset
      await prisma.auditLog.create({
        data: {
          userId: resetRecord.userId,
          action: 'PASSWORD_RESET',
          entityType: 'User',
          entityId: resetRecord.userId,
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent']
        }
      });

      res.status(200).json({ message: 'Password reset successful' });
    } catch (error) {
      console.error('Password reset error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }

  /**
   * Change password (for authenticated users)
   */
  static async changePassword(req, res) {
    try {
      const { currentPassword, newPassword } = req.body;
      const userId = req.user.id;

      // Find user
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });

      // Verify current password
      const isValidPassword = await bcrypt.compare(currentPassword, user.password);
      if (!isValidPassword) {
        return res.status(400).json({ message: 'Current password is incorrect' });
      }

      // Validar nova senha com políticas da empresa
      if (user.companyId) {
        const validation = await securitySettingsController.validatePassword(newPassword, user.companyId);
        if (!validation.valid) {
          return res.status(400).json({
            message: 'Senha não atende aos critérios de segurança',
            errors: validation.errors
          });
        }

        // Verificar histórico de senhas
        const historyCheck = await securitySettingsController.checkPasswordHistory(userId, newPassword);
        if (!historyCheck.canUse) {
          return res.status(400).json({
            message: historyCheck.message
          });
        }
      } else {
        // Fallback para validação padrão
        if (!AuthController.isStrongPassword(newPassword)) {
          return res.status(400).json({
            message: 'Password is too weak. It must have at least 8 characters, including uppercase, lowercase, number, and special character.'
          });
        }
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, SALT_ROUNDS);

      // Adicionar senha atual ao histórico antes de alterar
      if (user.companyId) {
        await securitySettingsController.addPasswordToHistory(userId, user.password);
      }

      // Update user password
      await prisma.user.update({
        where: { id: userId },
        data: {
          password: hashedPassword,
          passwordChangedAt: new Date()
        }
      });

      // Log password change
      await securitySettingsController.createSecurityLog({
        companyId: user.companyId,
        userId: userId,
        action: 'PASSWORD_CHANGE',
        status: 'SUCCESS',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        details: { changedBy: 'user' }
      });

      res.status(200).json({ message: 'Password changed successfully' });
    } catch (error) {
      console.error('Password change error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }

  /**
   * Solicita confirmação de email: gera código, salva e envia
   */
  static async requestEmailConfirmation(req, res) {
    try {
      const { email, userName } = req.body; // Adicionar userName opcional

      if (!email) return res.status(400).json({ message: 'Email é obrigatório' });

      // Gera código de 6 dígitos
      const code = Math.floor(100000 + Math.random() * 900000).toString();
      const expiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15 minutos

      // Remove confirmações antigas
      await prisma.emailVerification.deleteMany({ where: { email } });

      // Salva nova confirmação
      await prisma.emailVerification.create({
        data: {
          email,
          code,
          expiresAt
        }
      });

      // Envia email com template melhorado
      const emailResult = await emailService.sendEmailConfirmation(email, code, userName);

      if (!emailResult.success) {
        console.error('Falha ao enviar email:', emailResult.error);
        return res.status(500).json({
          message: 'Erro ao enviar email de confirmação. Tente novamente.'
        });
      }

      console.log(`Email de confirmação enviado para ${email}, código: ${code}`);

      return res.json({
        message: 'Código de confirmação enviado para o email.',
        emailSent: true
      });

    } catch (error) {
      console.error('Erro ao solicitar confirmação de email:', error);
      return res.status(500).json({
        message: 'Erro interno do servidor ao enviar confirmação de email.'
      });
    }
  }

  /**
   * Verifica código de confirmação de email
   */
  static async verifyEmailConfirmation(req, res) {
    try {
      const { email, code, sessionId } = req.body;

      if (!code) {
        return res.status(400).json({ message: 'Código é obrigatório' });
      }

      let emailToVerify = email;

      // Se sessionId foi fornecido, buscar email da sessão
      if (sessionId) {
        const session = await prisma.signupSession.findUnique({
          where: { id: sessionId }
        });

        if (!session) {
          return res.status(404).json({ message: 'Sessão não encontrada' });
        }

        if (session.used) {
          return res.status(400).json({ message: 'Sessão já foi utilizada' });
        }

        if (session.expiresAt < new Date()) {
          return res.status(400).json({ message: 'Sessão expirada' });
        }

        emailToVerify = session.sessionData.email;
      } else if (!email) {
        return res.status(400).json({ message: 'Email ou sessionId são obrigatórios' });
      }

      const record = await prisma.emailVerification.findUnique({ 
        where: { email: emailToVerify.toLowerCase() } 
      });
      
      if (!record) return res.status(400).json({ message: 'Solicite um novo código.' });
      if (record.verified) return res.status(400).json({ message: 'Email já confirmado.' });
      if (record.code !== code) return res.status(400).json({ message: 'Código inválido.' });
      if (record.expiresAt < new Date()) return res.status(400).json({ message: 'Código expirado.' });
      
      await prisma.emailVerification.update({ 
        where: { email: emailToVerify.toLowerCase() }, 
        data: { verified: true } 
      });
      
      return res.json({ message: 'Email confirmado com sucesso.' });
    } catch (error) {
      console.error('Erro ao verificar código de email:', error);
      return res.status(500).json({ message: 'Erro ao verificar código.' });
    }
  }

  /**
 * Validar se o email foi confirmado antes do registro
 */
  static async validateEmailConfirmation(req, res) {
    try {
      const { email } = req.body;

      if (!email) {
        return res.status(400).json({ message: 'Email é obrigatório' });
      }

      // Verificar se o email foi confirmado
      const emailVerification = await prisma.emailVerification.findUnique({
        where: { email }
      });

      if (!emailVerification) {
        return res.status(400).json({
          message: 'Código de confirmação não encontrado. Solicite um novo código.',
          needsVerification: true
        });
      }

      if (!emailVerification.verified) {
        return res.status(400).json({
          message: 'Email não confirmado. Confirme o email antes de prosseguir.',
          needsVerification: true
        });
      }

      // Se chegou até aqui, o email está confirmado
      return res.json({
        message: 'Email confirmado com sucesso',
        verified: true
      });

    } catch (error) {
      console.error('Erro ao validar confirmação de email:', error);
      return res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }
  /**
   * Helper to validate password strength
   */
  static isStrongPassword(password) {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, and 1 special character
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()\-_=+{};:,<.>]).{8,}$/;
    return passwordRegex.test(password);
  }

  /**
   * Criar sessão de signup para armazenar dados temporariamente
   */
  static async createSignupSession(req, res) {
    try {
      const {
        // Dados pessoais
        login,
        fullName,
        email,
        password,
        cpf,
        cnpj,
        birthDate,
        address,
        phone,
        
        // Dados da empresa
        companyName,
        companyTradingName,
        companyCnpj,
        companyPhone,
        companyAddress,
        companyCity,
        companyState,
        companyPostalCode,
        
        // Configurações do plano
        subscriptionType,
        planConfig
      } = req.body;

      // Validações básicas
      if (!login || !fullName || !email || !password) {
        return res.status(400).json({ message: 'Dados obrigatórios não fornecidos' });
      }

      // Validar força da senha
      if (!AuthController.isStrongPassword(password)) {
        return res.status(400).json({
          message: 'Senha muito fraca. Deve ter pelo menos 8 caracteres, incluindo maiúscula, minúscula, número e caractere especial.'
        });
      }

      // Verificar se email já existe
      const existingUser = await prisma.user.findUnique({
        where: { email: email.toLowerCase() }
      });

      if (existingUser) {
        return res.status(400).json({ message: 'Email já está em uso' });
      }

      // Verificar se login já existe
      const existingLogin = await prisma.user.findUnique({
        where: { login }
      });

      if (existingLogin) {
        return res.status(400).json({ message: 'Nome de usuário já está em uso' });
      }

      // Verificar CPF se fornecido
      if (cpf) {
        const cleanedCpf = cpf.replace(/\D/g, "");
        const existingCpf = await prisma.user.findUnique({
          where: { cpf: cleanedCpf }
        });

        if (existingCpf) {
          return res.status(400).json({ message: 'CPF já está cadastrado' });
        }
      }

      // Verificar CNPJ se fornecido
      if (cnpj) {
        const cleanedCnpj = cnpj.replace(/\D/g, "");
        const existingCnpj = await prisma.user.findUnique({
          where: { cnpj: cleanedCnpj }
        });

        if (existingCnpj) {
          return res.status(400).json({ message: 'CNPJ já está cadastrado' });
        }
      }

      // Verificar CNPJ da empresa se fornecido
      if (companyCnpj) {
        const cleanedCnpj = companyCnpj.replace(/\D/g, "");
        const existingCompany = await prisma.company.findUnique({
          where: { cnpj: cleanedCnpj }
        });

        if (existingCompany) {
          return res.status(400).json({ message: 'CNPJ da empresa já está cadastrado' });
        }
      }

      // Validar código de cupom se fornecido
      let coupon = null;
      let affiliate = null;
      if (planConfig?.coupon) {
        // Primeiro, buscar o cupom
        coupon = await prisma.coupon.findUnique({
          where: { code: planConfig.coupon.toUpperCase() },
          include: { affiliate: true }
        });
        
        if (!coupon || !coupon.active) {
          return res.status(400).json({ message: 'Código de cupom inválido' });
        }

        // Se o cupom tem afiliado associado, usar ele
        if (coupon.affiliateId) {
          affiliate = coupon.affiliate;
          if (!affiliate || !affiliate.active) {
            return res.status(400).json({ message: 'Afiliado associado ao cupom está inativo' });
          }
        }
      }

      // Determinar tipo de assinatura baseado nos dados
      const finalSubscriptionType = planConfig ? 'paid' : (subscriptionType || 'trial');
      
      // Criar sessão com dados criptografados
      const sessionData = {
        // Dados pessoais
        login,
        fullName,
        email: email.toLowerCase(),
        password: await bcrypt.hash(password, SALT_ROUNDS), // Hash da senha
        originalPassword: password, // Senha original para login automático em trial
        cpf,
        cnpj,
        birthDate,
        address,
        phone,
        
        // Dados da empresa
        companyName,
        companyTradingName,
        companyCnpj,
        companyPhone,
        companyAddress,
        companyCity,
        companyState,
        companyPostalCode,
        
        // Configurações do plano
        subscriptionType: finalSubscriptionType,
        planConfig,
        
        // Dados do cupom e afiliado
        couponId: coupon?.id || null,
        couponCode: coupon?.code || null,
        couponValue: coupon?.value || null,
        couponType: coupon?.type || null,
        affiliateId: affiliate?.id || null,
        affiliateCode: affiliate?.code || null
      };

      // Criar sessão que expira em 1 hora
      const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hora
      
      const signupSession = await prisma.signupSession.create({
        data: {
          sessionData,
          expiresAt
        }
      });

      res.status(201).json({
        message: 'Sessão de signup criada com sucesso',
        sessionId: signupSession.id,
        expiresAt: signupSession.expiresAt
      });

    } catch (error) {
      console.error('Erro ao criar sessão de signup:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Obter dados da sessão de signup
   */
  static async getSignupSession(req, res) {
    try {
      const { sessionId } = req.params;

      if (!sessionId) {
        return res.status(400).json({ message: 'ID da sessão é obrigatório' });
      }

      const session = await prisma.signupSession.findUnique({
        where: { id: sessionId }
      });

      if (!session) {
        return res.status(404).json({ message: 'Sessão não encontrada' });
      }

      if (session.used) {
        return res.status(400).json({ message: 'Sessão já foi utilizada' });
      }

      if (session.expiresAt < new Date()) {
        return res.status(400).json({ message: 'Sessão expirada' });
      }

      // Retorna dados necessários para a UI (incluindo email para exibição)
      const { sessionData, ...sessionInfo } = session;
      
      res.json({
        sessionId: session.id,
        expiresAt: session.expiresAt,
        email: sessionData.email, // Email é necessário para a UI
        subscriptionType: sessionData.subscriptionType,
        planConfig: sessionData.planConfig,
        couponCode: sessionData.couponCode,
        couponValue: sessionData.couponValue,
        couponType: sessionData.couponType,
        affiliateCode: sessionData.affiliateCode
      });

    } catch (error) {
      console.error('Erro ao obter sessão de signup:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Finalizar signup e criar conta após confirmação de email
   */
  static async finalizeSignup(req, res) {
    try {
      const { sessionId } = req.body;

      if (!sessionId) {
        return res.status(400).json({ message: 'ID da sessão é obrigatório' });
      }

      const session = await prisma.signupSession.findUnique({
        where: { id: sessionId }
      });

      if (!session) {
        return res.status(404).json({ message: 'Sessão não encontrada' });
      }

      if (session.used) {
        return res.status(400).json({ message: 'Sessão já foi utilizada' });
      }

      if (session.expiresAt < new Date()) {
        return res.status(400).json({ message: 'Sessão expirada' });
      }

      const sessionData = session.sessionData;

      // Verificar se o email foi confirmado
      const emailVerification = await prisma.emailVerification.findUnique({
        where: { email: sessionData.email }
      });

      if (!emailVerification || !emailVerification.verified) {
        return res.status(400).json({ message: 'Email não confirmado. Confirme o email antes de prosseguir.' });
      }

      // Criar empresa e usuário em uma transação
      const result = await prisma.$transaction(async (tx) => {
        let companyId = null;

        // Criar empresa se dados foram fornecidos
        if (sessionData.companyName && sessionData.companyCnpj) {
          const cleanedCnpj = sessionData.companyCnpj.replace(/\D/g, "");
          
          // Se há cupom, não criar trial (será criado após pagamento)
          const hasCoupon = !!sessionData.couponCode;
          
          const company = await tx.company.create({
            data: {
              name: sessionData.companyName,
              tradingName: sessionData.companyTradingName || sessionData.companyName,
              cnpj: cleanedCnpj,
              phone: sessionData.companyPhone ? sessionData.companyPhone.replace(/\D/g, "") : null,
              address: sessionData.companyAddress,
              city: sessionData.companyCity,
              state: sessionData.companyState,
              postalCode: sessionData.companyPostalCode ? sessionData.companyPostalCode.replace(/\D/g, "") : null,
              trialStart: hasCoupon ? null : new Date(),
              trialEnd: hasCoupon ? null : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 dias
              isTrial: !hasCoupon
            }
          });

          companyId = company.id;

          // Só criar assinatura trial se não há cupom
          if (!hasCoupon) {
            await tx.subscription.create({
              data: {
                companyId: company.id,
                billingCycle: 'MONTHLY',
                active: true,
                status: 'TRIAL',
                startDate: new Date(),
                trialEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                pricePerMonth: 0,
                userLimit: 5,
                modules: {
                  create: [
                    { moduleType: 'ADMIN', active: true, pricePerMonth: 0 },
                    { moduleType: 'SCHEDULING', active: true, pricePerMonth: 0 },
                    { moduleType: 'BASIC', active: true, pricePerMonth: 0 },
                  ]
                }
              }
            });
          }
        }

        // Criar usuário
        const user = await tx.user.create({
          data: {
            login: sessionData.login,
            email: sessionData.email,
            fullName: sessionData.fullName,
            password: sessionData.password, // Já está hasheada
            cpf: sessionData.cpf,
            cnpj: sessionData.cnpj,
            birthDate: sessionData.birthDate ? new Date(sessionData.birthDate) : null,
            address: sessionData.address,
            phone: sessionData.phone,
            modules: companyId ? ['ADMIN', 'SCHEDULING', 'BASIC'] : ['BASIC'],
            permissions: companyId ? getAllPermissions() : [],
            active: true,
            companyId,
            role: companyId ? 'COMPANY_ADMIN' : 'EMPLOYEE'
          }
        });

        // Registrar venda de afiliado se aplicável
        if (sessionData.affiliateId && companyId) {
          await tx.affiliateSale.create({
            data: {
              affiliateId: sessionData.affiliateId,
              companyId,
              amount: 0, // Trial gratuito
              commission: 0, // Sem comissão para trial
              commissionPercentage: 0,
              monthsCount: 1,
              status: 'PENDING'
            }
          });
        }

        // Registrar uso do cupom se aplicável
        if (sessionData.couponId && companyId) {
          await tx.couponRedemption.create({
            data: {
              couponId: sessionData.couponId,
              companyId,
              redeemedAt: new Date()
            }
          });
        }

        // Marcar sessão como utilizada
        await tx.signupSession.update({
          where: { id: sessionId },
          data: { used: true }
        });

        return { user, companyId };
      });

      // Para planos trial, retornar senha temporária para login automático
      const isTrial = sessionData.subscriptionType === 'trial' || !sessionData.couponCode;
      
      res.json({
        message: 'Conta criada com sucesso!',
        user: {
          id: result.user.id,
          login: result.user.login,
          email: result.user.email,
          fullName: result.user.fullName,
          role: result.user.role,
          companyId: result.companyId
        },
        subscriptionType: sessionData.subscriptionType,
        planConfig: sessionData.planConfig,
        couponCode: sessionData.couponCode,
        tempPassword: isTrial ? sessionData.originalPassword : undefined // Senha original para login automático em trial
      });

    } catch (error) {
      console.error('Erro ao finalizar signup:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Verificar se a conta foi criada após pagamento e retornar dados para login
   */
  static async checkPaymentSignupStatus(req, res) {
    try {
      const { sessionId } = req.params;

      if (!sessionId) {
        return res.status(400).json({ message: 'ID da sessão é obrigatório' });
      }

      const session = await prisma.signupSession.findUnique({
        where: { id: sessionId }
      });

      if (!session) {
        return res.status(404).json({ message: 'Sessão não encontrada' });
      }

      if (session.expiresAt < new Date()) {
        return res.status(400).json({ message: 'Sessão expirada' });
      }

      // Verificar se já existe um usuário criado para esta sessão
      const existingUser = await prisma.user.findUnique({
        where: { email: session.sessionData.email },
        include: { 
          company: {
            include: {
              subscription: true
            }
          }
        }
      });

      if (existingUser && existingUser.company && existingUser.company.subscription && existingUser.company.subscription.status === 'ACTIVE') {
        // Conta foi criada pelo webhook após pagamento - retornar dados para login
        res.json({
          accountCreated: true,
          user: {
            id: existingUser.id,
            login: existingUser.login,
            email: existingUser.email,
            fullName: existingUser.fullName,
            role: existingUser.role,
            companyId: existingUser.companyId
          },
          tempPassword: session.sessionData.originalPassword // Senha original para login automático
        });
      } else {
        // Conta ainda não foi criada - pagamento pode estar pendente
        res.json({
          accountCreated: false,
          message: 'Aguardando confirmação do pagamento...'
        });
      }

    } catch (error) {
      console.error('Erro ao verificar status do signup pós-pagamento:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Finalizar signup através do webhook do Stripe após pagamento bem-sucedido
   */
  static async finalizeSignupFromWebhook(sessionId, stripeCustomerId, stripeSubscriptionId, stripeSubscription, metadata) {
    const session = await prisma.signupSession.findUnique({
      where: { id: sessionId }
    });

    if (!session || session.used) {
      throw new Error('Sessão não encontrada ou já utilizada');
    }

    const sessionData = session.sessionData;

    // Extract billing cycle from subscription
    const billingCycle = stripeSubscription.items.data[0]?.price.recurring.interval === 'year'
      ? 'YEARLY'
      : 'MONTHLY';

    // Criar empresa e usuário em uma transação
    const result = await prisma.$transaction(async (tx) => {
      // 1. Criar empresa
      const company = await tx.company.create({
        data: {
          name: sessionData.companyName,
          tradingName: sessionData.companyTradingName || sessionData.companyName,
          cnpj: sessionData.companyCnpj ? sessionData.companyCnpj.replace(/\D/g, "") : null,
          phone: sessionData.companyPhone ? sessionData.companyPhone.replace(/\D/g, "") : null,
          address: sessionData.companyAddress,
          city: sessionData.companyCity,
          state: sessionData.companyState,
          postalCode: sessionData.companyPostalCode ? sessionData.companyPostalCode.replace(/\D/g, "") : null,
          stripeCustomerId: stripeCustomerId,
          isTrial: false // Não é trial pois foi pago
        }
      });

      // 2. Criar assinatura
      await tx.subscription.create({
        data: {
          companyId: company.id,
          status: 'ACTIVE',
          billingCycle,
          stripeCustomerId: stripeCustomerId,
          stripeSubscriptionId: stripeSubscriptionId,
          stripeCurrentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
          active: true,
          startDate: new Date(),
          pricePerMonth: new Prisma.Decimal(metadata.finalPrice || 0),
          userLimit: parseInt(metadata.userLimit || 5),
          modules: {
            create: [
              { moduleType: 'ADMIN', active: true, pricePerMonth: 0 },
              { moduleType: 'SCHEDULING', active: true, pricePerMonth: 0 },
              { moduleType: 'BASIC', active: true, pricePerMonth: 0 },
            ]
          }
        }
      });

      // 3. Criar usuário
      const user = await tx.user.create({
        data: {
          login: sessionData.login,
          email: sessionData.email,
          fullName: sessionData.fullName,
          password: sessionData.password, // Já está hasheada
          cpf: sessionData.cpf,
          cnpj: sessionData.cnpj,
          birthDate: sessionData.birthDate ? new Date(sessionData.birthDate) : null,
          address: sessionData.address,
          phone: sessionData.phone,
          modules: ['ADMIN', 'SCHEDULING', 'BASIC'],
          role: 'COMPANY_ADMIN',
          companyId: company.id,
          verified: true // Email já foi verificado
        }
      });

      // 4. Marcar sessão como utilizada
      await tx.signupSession.update({
        where: { id: sessionId },
        data: { used: true }
      });

      return { user, company };
    });

    console.log(`[WEBHOOK] Conta criada com sucesso - Email: ${result.user.email}, Empresa: ${result.company.name}`);
    return result;
  }

  /**
   * Verify 2FA token and complete login
   */
  static async verifyTwoFactor(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { userId, token } = req.body;

      if (!userId || !token) {
        return res.status(400).json({ message: 'User ID e token são obrigatórios' });
      }

      // Verify the 2FA token
      const verificationResult = await twoFactorService.verifyToken(userId, token);

      if (!verificationResult.success) {
        // Log failed 2FA attempt
        await securitySettingsController.createSecurityLog({
          userId,
          action: 'TWO_FACTOR_VERIFICATION_FAILED',
          status: 'FAILED',
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
          details: { 
            reason: verificationResult.message,
            token: token.substring(0, 2) + '****' // Log partial token for debugging
          }
        });

        return res.status(400).json({
          message: verificationResult.message
        });
      }

      // Get user data for token generation
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          fullName: true,
          login: true,
          active: true,
          modules: true,
          role: true,
          companyId: true,
          profileImageUrl: true,
          phone: true,
          address: true,
          city: true,
          state: true,
          permissions: true,
          twoFactorEnabled: true,
          twoFactorMethod: true
        }
      });

      if (!user || !user.active) {
        return res.status(400).json({ message: 'Usuário não encontrado ou inativo' });
      }

      // Generate JWT token
      const tokenData = {
        id: user.id,
        iat: Math.floor(Date.now() / 1000),
        isClient: false,
        modules: user.modules,
        role: user.role
      };

      const jwtToken = jwt.sign(
        tokenData,
        process.env.JWT_SECRET,
        { expiresIn: JWT_EXPIRY }
      );

      // Log successful 2FA login
      await prisma.auditLog.create({
        data: {
          userId: user.id,
          action: 'LOGIN_WITH_2FA',
          entityType: 'User',
          entityId: user.id,
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: user.companyId
        }
      });

      await securitySettingsController.createSecurityLog({
        companyId: user.companyId,
        userId: user.id,
        action: 'TWO_FACTOR_VERIFICATION_SUCCESS',
        status: 'SUCCESS',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        details: { 
          tokenId: verificationResult.tokenId
        }
      });

      // Remove sensitive data
      const { twoFactorEnabled: _, twoFactorMethod: __, ...userWithoutSensitiveData } = user;

      res.json({
        user: userWithoutSensitiveData,
        token: jwtToken,
        message: 'Login realizado com sucesso'
      });
    } catch (error) {
      console.error('Error verifying 2FA token:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Resend 2FA token
   */
  static async resendTwoFactor(req, res) {
    try {
      const { userId } = req.body;

      if (!userId) {
        return res.status(400).json({ message: 'User ID é obrigatório' });
      }

      // Check if user exists and has 2FA enabled
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { 
          id: true, 
          twoFactorEnabled: true, 
          active: true 
        }
      });

      if (!user || !user.active) {
        return res.status(400).json({ message: 'Usuário não encontrado ou inativo' });
      }

      if (!user.twoFactorEnabled) {
        return res.status(400).json({ message: 'Autenticação de dois fatores não está ativada' });
      }

      // Send new 2FA token
      const tokenResult = await twoFactorService.sendEmailToken(
        userId, 
        req.ip || req.connection.remoteAddress,
        req.headers['user-agent']
      );

      // Log token resent
      await securitySettingsController.createSecurityLog({
        companyId: user.companyId,
        userId,
        action: 'TWO_FACTOR_TOKEN_RESENT',
        status: 'SUCCESS',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        details: { 
          reason: 'User requested resend'
        }
      });

      res.json({
        message: 'Novo token de verificação enviado para seu email',
        expiresAt: tokenResult.expiresAt
      });
    } catch (error) {
      console.error('Error resending 2FA token:', error);
      res.status(500).json({ message: 'Erro ao reenviar token de verificação' });
    }
  }
}

module.exports = AuthController;