"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(auth)/login/page",{

/***/ "(app-pages-browser)/./src/components/auth/TwoFactorModal.js":
/*!***********************************************!*\
  !*** ./src/components/auth/TwoFactorModal.js ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Loader2,Mail,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Loader2,Mail,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Loader2,Mail,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Loader2,Mail,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Loader2,Mail,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Loader2,Mail,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst TwoFactorModal = (param)=>{\n    let { isOpen, userId, expiresAt, onSuccess, onCancel, onResend } = param;\n    _s();\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isVerifying, setIsVerifying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResending, setIsResending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    // Calculate time left\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TwoFactorModal.useEffect\": ()=>{\n            if (!expiresAt) return;\n            const updateTimeLeft = {\n                \"TwoFactorModal.useEffect.updateTimeLeft\": ()=>{\n                    const now = new Date().getTime();\n                    const expiry = new Date(expiresAt).getTime();\n                    const diff = Math.max(0, expiry - now);\n                    setTimeLeft(Math.floor(diff / 1000));\n                }\n            }[\"TwoFactorModal.useEffect.updateTimeLeft\"];\n            updateTimeLeft();\n            const interval = setInterval(updateTimeLeft, 1000);\n            return ({\n                \"TwoFactorModal.useEffect\": ()=>clearInterval(interval)\n            })[\"TwoFactorModal.useEffect\"];\n        }\n    }[\"TwoFactorModal.useEffect\"], [\n        expiresAt\n    ]);\n    const formatTime = (seconds)=>{\n        const minutes = Math.floor(seconds / 60);\n        const remainingSeconds = seconds % 60;\n        return \"\".concat(minutes, \":\").concat(remainingSeconds.toString().padStart(2, '0'));\n    };\n    const handleTokenChange = (e)=>{\n        const value = e.target.value.replace(/\\D/g, '').slice(0, 6);\n        setToken(value);\n        setError('');\n    };\n    const handleVerifyToken = async (e)=>{\n        e.preventDefault();\n        if (token.length !== 6) {\n            setError('Token deve ter 6 dígitos');\n            return;\n        }\n        if (timeLeft <= 0) {\n            setError('Token expirou. Solicite um novo token.');\n            return;\n        }\n        setIsVerifying(true);\n        setError('');\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.post('/auth/verify-2fa', {\n                userId,\n                token\n            });\n            if (response.data.user && response.data.token) {\n                toast_success('Login realizado com sucesso!');\n                onSuccess(response.data);\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('2FA verification error:', error);\n            const message = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao verificar token';\n            setError(message);\n            toast_error(message);\n        } finally{\n            setIsVerifying(false);\n        }\n    };\n    const handleResendToken = async ()=>{\n        setIsResending(true);\n        setError('');\n        try {\n            if (onResend) {\n                const result = await onResend();\n                toast_success(result.message || 'Novo token enviado para seu email');\n            } else {\n                // Fallback to direct API call\n                const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.post('/auth/resend-2fa', {\n                    userId\n                });\n                if (response.data.expiresAt) {\n                    toast_success('Novo token enviado para seu email');\n                    // Reset timer with new expiration\n                    const now = new Date().getTime();\n                    const expiry = new Date(response.data.expiresAt).getTime();\n                    const diff = Math.max(0, expiry - now);\n                    setTimeLeft(Math.floor(diff / 1000));\n                }\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Resend 2FA error:', error);\n            const message = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || 'Erro ao reenviar token';\n            setError(message);\n            toast_error(message);\n        } finally{\n            setIsResending(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mr-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: \"Verifica\\xe7\\xe3o de Dois Fatores\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"Digite o c\\xf3digo enviado para seu email\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, undefined),\n                    timeLeft > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 text-green-600 dark:text-green-400 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 141,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-green-700 dark:text-green-300\",\n                                children: [\n                                    \"Token expira em: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: formatTime(timeLeft)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 143,\n                                        columnNumber: 34\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 142,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                        lineNumber: 140,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mb-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4 text-red-600 dark:text-red-400 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 148,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-red-700 dark:text-red-300\",\n                                children: \"Token expirado\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                        lineNumber: 147,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleVerifyToken,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                        children: \"C\\xf3digo de Verifica\\xe7\\xe3o\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: token,\n                                        onChange: handleTokenChange,\n                                        placeholder: \"123456\",\n                                        className: \"w-full px-4 py-3 text-center text-2xl font-mono tracking-widest border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                        maxLength: 6,\n                                        autoComplete: \"off\",\n                                        disabled: isVerifying || timeLeft <= 0\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-red-600 dark:text-red-400\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: token.length !== 6 || isVerifying || timeLeft <= 0,\n                                        className: \"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isVerifying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4 animate-spin mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Verificando...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Verificar C\\xf3digo\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: handleResendToken,\n                                                disabled: isResending,\n                                                className: \"flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                children: isResending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-4 h-4 animate-spin mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Reenviando...\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Reenviar\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: onCancel,\n                                                className: \"flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                children: \"Cancelar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-400 mt-0.5 mr-3 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: 'N\\xe3o recebeu o c\\xf3digo? Verifique sua caixa de spam ou clique em \"Reenviar\" para solicitar um novo c\\xf3digo.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 234,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TwoFactorModal, \"tEzSLLVn27kUs/jKoxjzb++bu5c=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TwoFactorModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TwoFactorModal);\nvar _c;\n$RefreshReg$(_c, \"TwoFactorModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/TwoFactorModal.js\n"));

/***/ })

});