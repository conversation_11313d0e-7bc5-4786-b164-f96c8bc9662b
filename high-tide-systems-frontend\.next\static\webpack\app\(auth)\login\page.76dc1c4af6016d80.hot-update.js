"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(auth)/login/page",{

/***/ "(app-pages-browser)/./src/components/auth/TwoFactorModal.js":
/*!***********************************************!*\
  !*** ./src/components/auth/TwoFactorModal.js ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Loader2,Mail,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Loader2,Mail,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Loader2,Mail,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Loader2,Mail,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Loader2,Mail,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clock,Loader2,Mail,RefreshCw,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst TwoFactorModal = (param)=>{\n    let { isOpen, userId, expiresAt, onSuccess, onCancel, onResend } = param;\n    _s();\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isVerifying, setIsVerifying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResending, setIsResending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    // Calculate time left\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TwoFactorModal.useEffect\": ()=>{\n            if (!expiresAt) return;\n            const updateTimeLeft = {\n                \"TwoFactorModal.useEffect.updateTimeLeft\": ()=>{\n                    const now = new Date().getTime();\n                    const expiry = new Date(expiresAt).getTime();\n                    const diff = Math.max(0, expiry - now);\n                    setTimeLeft(Math.floor(diff / 1000));\n                }\n            }[\"TwoFactorModal.useEffect.updateTimeLeft\"];\n            updateTimeLeft();\n            const interval = setInterval(updateTimeLeft, 1000);\n            return ({\n                \"TwoFactorModal.useEffect\": ()=>clearInterval(interval)\n            })[\"TwoFactorModal.useEffect\"];\n        }\n    }[\"TwoFactorModal.useEffect\"], [\n        expiresAt\n    ]);\n    const formatTime = (seconds)=>{\n        const minutes = Math.floor(seconds / 60);\n        const remainingSeconds = seconds % 60;\n        return \"\".concat(minutes, \":\").concat(remainingSeconds.toString().padStart(2, '0'));\n    };\n    const handleTokenChange = (e)=>{\n        const value = e.target.value.replace(/\\D/g, '').slice(0, 6);\n        setToken(value);\n        setError('');\n    };\n    const handleVerifyToken = async (e)=>{\n        e.preventDefault();\n        if (token.length !== 6) {\n            setError('Token deve ter 6 dígitos');\n            return;\n        }\n        if (timeLeft <= 0) {\n            setError('Token expirou. Solicite um novo token.');\n            return;\n        }\n        setIsVerifying(true);\n        setError('');\n        try {\n            if (onSuccess) {\n                await onSuccess(token); // Let the parent handle the API call\n            }\n        } catch (error) {\n            console.error('2FA verification error:', error);\n            const message = error.message || 'Erro ao verificar token';\n            setError(message);\n        } finally{\n            setIsVerifying(false);\n        }\n    };\n    const handleResendToken = async ()=>{\n        setIsResending(true);\n        setError('');\n        try {\n            if (onResend) {\n                const result = await onResend();\n                toast_success(result.message || 'Novo token enviado para seu email');\n            } else {\n                // Fallback to direct API call\n                const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.post('/auth/resend-2fa', {\n                    userId\n                });\n                if (response.data.expiresAt) {\n                    toast_success('Novo token enviado para seu email');\n                    // Reset timer with new expiration\n                    const now = new Date().getTime();\n                    const expiry = new Date(response.data.expiresAt).getTime();\n                    const diff = Math.max(0, expiry - now);\n                    setTimeLeft(Math.floor(diff / 1000));\n                }\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Resend 2FA error:', error);\n            const message = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message || 'Erro ao reenviar token';\n            setError(message);\n            toast_error(message);\n        } finally{\n            setIsResending(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mr-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: \"Verifica\\xe7\\xe3o de Dois Fatores\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"Digite o c\\xf3digo enviado para seu email\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, undefined),\n                    timeLeft > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 text-green-600 dark:text-green-400 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 134,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-green-700 dark:text-green-300\",\n                                children: [\n                                    \"Token expira em: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: formatTime(timeLeft)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 136,\n                                        columnNumber: 34\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 135,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                        lineNumber: 133,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center mb-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4 text-red-600 dark:text-red-400 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 141,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-red-700 dark:text-red-300\",\n                                children: \"Token expirado\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 142,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                        lineNumber: 140,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleVerifyToken,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                        children: \"C\\xf3digo de Verifica\\xe7\\xe3o\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: token,\n                                        onChange: handleTokenChange,\n                                        placeholder: \"123456\",\n                                        className: \"w-full px-4 py-3 text-center text-2xl font-mono tracking-widest border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                        maxLength: 6,\n                                        autoComplete: \"off\",\n                                        disabled: isVerifying || timeLeft <= 0\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-red-600 dark:text-red-400\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: token.length !== 6 || isVerifying || timeLeft <= 0,\n                                        className: \"w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isVerifying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4 animate-spin mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Verificando...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Verificar C\\xf3digo\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: handleResendToken,\n                                                disabled: isResending,\n                                                className: \"flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                children: isResending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-4 h-4 animate-spin mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Reenviando...\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Reenviar\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: onCancel,\n                                                className: \"flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                children: \"Cancelar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clock_Loader2_Mail_RefreshCw_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-400 mt-0.5 mr-3 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: 'N\\xe3o recebeu o c\\xf3digo? Verifique sua caixa de spam ou clique em \"Reenviar\" para solicitar um novo c\\xf3digo.'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                        lineNumber: 227,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                            lineNumber: 224,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n            lineNumber: 114,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\auth\\\\TwoFactorModal.js\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TwoFactorModal, \"tEzSLLVn27kUs/jKoxjzb++bu5c=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TwoFactorModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TwoFactorModal);\nvar _c;\n$RefreshReg$(_c, \"TwoFactorModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/TwoFactorModal.js\n"));

/***/ })

});