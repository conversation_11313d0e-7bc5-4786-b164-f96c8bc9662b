// src/routes/twoFactorRoutes.js
const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const TwoFactorController = require('../controllers/twoFactorController');
const { authenticate } = require('../middlewares/auth');
const { check2FAPolicy } = require('../middlewares/twoFactorMiddleware');

// All routes require authentication
router.use(authenticate);

// Get user's 2FA settings
router.get('/settings', TwoFactorController.getSettings);

// Get 2FA status
router.get('/status', TwoFactorController.getStatus);

// Enable 2FA
router.post('/enable', check2FAPolicy, [
  body('method').optional().isIn(['email']).withMessage('Método inválido')
], TwoFactorController.enable);

// Disable 2FA
router.post('/disable', check2FAPolicy, TwoFactorController.disable);

// Send test token
router.post('/test/send', TwoFactorController.sendTestToken);

// Verify test token
router.post('/test/verify', [
  body('token').isLength({ min: 6, max: 6 }).withMessage('Token deve ter 6 dígitos')
], TwoFactorController.verifyTestToken);

module.exports = router;