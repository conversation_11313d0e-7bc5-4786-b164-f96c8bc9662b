'use client';

import { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { companyService } from '@/app/modules/admin/services/companyService';
import { useToast } from '@/contexts/ToastContext';

const CompanySelectionContext = createContext({});

export function CompanySelectionProvider({ children }) {
  const { user, isSystemAdmin } = useAuth();
  const { toast_error } = useToast();
  
  const [selectedCompanyId, setSelectedCompanyId] = useState(null);
  const [companies, setCompanies] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState(null);

  // Carregar empresas quando for SYSTEM_ADMIN
  useEffect(() => {
    async function loadCompanies() {
      if (!isSystemAdmin()) return;
      
      try {
        setIsLoading(true);
        const companiesData = await companyService.getCompaniesForSelect();
        setCompanies(companiesData);
      } catch (error) {
        console.error('Erro ao carregar empresas:', error);
        toast_error('Erro ao carregar lista de empresas');
      } finally {
        setIsLoading(false);
      }
    }

    loadCompanies();
  }, [isSystemAdmin, toast_error]);

  // Atualizar empresa selecionada quando a lista ou ID mudar
  useEffect(() => {
    if (selectedCompanyId && companies.length > 0) {
      const company = companies.find(c => c.id === selectedCompanyId);
      setSelectedCompany(company || null);
    } else {
      setSelectedCompany(null);
    }
  }, [selectedCompanyId, companies]);

  // Para não-SYSTEM_ADMIN, usar a empresa do usuário e limpar localStorage
  useEffect(() => {
    if (user && !isSystemAdmin()) {
      setSelectedCompanyId(user.companyId);
      setSelectedCompany(user.company || null);
      
      // Limpar qualquer empresa salva no localStorage quando não for SYSTEM_ADMIN
      if (typeof window !== 'undefined') {
        localStorage.removeItem('systemAdminSelectedCompany');
      }
    }
  }, [user, isSystemAdmin]);

  const handleCompanyChange = (companyId) => {
    if (isSystemAdmin()) {
      setSelectedCompanyId(companyId || null);
      
      // Salvar no localStorage para persistir entre sessões
      if (typeof window !== 'undefined') {
        if (companyId) {
          localStorage.setItem('systemAdminSelectedCompany', companyId);
        } else {
          localStorage.removeItem('systemAdminSelectedCompany');
        }
      }
    }
  };

  // Restaurar empresa selecionada do localStorage (apenas para SYSTEM_ADMIN)
  useEffect(() => {
    if (user && isSystemAdmin() && typeof window !== 'undefined') {
      const savedCompanyId = localStorage.getItem('systemAdminSelectedCompany');
      if (savedCompanyId) {
        setSelectedCompanyId(savedCompanyId);
      }
    }
  }, [user, isSystemAdmin]);

  // Obter ID da empresa efetiva (para usar nas APIs)
  const getEffectiveCompanyId = () => {
    if (isSystemAdmin()) {
      return selectedCompanyId; // Pode ser null para "Sem Empresa"
    } else {
      return user?.companyId || null;
    }
  };

  // Verificar se o SYSTEM_ADMIN está operando sem empresa
  const isOperatingWithoutCompany = () => {
    return isSystemAdmin() && !selectedCompanyId;
  };

  const value = {
    // Estados
    selectedCompanyId,
    companies,
    isLoading,
    selectedCompany,
    
    // Funções
    handleCompanyChange,
    getEffectiveCompanyId,
    isOperatingWithoutCompany,
    
    // Flags
    canSelectCompany: isSystemAdmin(),
    hasMultipleCompanies: companies.length > 1
  };

  return (
    <CompanySelectionContext.Provider value={value}>
      {children}
    </CompanySelectionContext.Provider>
  );
}

export const useCompanySelection = () => {
  const context = useContext(CompanySelectionContext);
  if (!context) {
    throw new Error('useCompanySelection deve ser usado dentro de um CompanySelectionProvider');
  }
  return context;
};