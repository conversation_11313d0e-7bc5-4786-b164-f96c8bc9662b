// src/app/modules/admin/logs/auditLogService.js
import { api } from '@/utils/api';

export const auditLogService = {
  /**
   * Buscar logs de auditoria com paginação e filtros
   * @param {Object} filters - Filtros para a busca
   * @returns {Promise} - Lista de logs e metadados de paginação
   */
  getLogs: async (filters = {}) => {
    const { page = 1, limit = 20, search, startDate, endDate, action, entityType, userId } = filters;
    
    const response = await api.get('/audit-logs', {
      params: {
        page,
        limit,
        search: search || undefined,
        startDate: startDate || undefined,
        endDate: endDate || undefined,
        action: action || undefined,
        entityType: entityType || undefined,
        userId: userId || undefined,
      },
    });
    
    return response.data;
  },
  
  /**
   * Exportar logs usando o exportService padrão
   * @param {Object} filters - Filtros para a exportação
   * @param {String} format - Formato do arquivo (xlsx, pdf, image)
   * @returns {Promise} - Sucesso da exportação
   */
  exportLogs: async (filters = {}, format = 'xlsx') => {
    const { exportService } = await import('@/app/services/exportService');
    
    try {
      // Buscar todos os logs com os filtros aplicados
      const response = await api.get('/audit-logs', {
        params: {
          ...filters,
          page: 1,
          limit: 10000,
        },
      });
      
      const logs = response.data.logs || [];
      
      // Definir colunas para exportação
      const columns = [
        {
          key: 'createdAt',
          header: 'Data/Hora',
          format: (value) => new Date(value).toLocaleString('pt-BR')
        },
        {
          key: 'userName',
          header: 'Usuário',
          format: (value) => value || 'Sistema'
        },
        {
          key: 'action',
          header: 'Ação',
          format: (value) => {
            const actionMap = {
              'CREATE': 'Criação',
              'UPDATE': 'Atualização',
              'DELETE': 'Exclusão',
              'LOGIN': 'Login',
              'LOGOUT': 'Logout'
            };
            return actionMap[value] || value;
          }
        },
        {
          key: 'entityType',
          header: 'Categoria',
          format: (value) => {
            const entityMap = {
              'User': 'Usuário',
              'Client': 'Cliente',
              'Scheduling': 'Agendamento'
            };
            return entityMap[value] || value;
          }
        }
      ];
      
      // Usar o exportService padrão
      return await exportService.exportData(logs, {
        format,
        filename: 'historico-atividades',
        columns,
        title: 'Histórico de Atividades'
      });
      
    } catch (error) {
      console.error('Erro ao exportar logs:', error);
      throw error;
    }
  },
  
  /**
   * Buscar detalhes de um log específico
   * @param {String} id - ID do log
   * @returns {Promise} - Detalhes do log
   */
  getLogDetails: async (id) => {
    const response = await api.get(`/audit-logs/${id}`);
    return response.data;
  },
  
  /**
   * Obter estatísticas de logs de auditoria
   * @param {Number} days - Número de dias para analisar
   * @returns {Promise} - Estatísticas de logs
   */
  getStats: async (days = 30) => {
    const response = await api.get('/audit-logs/stats', {
      params: { days }
    });
    return response.data;
  },
  
  /**
   * Limpar logs antigos (apenas para administradores)
   * @param {Number} olderThan - Remover logs mais antigos que X dias
   * @returns {Promise} - Resultado da operação
   */
  cleanupOldLogs: async (olderThan = 365) => {
    const response = await api.post('/audit-logs/cleanup', {
      olderThan
    });
    return response.data;
  }
};