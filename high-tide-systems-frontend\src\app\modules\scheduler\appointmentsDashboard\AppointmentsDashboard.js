"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  <PERSON>Chart,
  Line,
  Legend
} from "recharts";
import {
  Users,
  Calendar,
  TrendingUp,
  CheckCircle2,
  XCircle,
  AlertTriangle,
  Pause,
  ThumbsUp,
  BarChart2,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON>hartIcon,
  MapPin,
  Briefcase,
  Filter,
  ChevronDown,
  LayoutDashboard
} from "lucide-react";
import { ModuleHeader, ModuleDatePicker, ModuleFormGroup } from "@/components/ui";
import ExportMenu from "@/components/ui/ExportMenu";
import { appointmentDashboardService } from "@/app/modules/scheduler/services/appointmentDashboardService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { APPOINTMENT_STATUS } from "@/app/modules/scheduler/calendar/utils/appointmentConstants";
import { useAuth } from "@/contexts/AuthContext";
import { AppointmentsDashboardFilters } from "@/components/scheduler/AppointmentsDashboardFilters";

const AppointmentsDashboard = () => {
  // Obter contexto de autenticação
  const { user } = useAuth();
  const isSystemAdmin = user?.role === 'SYSTEM_ADMIN';

  // State for dashboard data
  const [stats, setStats] = useState({
    totalAppointments: 0,
    completedAppointments: 0,
    cancelledAppointments: 0,
    noShowAppointments: 0,
    pendingAppointments: 0,
    confirmedAppointments: 0
  });
  const [topPatients, setTopPatients] = useState([]);
  const [topServices, setTopServices] = useState([]);
  const [topLocations, setTopLocations] = useState([]);
  const [statusDistribution, setStatusDistribution] = useState([]);
  const [appointmentTrends, setAppointmentTrends] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState("30dias");
  const [chartType, setChartType] = useState("bar"); // 'bar' or 'pie'

  // Estados para filtro por empresa
  const [companies, setCompanies] = useState([]);
  const [selectedCompany, setSelectedCompany] = useState("");

  // Estado para datas personalizadas
  const [period, setPeriod] = useState(selectedPeriod);
  const [startDate, setStartDate] = useState(new Date(new Date().setDate(new Date().getDate() - 30)));
  const [endDate, setEndDate] = useState(new Date());

  // Colors for the charts - usando cores do módulo scheduler
  const SCHEDULER_COLORS = ["#f97316", "#fb923c", "#fdba74", "#ffedd5", "#fff7ed"];
  const PATIENT_COLORS = SCHEDULER_COLORS;
  const SERVICE_COLORS = SCHEDULER_COLORS;
  const LOCATION_COLORS = SCHEDULER_COLORS;
  const STATUS_COLORS = [
    APPOINTMENT_STATUS.PENDING.color,
    APPOINTMENT_STATUS.CONFIRMED.color,
    APPOINTMENT_STATUS.COMPLETED.color,
    APPOINTMENT_STATUS.CANCELLED.color,
    APPOINTMENT_STATUS.NO_SHOW.color
  ];

  // Função para formatar data para input
  const formatDateForInput = (date) => {
    if (!date) return "";
    const d = new Date(date);
    return d.toISOString().split('T')[0]; // Formato YYYY-MM-DD
  };

  // Função para criar uma data segura
  const createSafeDate = (dateString) => {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      throw new Error('Data inválida');
    }
    return date;
  };

  // Função para carregar empresas
  const loadCompanies = async () => {
    try {
      const companiesData = await companyService.getCompaniesForSelect();
      setCompanies(companiesData);
    } catch (error) {
      console.error("Erro ao carregar empresas:", error);
    }
  };

  // Load dashboard data
  const loadDashboardData = async () => {
    console.log("[AppointmentsDashboard] Carregando dados do dashboard...");
    setIsLoading(true);
    try {
      let params = {};

      // Adicionar filtro por empresa se selecionado
      if (selectedCompany) {
        params.companyId = selectedCompany;
      }

      console.log("Parâmetros enviados para o dashboard:", params);

      // Adicionar parâmetros com base no período selecionado
      if (period === 'custom') {
        try {
          // Verificar se as datas são válidas
          if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            throw new Error('Datas inválidas para o período personalizado');
          }

          // Para período personalizado, usar datas de início e fim explícitas
          params.startDate = startDate.toISOString();
          params.endDate = endDate.toISOString();
        } catch (error) {
          console.error('Erro com datas personalizadas:', error);
          setIsLoading(false);
          return;
        }
      } else {
        // Para períodos padrão, usar o mapeamento
        const periodMap = {
          // Períodos passados
          "7dias": "7days",
          "30dias": "30days",
          "3meses": "3months",
          "1ano": "1year",
          // Períodos futuros
          "next7dias": "next7days",
          "next30dias": "next30days"
        };
        params.period = periodMap[selectedPeriod] || "30days";
      }

      const data = await appointmentDashboardService.getAppointmentsDashboardData(params);

      // Process data before setting to state for better display
      const processedPatients = (data.topPatients || []).map(patient => ({
        ...patient,
        // Limitar tamanho do nome para evitar corte
        name: patient.name?.length > 20 ? patient.name.substring(0, 18) + '...' : patient.name,
        // Guardar o nome completo para tooltips
        fullName: patient.name
      }));

      const processedServices = (data.topServices || []).map(service => ({
        ...service,
        name: service.name?.length > 20 ? service.name.substring(0, 18) + '...' : service.name,
        fullName: service.name
      }));

      const processedLocations = (data.topLocations || []).map(location => ({
        ...location,
        name: location.name?.length > 20 ? location.name.substring(0, 18) + '...' : location.name,
        fullName: location.name
      }));

      setStats(data.stats || {});
      setTopPatients(processedPatients);
      setTopServices(processedServices);
      setTopLocations(processedLocations);
      setStatusDistribution(data.statusDistribution || []);
      setAppointmentTrends(data.appointmentTrends || []);
    } catch (error) {
      console.error("Error loading dashboard data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Effect to load companies when component mounts (apenas para system_admin)
  useEffect(() => {
    if (isSystemAdmin) {
      loadCompanies();
    }
  }, [isSystemAdmin]);

  // Sincronizar period com selectedPeriod
  useEffect(() => {
    setPeriod(selectedPeriod);
  }, [selectedPeriod]);

  // Load data when period, company or custom dates change
  useEffect(() => {
    loadDashboardData();
  }, [period, selectedCompany, period === 'custom' ? startDate : null, period === 'custom' ? endDate : null]);

  // Format percentage for display
  const formatPercentage = (value) => {
    if (stats.totalAppointments === 0) return "0%";
    return `${Math.round((value / stats.totalAppointments) * 100)}%`;
  };

  // Função para exportar os dados do dashboard
  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      // Preparar os parâmetros para a exportação
      let params = {};

      // Adicionar parâmetros com base no período selecionado
      if (period === 'custom') {
        params.startDate = startDate.toISOString();
        params.endDate = endDate.toISOString();
      } else {
        // Para períodos padrão, usar o mapeamento
        const periodMap = {
          "7dias": "7days",
          "30dias": "30days",
          "3meses": "3months",
          "1ano": "1year",
          "next7dias": "next7days",
          "next30dias": "next30days"
        };
        params.period = periodMap[period] || "30days";
      }

      // Criar um objeto com todos os dados do dashboard
      const dashboardData = {
        stats,
        topPatients,
        topServices,
        topLocations,
        statusDistribution,
        appointmentTrends
      };

      // Chamar o serviço de exportação
      await appointmentDashboardService.exportDashboardData(dashboardData, params, format);
    } catch (error) {
      console.error("Erro ao exportar dados do dashboard:", error);
    } finally {
      setIsExporting(false);
    }
  };



  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-2 border border-gray-200 dark:border-gray-700 rounded shadow">
          <p className="font-medium">
            {payload[0]?.payload?.fullName || label}
          </p>
          {payload.map((entry, index) => (
            <p key={index} style={{ color: entry.color }}>
              {entry.name}: {entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Função para alterar empresa
  const handleCompanyChange = (e) => {
    setSelectedCompany(e.target.value);
  };

  // Handler para mudança de filtros
  const handleFiltersChange = (newFilters) => {
    if (newFilters.period !== undefined) setPeriod(newFilters.period);
    if (newFilters.selectedCompany !== undefined) setSelectedCompany(newFilters.selectedCompany);
    if (newFilters.startDate !== undefined) setStartDate(newFilters.startDate);
    if (newFilters.endDate !== undefined) setEndDate(newFilters.endDate);
  };

  // Handler para busca/atualização
  const handleSearch = (searchFilters) => {
    // Os filtros já foram aplicados pelo handleFiltersChange
    // Aqui apenas forçamos o reload dos dados
    loadDashboardData();
  };

  return (
    <div className="space-y-6">
      {/* Título padronizado igual outras páginas */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <LayoutDashboard size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
          Dashboard de Agendamentos
        </h1>
        <div className="flex items-center gap-2">
          <ExportMenu
            onExport={handleExport}
            isExporting={isExporting}
            disabled={isLoading}
            className="text-purple-600 dark:text-purple-400"
          />
        </div>
      </div>

      {/* Cabeçalho da página com filtros integrados */}
      <ModuleHeader
        title="Filtros e Período"
        description="Análise e estatísticas dos agendamentos. Utilize os filtros abaixo para personalizar a visualização dos dados."
        icon={<Filter size={22} className="text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />}
        moduleColor="scheduler"
        filters={
          <AppointmentsDashboardFilters
            filters={{
              period,
              selectedCompany,
              startDate,
              endDate
            }}
            onFiltersChange={handleFiltersChange}
            onSearch={handleSearch}
            isLoading={isLoading}
            companies={isSystemAdmin ? companies : []}
          />
        }
        showAddButton={false}
      />

      {/* Seletor de tipo de gráfico */}
      <div className="flex justify-end">
        <div className="flex border border-scheduler-border dark:border-scheduler-border-dark rounded-lg overflow-hidden">
          <button
            className={`p-2 ${
              chartType === "bar"
                ? "bg-scheduler-500 text-white"
                : "bg-white dark:bg-gray-700 text-neutral-700 dark:text-neutral-300"
            }`}
            onClick={() => setChartType("bar")}
            title="Gráfico de barras"
          >
            <BarChart2 className="h-5 w-5" />
          </button>
          <button
            className={`p-2 ${
              chartType === "pie"
                ? "bg-scheduler-500 text-white"
                : "bg-white dark:bg-gray-700 text-neutral-700 dark:text-neutral-300"
            }`}
            onClick={() => setChartType("pie")}
            title="Gráfico de pizza"
          >
            <PieChartIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Loading state */}
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500 dark:border-primary-400"></div>
        </div>
      ) : (
        <>
          {/* Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Appointments */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-scheduler-border dark:border-scheduler-border-dark">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                    Total de Agendamentos
                  </p>
                  <h3 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2">
                    {stats.totalAppointments}
                  </h3>
                </div>
                <div className="h-12 w-12 bg-scheduler-100 dark:bg-scheduler-900/30 rounded-full flex items-center justify-center">
                  <Calendar className="h-6 w-6 text-scheduler-600 dark:text-scheduler-400" />
                </div>
              </div>
            </div>



            {/* Completed Appointments */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-scheduler-border dark:border-scheduler-border-dark">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                    Concluídos
                  </p>
                  <h3 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2">
                    {stats.completedAppointments}
                  </h3>
                </div>
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                  <CheckCircle2 className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-xs">
                <span className="text-green-500 dark:text-green-400">
                  {formatPercentage(stats.completedAppointments)}
                </span>
                <span className="text-neutral-500 dark:text-neutral-400 ml-2">
                  do total
                </span>
              </div>
            </div>

            {/* Confirmed Appointments */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-scheduler-border dark:border-scheduler-border-dark">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                    Confirmados
                  </p>
                  <h3 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2">
                    {stats.confirmedAppointments}
                  </h3>
                </div>
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                  <ThumbsUp className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-xs">
                <span className="text-blue-500 dark:text-blue-400">
                  {formatPercentage(stats.confirmedAppointments)}
                </span>
                <span className="text-neutral-500 dark:text-neutral-400 ml-2">
                  do total
                </span>
              </div>
            </div>

            {/* Pending Appointments */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-scheduler-border dark:border-scheduler-border-dark">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                    Pendentes
                  </p>
                  <h3 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2">
                    {stats.pendingAppointments}
                  </h3>
                </div>
                <div className="h-12 w-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center">
                  <Pause className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-xs">
                <span className="text-yellow-500 dark:text-yellow-400">
                  {formatPercentage(stats.pendingAppointments)}
                </span>
                <span className="text-neutral-500 dark:text-neutral-400 ml-2">
                  do total
                </span>
              </div>
            </div>

            {/* Cancelled Appointments */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-scheduler-border dark:border-scheduler-border-dark">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                    Cancelados
                  </p>
                  <h3 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2">
                    {stats.cancelledAppointments}
                  </h3>
                </div>
                <div className="h-12 w-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
                  <XCircle className="h-6 w-6 text-red-600 dark:text-red-400" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-xs">
                <span className="text-red-500 dark:text-red-400">
                  {formatPercentage(stats.cancelledAppointments)}
                </span>
                <span className="text-neutral-500 dark:text-neutral-400 ml-2">
                  do total
                </span>
              </div>
            </div>

            {/* No-Show Appointments */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-scheduler-border dark:border-scheduler-border-dark">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                    Não Compareceram
                  </p>
                  <h3 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2">
                    {stats.noShowAppointments}
                  </h3>
                </div>
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                  <AlertTriangle className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-xs">
                <span className="text-purple-500 dark:text-purple-400">
                  {formatPercentage(stats.noShowAppointments)}
                </span>
                <span className="text-neutral-500 dark:text-neutral-400 ml-2">
                  do total
                </span>
              </div>
            </div>
          </div>

          {/* Charts Row */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6"
          id="gradedashboards">
            {/* Top Patients Chart */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-scheduler-border dark:border-scheduler-border-dark">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 flex items-center">
                  <Users className="h-5 w-5 mr-2 text-scheduler-500 dark:text-scheduler-400" />
                  Pacientes Mais Agendados
                </h3>
              </div>

              <div className="h-80">
                {chartType === "bar" ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={topPatients}
                      margin={{ top: 5, right: 30, left: 20, bottom: 50 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" className="dark:stroke-gray-700" />
                      <XAxis
                        dataKey="name"
                        tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                        angle={-45}
                        textAnchor="end"
                        height={60}
                      />
                      <YAxis
                        tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Bar dataKey="count" name="Agendamentos" fill="#f97316">
                        {topPatients.map((_, index) => (
                          <Cell key={`cell-${index}`} fill={PATIENT_COLORS[index % PATIENT_COLORS.length]} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={topPatients}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        dataKey="count"
                        nameKey="name"
                        label={({ percent }) => `${(percent * 100).toFixed(0)}%`}
                      >
                        {topPatients.map((_, index) => (
                          <Cell key={`cell-${index}`} fill={PATIENT_COLORS[index % PATIENT_COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip content={<CustomTooltip />} />
                    </PieChart>
                  </ResponsiveContainer>
                )}
              </div>

              {chartType === "pie" && (
                <div className="mt-4 grid grid-cols-1 gap-2">
                  {topPatients.map((patient, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div
                          className="h-3 w-3 rounded-full mr-2"
                          style={{ backgroundColor: PATIENT_COLORS[index % PATIENT_COLORS.length] }}
                        ></div>
                        <span className="text-sm text-neutral-700 dark:text-neutral-300 truncate max-w-[150px]" title={patient.fullName}>
                          {patient.name}
                        </span>
                      </div>
                      <span className="text-sm font-medium text-neutral-800 dark:text-neutral-100">
                        {patient.count}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Top Services Chart */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-scheduler-border dark:border-scheduler-border-dark">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 flex items-center">
                  <Briefcase className="h-5 w-5 mr-2 text-scheduler-500 dark:text-scheduler-400" />
                  Serviços Mais Agendados
                </h3>
              </div>

              <div className="h-80">
                {chartType === "bar" ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={topServices}
                      margin={{ top: 5, right: 30, left: 20, bottom: 50 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" className="dark:stroke-gray-700" />
                      <XAxis
                        dataKey="name"
                        tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                        angle={-45}
                        textAnchor="end"
                        height={60}
                      />
                      <YAxis
                        tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Bar dataKey="count" name="Agendamentos" fill="#f97316">
                        {topServices.map((_, index) => (
                          <Cell key={`cell-${index}`} fill={SERVICE_COLORS[index % SERVICE_COLORS.length]} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={topServices}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        dataKey="count"
                        nameKey="name"
                        label={({ percent }) => `${(percent * 100).toFixed(0)}%`}
                      >
                        {topServices.map((_, index) => (
                          <Cell key={`cell-${index}`} fill={SERVICE_COLORS[index % SERVICE_COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip content={<CustomTooltip />} />
                    </PieChart>
                  </ResponsiveContainer>
                )}
              </div>

              {chartType === "pie" && (
                <div className="mt-4 grid grid-cols-1 gap-2">
                  {topServices.map((service, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div
                          className="h-3 w-3 rounded-full mr-2"
                          style={{ backgroundColor: SERVICE_COLORS[index % SERVICE_COLORS.length] }}
                        ></div>
                        <span className="text-sm text-neutral-700 dark:text-neutral-300 truncate max-w-[150px]" title={service.fullName}>
                          {service.name}
                        </span>
                      </div>
                      <span className="text-sm font-medium text-neutral-800 dark:text-neutral-100">
                        {service.count}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Top Locations Chart */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-scheduler-border dark:border-scheduler-border-dark">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 flex items-center">
                  <MapPin className="h-5 w-5 mr-2 text-scheduler-500 dark:text-scheduler-400" />
                  Locais Mais Agendados
                </h3>
              </div>

              <div className="h-80">
                {chartType === "bar" ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={topLocations}
                      margin={{ top: 5, right: 30, left: 20, bottom: 50 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" className="dark:stroke-gray-700" />
                      <XAxis
                        dataKey="name"
                        tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                        angle={-45}
                        textAnchor="end"
                        height={60}
                      />
                      <YAxis
                        tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Bar dataKey="count" name="Agendamentos" fill="#f97316">
                        {topLocations.map((_, index) => (
                          <Cell key={`cell-${index}`} fill={LOCATION_COLORS[index % LOCATION_COLORS.length]} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={topLocations}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        dataKey="count"
                        nameKey="name"
                        label={({ percent }) => `${(percent * 100).toFixed(0)}%`}
                      >
                        {topLocations.map((_, index) => (
                          <Cell key={`cell-${index}`} fill={LOCATION_COLORS[index % LOCATION_COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip content={<CustomTooltip />} />
                    </PieChart>
                  </ResponsiveContainer>
                )}
              </div>

              {chartType === "pie" && (
                <div className="mt-4 grid grid-cols-1 gap-2">
                  {topLocations.map((location, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div
                          className="h-3 w-3 rounded-full mr-2"
                          style={{ backgroundColor: LOCATION_COLORS[index % LOCATION_COLORS.length] }}
                        ></div>
                        <span className="text-sm text-neutral-700 dark:text-neutral-300 truncate max-w-[150px]" title={location.fullName}>
                          {location.name}
                        </span>
                      </div>
                      <span className="text-sm font-medium text-neutral-800 dark:text-neutral-100">
                        {location.count}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>



          {/* Bottom Charts - Status and Trends */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Status Distribution */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-scheduler-border dark:border-scheduler-border-dark">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 flex items-center">
                  <PieChartIcon className="h-5 w-5 mr-2 text-scheduler-500 dark:text-scheduler-400" />
                  Distribuição por Status
                </h3>
              </div>

              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={statusDistribution}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={90}
                      paddingAngle={5}
                      dataKey="count"
                      nameKey="label"
                    >
                      {statusDistribution.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={STATUS_COLORS[Object.keys(APPOINTMENT_STATUS).indexOf(entry.status)] || "#999"}
                        />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [`${value} agendamentos`, name]} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Appointment Trends */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-scheduler-border dark:border-scheduler-border-dark">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-scheduler-500 dark:text-scheduler-400" />
                  Tendência de Agendamentos
                </h3>
              </div>

              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={appointmentTrends}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" className="dark:stroke-gray-700" />
                    <XAxis
                      dataKey="date"
                      tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                      tickFormatter={(value) => {
                        // Format the date to be more readable
                        const date = new Date(value);
                        return date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
                      }}
                    />
                    <YAxis tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }} />
                    <Tooltip
                      labelFormatter={(value) => {
                        // Format the date for the tooltip
                        const date = new Date(value);
                        return date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit', year: 'numeric' });
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="count"
                      name="Agendamentos"
                      stroke="#f97316"
                      activeDot={{ r: 8 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default AppointmentsDashboard;