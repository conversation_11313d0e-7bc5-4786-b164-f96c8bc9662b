"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar/index.js":
/*!***************************************************!*\
  !*** ./src/components/dashboard/Sidebar/index.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Menu,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Menu,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Menu,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/dashboard/components */ \"(app-pages-browser)/./src/app/dashboard/components.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useSchedulingPreferences */ \"(app-pages-browser)/./src/hooks/useSchedulingPreferences.js\");\n/* harmony import */ var _components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/CustomScrollArea */ \"(app-pages-browser)/./src/components/ui/CustomScrollArea.js\");\n/* harmony import */ var _hooks_useConstructionMessage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useConstructionMessage */ \"(app-pages-browser)/./src/hooks/useConstructionMessage.js\");\n/* harmony import */ var _components_construction__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/construction */ \"(app-pages-browser)/./src/components/construction/index.js\");\n/* harmony import */ var _utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/constructionUtils */ \"(app-pages-browser)/./src/utils/constructionUtils.js\");\n/* harmony import */ var _components_dashboard_CompanySelector__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/dashboard/CompanySelector */ \"(app-pages-browser)/./src/components/dashboard/CompanySelector.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst ModuleTitle = (param)=>{\n    let { moduleId, title, icon: Icon, isCollapsed } = param;\n    // Exibir o logo no lugar do título do módulo, como botão para /dashboard\n    const handleLogoClick = ()=>{\n        if (true) {\n            window.location.href = '/dashboard';\n        }\n    };\n    if (isCollapsed) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-6 flex justify-center items-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleLogoClick,\n                className: \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 focus:outline-none cursor-pointer\",\n                \"aria-label\": \"Ir para o dashboard\",\n                title: \"High Tide - Ir para dashboard\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/logo_icon_sem_fundo.png\",\n                    alt: \"High Tide\",\n                    className: \"w-8 h-8 dark:invert transition-all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 33,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6 flex justify-center items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleLogoClick,\n            style: {\n                background: 'none',\n                border: 'none',\n                padding: 0\n            },\n            className: \"focus:outline-none cursor-pointer\",\n            \"aria-label\": \"Ir para o dashboard\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/logo_horizontal_sem_fundo.png\",\n                    alt: \"High Tide Logo\",\n                    className: \"h-12 dark:invert dark:text-white transition-all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 52,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ModuleTitle;\n// Mapeamento de submenus para permissões\nconst submenuPermissionsMap = {\n    // Admin\n    'admin.introduction': 'admin.dashboard.view',\n    'admin.dashboard': 'admin.dashboard.view',\n    'admin.users': 'admin.users.view',\n    'admin.professions': [\n        'admin.professions.view',\n        'admin.profession-groups.view'\n    ],\n    'admin.bug-reports': 'admin.dashboard.view',\n    'admin.plans': 'admin.dashboard.view',\n    'admin.logs': 'admin.logs.view',\n    'admin.settings': 'admin.config.edit',\n    'admin.backup': 'admin.config.edit',\n    'admin.affiliates': 'admin.dashboard.view',\n    // ABA+\n    'abaplus.anamnese': 'abaplus.anamnese.view',\n    'abaplus.evolucoes-diarias': 'abaplus.evolucoes-diarias.view',\n    'abaplus.sessao': 'abaplus.sessao.view',\n    // Financeiro\n    'financial.invoices': 'financial.invoices.view',\n    'financial.payments': 'financial.payments.view',\n    'financial.expenses': 'financial.expenses.view',\n    'financial.reports': 'financial.reports.view',\n    'financial.cashflow': 'financial.reports.view',\n    // RH\n    'hr.employees': 'rh.employees.view',\n    'hr.payroll': 'rh.payroll.view',\n    'hr.documents': 'rh.employees.view',\n    'hr.departments': 'rh.employees.view',\n    'hr.attendance': 'rh.attendance.view',\n    'hr.benefits': 'rh.benefits.view',\n    'hr.training': 'rh.employees.view',\n    // Pessoas\n    'people.clients': 'people.clients.view',\n    'people.persons': 'people.persons.view',\n    'people.documents': 'people.documents.view',\n    'people.insurances': 'people.insurances.view',\n    'people.insurance-limits': 'people.insurance-limits.view',\n    // Agendamento\n    'scheduler.calendar': 'scheduling.calendar.view',\n    'scheduler.working-hours': 'scheduling.working-hours.view',\n    'scheduler.service-types': 'scheduling.service-types.view',\n    'scheduler.locations': 'scheduling.locations.view',\n    'scheduler.occupancy': 'scheduling.occupancy.view',\n    'scheduler.appointment-requests': 'scheduling.appointment-requests.view',\n    'scheduler.appointments-report': 'scheduling.appointments-report.view',\n    'scheduler.appointments-dashboard': 'scheduling.appointments-dashboard.view'\n};\nconst Sidebar = (param)=>{\n    let { activeModule, activeModuleTitle, isSubmenuActive, handleModuleSubmenuClick, handleBackToModules, isSidebarOpen } = param;\n    var _moduleSubmenus_activeModule;\n    _s();\n    const { can, hasModule, isAdmin } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { schedulingPreferences, isLoading: preferencesLoading } = (0,_hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__.useSchedulingPreferences)();\n    // Estado para controlar se a sidebar está colapsada\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Sidebar.useState\": ()=>{\n            if (true) {\n                const savedCollapsed = localStorage.getItem('sidebarCollapsed');\n                return savedCollapsed === 'true';\n            }\n            return false;\n        }\n    }[\"Sidebar.useState\"]);\n    // Inicializar o estado de grupos expandidos a partir do localStorage\n    const [expandedGroups, setExpandedGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Sidebar.useState\": ()=>{\n            // Verificar se estamos no cliente (browser) antes de acessar localStorage\n            if (true) {\n                const savedState = localStorage.getItem('sidebarExpandedGroups');\n                return savedState ? JSON.parse(savedState) : {};\n            }\n            return {};\n        }\n    }[\"Sidebar.useState\"]);\n    // Encontrar o objeto do módulo ativo para acessar seu ícone\n    const activeModuleObject = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.modules.find((m)=>m.id === activeModule);\n    const ModuleIcon = activeModuleObject === null || activeModuleObject === void 0 ? void 0 : activeModuleObject.icon;\n    // Função para verificar se o usuário tem permissão para ver um submenu\n    const hasPermissionForSubmenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenu]\": (moduleId, submenuId)=>{\n            var _moduleSubmenus_moduleId;\n            // Ignorar verificação para grupos, pois a permissão é verificada para cada item\n            if (submenuId === 'cadastro' || submenuId === 'configuracoes' || submenuId === 'gestao' || submenuId === 'convenios' || submenuId === 'financeiro' || submenuId === 'relatorios') {\n                return true;\n            }\n            // Buscar o submenu específico para verificar systemAdminOnly\n            const submenuItem = (_moduleSubmenus_moduleId = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId]) === null || _moduleSubmenus_moduleId === void 0 ? void 0 : _moduleSubmenus_moduleId.find({\n                \"Sidebar.useCallback[hasPermissionForSubmenu]\": (item)=>item.id === submenuId\n            }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"]);\n            // Verificar se o submenu requer SYSTEM_ADMIN\n            if (submenuItem === null || submenuItem === void 0 ? void 0 : submenuItem.systemAdminOnly) {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            // Verificação especial adicional para afiliados - apenas SYSTEM_ADMIN\n            if (submenuId === 'affiliates') {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            // Verificação especial adicional para bug-reports - apenas SYSTEM_ADMIN\n            if (submenuId === 'bug-reports') {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            const permissionKey = \"\".concat(moduleId, \".\").concat(submenuId);\n            const requiredPermission = submenuPermissionsMap[permissionKey];\n            // Se não há mapeamento de permissão, permitir acesso\n            if (!requiredPermission) return true;\n            // Administradores têm acesso a tudo (exceto itens systemAdminOnly)\n            if (isAdmin()) return true;\n            // Verificar permissão específica\n            if (Array.isArray(requiredPermission)) {\n                // Se for um array de permissões, verificar se o usuário tem pelo menos uma delas\n                return requiredPermission.some({\n                    \"Sidebar.useCallback[hasPermissionForSubmenu]\": (perm)=>can(perm)\n                }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"]);\n            } else {\n                // Se for uma única permissão, verificar normalmente\n                return can(requiredPermission);\n            }\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"], [\n        can,\n        isAdmin,\n        user.role\n    ]);\n    // Função para verificar se um submenu deve ser exibido baseado nas preferências\n    const shouldShowSubmenuByPreferences = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[shouldShowSubmenuByPreferences]\": (moduleId, submenuId)=>{\n            // Apenas verificar preferências para o módulo de agendamento\n            if (moduleId !== 'scheduler') return true;\n            // Se as preferências ainda estão carregando, não mostrar os itens que podem ser escondidos\n            if (preferencesLoading) {\n                return false;\n            }\n            // Verificar se as preferências estão carregadas\n            if (!schedulingPreferences) {\n                return false;\n            }\n            switch(submenuId){\n                case 'locations':\n                    const showLocations = schedulingPreferences.showLocations !== false;\n                    return showLocations;\n                case 'service-types':\n                    const showServiceTypes = schedulingPreferences.showServiceTypes !== false;\n                    return showServiceTypes;\n                case 'insurances':\n                    const showInsurance = schedulingPreferences.showInsurance !== false;\n                    return showInsurance;\n                case 'working-hours':\n                    const showWorkingHours = schedulingPreferences.showWorkingHours !== false;\n                    return showWorkingHours;\n                default:\n                    return true;\n            }\n        }\n    }[\"Sidebar.useCallback[shouldShowSubmenuByPreferences]\"], [\n        schedulingPreferences,\n        preferencesLoading\n    ]);\n    // Função para alternar o colapso da sidebar\n    const toggleCollapse = ()=>{\n        const newCollapsedState = !isCollapsed;\n        setIsCollapsed(newCollapsedState);\n        if (true) {\n            localStorage.setItem('sidebarCollapsed', newCollapsedState.toString());\n        }\n    };\n    // Função para alternar a expansão de um grupo\n    const toggleGroup = (groupId)=>{\n        setExpandedGroups((prev)=>({\n                ...prev,\n                [groupId]: !prev[groupId]\n            }));\n    };\n    // Verificar se algum item dentro de um grupo está ativo\n    const isGroupActive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[isGroupActive]\": (moduleId, groupItems)=>{\n            return groupItems.some({\n                \"Sidebar.useCallback[isGroupActive]\": (item)=>isSubmenuActive(moduleId, item.id)\n            }[\"Sidebar.useCallback[isGroupActive]\"]);\n        }\n    }[\"Sidebar.useCallback[isGroupActive]\"], [\n        isSubmenuActive\n    ]);\n    // Expandir automaticamente grupos que contêm o item ativo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            if (activeModule && _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) {\n                const newExpandedGroups = {\n                    ...expandedGroups\n                };\n                _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule].forEach({\n                    \"Sidebar.useEffect\": (submenu)=>{\n                        if (submenu.type === 'group' && isGroupActive(activeModule, submenu.items)) {\n                            newExpandedGroups[submenu.id] = true;\n                        }\n                    }\n                }[\"Sidebar.useEffect\"]);\n                setExpandedGroups(newExpandedGroups);\n            }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"Sidebar.useEffect\"], [\n        activeModule,\n        pathname,\n        isGroupActive\n    ]);\n    // Verificar se um item de submenu tem permissão para ser exibido\n    const hasPermissionForSubmenuItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (moduleId, submenuId)=>{\n            var _moduleSubmenus_moduleId;\n            // Buscar o item dentro dos grupos do módulo\n            let submenuItem = null;\n            // Primeiro buscar nos itens diretos\n            submenuItem = (_moduleSubmenus_moduleId = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId]) === null || _moduleSubmenus_moduleId === void 0 ? void 0 : _moduleSubmenus_moduleId.find({\n                \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (item)=>item.id === submenuId\n            }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"]);\n            // Se não encontrou, buscar dentro dos grupos\n            if (!submenuItem) {\n                for (const item of _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[moduleId] || []){\n                    if (item.type === 'group' && item.items) {\n                        submenuItem = item.items.find({\n                            \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (subItem)=>subItem.id === submenuId\n                        }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"]);\n                        if (submenuItem) break;\n                    }\n                }\n            }\n            // Verificar se o item requer SYSTEM_ADMIN\n            if (submenuItem === null || submenuItem === void 0 ? void 0 : submenuItem.systemAdminOnly) {\n                return (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n            }\n            return hasPermissionForSubmenu(moduleId, submenuId);\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"], [\n        hasPermissionForSubmenu,\n        user === null || user === void 0 ? void 0 : user.role\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"\".concat(isCollapsed ? 'w-16' : 'w-72', \" bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col \").concat(isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'),\n        \"aria-label\": \"Navega\\xe7\\xe3o lateral\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"flex-1 p-5\",\n                moduleColor: activeModule,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toggleCollapse,\n                            className: \"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200\",\n                            \"aria-label\": isCollapsed ? 'Expandir sidebar' : 'Minimizar sidebar',\n                            title: isCollapsed ? 'Expandir sidebar' : 'Minimizar sidebar',\n                            children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 313,\n                                columnNumber: 28\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 313,\n                                columnNumber: 57\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, undefined),\n                    activeModuleObject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModuleTitle, {\n                        moduleId: activeModule,\n                        title: activeModuleTitle,\n                        icon: ModuleIcon,\n                        isCollapsed: isCollapsed\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, undefined),\n                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_CompanySelector__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        activeModule: activeModule\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 328,\n                        columnNumber: 26\n                    }, undefined),\n                    preferencesLoading && activeModule === 'scheduler' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, undefined),\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-sm text-gray-500 dark:text-gray-400\",\n                                children: \"Carregando...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 334,\n                                columnNumber: 30\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2 flex flex-col justify-center items-center mt-8 \".concat(isCollapsed ? 'px-1' : ''),\n                        \"aria-labelledby\": \"sidebar-heading\",\n                        children: !(preferencesLoading && activeModule === 'scheduler') && activeModule && ((_moduleSubmenus_activeModule = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) === null || _moduleSubmenus_activeModule === void 0 ? void 0 : _moduleSubmenus_activeModule.map((submenu)=>{\n                            // Verificar se é um grupo ou um item individual\n                            if (submenu.type === 'group') {\n                                // Verificar se algum item do grupo tem permissão para ser exibido\n                                const hasAnyPermission = submenu.items.some((item)=>hasPermissionForSubmenuItem(activeModule, item.id));\n                                if (!hasAnyPermission) {\n                                    return null; // Não renderizar o grupo se nenhum item tiver permissão\n                                }\n                                const isGroupExpanded = expandedGroups[submenu.id] || false;\n                                const isAnyItemActive = isGroupActive(activeModule, submenu.items);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleGroup(submenu.id),\n                                            className: \"\\n                        group w-full flex items-center justify-between px-4 py-2.5 rounded-lg transition-all duration-300\\n                        \".concat(isAnyItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark font-medium\") : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700', \"\\n                      \"),\n                                            \"aria-expanded\": isGroupExpanded,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-left\",\n                                                    children: submenu.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-500 dark:text-gray-400\",\n                                                    children: isGroupExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 27\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 363,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        (isGroupExpanded || isCollapsed) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat(isCollapsed ? '' : 'ml-4 pl-2 border-l border-gray-200 dark:border-gray-700', \" space-y-1\"),\n                                            children: submenu.items.map((item)=>{\n                                                const isItemActive = isSubmenuActive(activeModule, item.id);\n                                                // Verificar permissão antes de renderizar o item\n                                                if (!hasPermissionForSubmenuItem(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não tiver permissão\n                                                }\n                                                // Verificar se o item deve ser exibido baseado nas preferências\n                                                if (!shouldShowSubmenuByPreferences(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não estiver habilitado nas preferências\n                                                }\n                                                // Verificar se o item está em construção\n                                                const itemKey = \"\".concat(activeModule, \".\").concat(item.id);\n                                                const isItemUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.isUnderConstruction)(activeModule, item.id);\n                                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.getConstructionMessage)(activeModule, item.id);\n                                                // Estilo comum para os itens\n                                                const itemClassName = \"\\n                          group w-full flex items-center \".concat(isCollapsed ? 'justify-center px-2 py-2' : 'gap-3 px-3 py-2', \" rounded-lg transition-all duration-300\\n                          \").concat(isItemActive ? \"rounded-xl border border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                               bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                               shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n                        \");\n                                                // Se estiver em construção, usar o ConstructionButton\n                                                if (isItemUnderConstruction) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_9__.ConstructionButton, {\n                                                        className: itemClassName,\n                                                        \"aria-current\": isItemActive ? 'page' : undefined,\n                                                        title: constructionMessage.title,\n                                                        content: constructionMessage.content,\n                                                        icon: constructionMessage.icon,\n                                                        position: \"right\",\n                                                        children: [\n                                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\\n                                  \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                                \"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                    size: 18,\n                                                                    \"aria-hidden\": \"true\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 35\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 33\n                                                            }, undefined),\n                                                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                                children: item.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 48\n                                                            }, undefined)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 29\n                                                    }, undefined);\n                                                }\n                                                // Se não estiver em construção, usar o botão normal\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleModuleSubmenuClick(activeModule, item.id),\n                                                    className: itemClassName,\n                                                    \"aria-current\": isItemActive ? 'page' : undefined,\n                                                    children: [\n                                                        item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"\\n                                \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                              \"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                size: 18,\n                                                                \"aria-hidden\": \"true\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 31\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 27\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 387,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 360,\n                                    columnNumber: 17\n                                }, undefined);\n                            } else {\n                                // Renderização de itens individuais (não agrupados)\n                                const isActive = isSubmenuActive(activeModule, submenu.id);\n                                // Verificar permissão antes de renderizar o item\n                                const hasPermission = hasPermissionForSubmenu(activeModule, submenu.id);\n                                if (!hasPermission) {\n                                    return null; // Não renderizar se não tiver permissão\n                                }\n                                // Verificar se o submenu deve ser exibido baseado nas preferências\n                                const shouldShowByPreferences = shouldShowSubmenuByPreferences(activeModule, submenu.id);\n                                if (!shouldShowByPreferences) {\n                                    return null; // Não renderizar se não estiver habilitado nas preferências\n                                }\n                                // Verificar se o submenu está em construção\n                                const submenuKey = \"\".concat(activeModule, \".\").concat(submenu.id);\n                                const isSubmenuUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.isUnderConstruction)(activeModule, submenu.id);\n                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_10__.getConstructionMessage)(activeModule, submenu.id);\n                                // Estilo comum para ambos os tipos de botões\n                                const buttonClassName = \"\\n                group w-full flex items-center \".concat(isCollapsed ? 'justify-center px-2 py-3' : 'gap-3 px-4 py-3', \" rounded-lg transition-all duration-300\\n                \").concat(isActive ? \"rounded-xl border border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                     bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                     shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n              \");\n                                // Se estiver em construção, usar o ConstructionButton\n                                if (isSubmenuUnderConstruction) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_9__.ConstructionButton, {\n                                        className: buttonClassName,\n                                        \"aria-current\": isActive ? 'page' : undefined,\n                                        title: constructionMessage.title,\n                                        content: constructionMessage.content,\n                                        icon: constructionMessage.icon,\n                                        position: \"right\",\n                                        children: [\n                                            submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                    size: 20,\n                                                    \"aria-hidden\": \"true\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 523,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                                children: submenu.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 535,\n                                                columnNumber: 38\n                                            }, undefined)\n                                        ]\n                                    }, submenu.id, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                        lineNumber: 513,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }\n                                // Se não estiver em construção, usar o botão normal\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleModuleSubmenuClick(activeModule, submenu.id),\n                                    className: buttonClassName,\n                                    \"aria-current\": isActive ? 'page' : undefined,\n                                    children: [\n                                        submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                size: 20,\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 555,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 549,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                            children: submenu.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 561,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 542,\n                                    columnNumber: 17\n                                }, undefined);\n                            }\n                        }))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-5 border-t border-gray-100 dark:border-gray-700 mt-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToModules,\n                    className: \"\\n            group w-full py-3 px-4 rounded-lg flex items-center gap-3\\n            border-2 border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n            bg-transparent dark:bg-gray-800 hover:bg-module-\").concat(activeModule, \"-bg/10 dark:hover:bg-module-\").concat(activeModule, \"-bg-dark/10\\n            transition-all duration-300\\n          \"),\n                    \"aria-label\": \"Voltar para o dashboard principal\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            flex items-center justify-center w-8 h-8 rounded-full\\n            bg-module-\".concat(activeModule, \"-bg dark:bg-module-\").concat(activeModule, \"-bg-dark/70\\n            text-module-\").concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\\n            shadow-sm dark:shadow-md dark:shadow-black/20\\n            group-hover:scale-110 transition-transform duration-200\\n          \"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Menu_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 588,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 581,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-gray-800 dark:text-gray-200\",\n                            children: \"Voltar a Tela Inicial\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 590,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 571,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 570,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 298,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Sidebar, \"VAGN2v+pOWDhD1NheeJLNqum7jI=\", false, function() {\n    return [\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _hooks_useSchedulingPreferences__WEBPACK_IMPORTED_MODULE_6__.useSchedulingPreferences\n    ];\n});\n_c1 = Sidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\nvar _c, _c1;\n$RefreshReg$(_c, \"ModuleTitle\");\n$RefreshReg$(_c1, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar/index.js\n"));

/***/ })

});