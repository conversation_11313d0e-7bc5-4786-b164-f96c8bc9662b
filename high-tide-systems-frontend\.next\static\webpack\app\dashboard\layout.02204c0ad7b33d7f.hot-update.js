"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/app/dashboard/ClientHeader.js":
/*!*******************************************!*\
  !*** ./src/app/dashboard/ClientHeader.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,PanelLeftClose,PanelLeftOpen,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,PanelLeftClose,PanelLeftOpen,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,PanelLeftClose,PanelLeftOpen,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panel-left-open.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,PanelLeftClose,PanelLeftOpen,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panel-left-close.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,PanelLeftClose,PanelLeftOpen,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,PanelLeftClose,PanelLeftOpen,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,PanelLeftClose,PanelLeftOpen,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,PanelLeftClose,PanelLeftOpen,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,PanelLeftClose,PanelLeftOpen,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,PanelLeftClose,PanelLeftOpen,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/QuickNavContext */ \"(app-pages-browser)/./src/contexts/QuickNavContext.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(app-pages-browser)/./src/components/ThemeToggle.js\");\n/* harmony import */ var _components_chat_ChatButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/chat/ChatButton */ \"(app-pages-browser)/./src/components/chat/ChatButton.js\");\n/* harmony import */ var _components_notifications_NotificationButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/notifications/NotificationButton */ \"(app-pages-browser)/./src/components/notifications/NotificationButton.js\");\n/* harmony import */ var _components_bug_report_BugReportButton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/bug-report/BugReportButton */ \"(app-pages-browser)/./src/components/bug-report/BugReportButton.js\");\n/* harmony import */ var _config_appConfig__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/config/appConfig */ \"(app-pages-browser)/./src/config/appConfig.js\");\n/* harmony import */ var _app_modules_people_services_personsService__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/modules/people/services/personsService */ \"(app-pages-browser)/./src/app/modules/people/services/personsService.js\");\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./components */ \"(app-pages-browser)/./src/app/dashboard/components.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Header Component adaptado para clientes\nconst ClientHeader = (param)=>{\n    let { toggleSidebar, isSidebarOpen, isMinimized, toggleMinimized } = param;\n    _s();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { openQuickNav } = (0,_contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__.useQuickNav)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    // Função para obter o módulo ativo a partir da URL\n    const getActiveModule = ()=>{\n        if (!pathname) return null;\n        const path = pathname.split('/');\n        if (path.length >= 3) {\n            return _components__WEBPACK_IMPORTED_MODULE_11__.modules.find((m)=>m.id === path[2]);\n        }\n        return null;\n    };\n    const activeModule = getActiveModule();\n    // Pegar primeira letra de cada nome para o avatar\n    const getInitials = ()=>{\n        var _user_login;\n        // Verificar se temos uma pessoa associada ao cliente\n        if ((user === null || user === void 0 ? void 0 : user.persons) && user.persons.length > 0 && user.persons[0].fullName) {\n            const fullName = user.persons[0].fullName;\n            const names = fullName.split(' ');\n            if (names.length === 1) return names[0].charAt(0);\n            return \"\".concat(names[0].charAt(0)).concat(names[names.length - 1].charAt(0));\n        }\n        // Fallback para o login do cliente\n        return (user === null || user === void 0 ? void 0 : (_user_login = user.login) === null || _user_login === void 0 ? void 0 : _user_login.charAt(0)) || 'C';\n    };\n    // Obter o nome completo da pessoa associada ao cliente ou o login do cliente\n    const getDisplayName = ()=>{\n        if ((user === null || user === void 0 ? void 0 : user.persons) && user.persons.length > 0 && user.persons[0].fullName) {\n            return user.persons[0].fullName;\n        }\n        return (user === null || user === void 0 ? void 0 : user.login) || 'Cliente';\n    };\n    // Obter a URL da imagem de perfil da pessoa associada ao cliente\n    const getProfileImage = ()=>{\n        if ((user === null || user === void 0 ? void 0 : user.persons) && user.persons.length > 0) {\n            // Primeiro tenta usar a URL completa se disponível\n            if (user.persons[0].profileImageFullUrl) {\n                return user.persons[0].profileImageFullUrl;\n            } else if (user.persons[0].profileImageUrl) {\n                return _app_modules_people_services_personsService__WEBPACK_IMPORTED_MODULE_10__.personsService.getProfileImageUrl(user.persons[0].id, user.persons[0].profileImageUrl);\n            }\n        }\n        return null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white dark:bg-gray-800 border-b border-gray-300 dark:border-gray-700 px-8 py-3 flex justify-between items-center sticky top-0 z-[9000]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleSidebar,\n                        className: \"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg lg:hidden text-gray-600 dark:text-gray-300 transition-colors\",\n                        \"aria-label\": isSidebarOpen ? \"Fechar menu lateral\" : \"Abrir menu lateral\",\n                        children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            size: 22,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 83,\n                            columnNumber: 28\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            size: 22,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 83,\n                            columnNumber: 65\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: pathname !== '/dashboard' && activeModule ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleMinimized,\n                                    className: \"p-2 rounded-lg transition-all duration-300 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100\",\n                                    \"aria-label\": isMinimized ? \"Expandir sidebar\" : \"Minimizar sidebar\",\n                                    title: isMinimized ? \"Expandir sidebar\" : \"Minimizar sidebar\",\n                                    children: isMinimized ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: 20,\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 97,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        size: 20,\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 99,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: activeModule.id === 'admin' ? 'w-2 h-10 rounded-full bg-slate-600 dark:bg-slate-400 block mr-2' : \"w-2 h-10 rounded-full bg-module-\".concat(activeModule.id, \"-bg dark:bg-module-\").concat(activeModule.id, \"-bg-dark block mr-2\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, undefined),\n                                activeModule.icon && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(activeModule.icon, {\n                                    size: 28,\n                                    className: \"text-module-\".concat(activeModule.id, \"-icon dark:text-module-\").concat(activeModule.id, \"-icon-dark\")\n                                }),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs uppercase tracking-wider font-semibold text-gray-900 dark:text-white\",\n                                            children: \"M\\xf3dulo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-gray-900 dark:text-white\",\n                                            children: activeModule.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/logo_horizontal_sem_fundo.png\",\n                                    alt: \"High Tide Logo\",\n                                    className: \"h-10 mr-2.5 dark:invert dark:text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute -bottom-1 right-3 text-xs text-gray-500 dark:text-gray-400 font-mono\",\n                                    children: _config_appConfig__WEBPACK_IMPORTED_MODULE_9__.APP_VERSION\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                    lineNumber: 122,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden sm:flex items-center gap-2 px-3 py-1.5 bg-purple-50 dark:bg-purple-900/30 border border-purple-200 dark:border-purple-700 rounded-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                size: 14,\n                                className: \"text-purple-600 dark:text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-purple-700 dark:text-purple-300\",\n                                children: \"\\xc1rea do Cliente\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: openQuickNav,\n                        className: \"flex items-center gap-2 py-2 px-4 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 focus:ring-primary-500 focus:border-primary-500 dark:text-gray-200 outline-none transition-colors\",\n                        \"aria-label\": \"Abrir pesquisa r\\xe1pida\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                size: 18,\n                                className: \"text-gray-400 dark:text-gray-500\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden sm:inline\",\n                                children: \"Pesquisar...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden sm:flex items-center gap-1 ml-2 px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs text-gray-500 dark:text-gray-400\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Ctrl + K\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/dashboard/scheduler/calendar'),\n                        className: \"p-2 text-gray-600 dark:text-gray-300 hover:bg-purple-100 dark:hover:bg-purple-900/30 hover:text-purple-600 dark:hover:text-purple-400 rounded-full transition-all duration-200 hover:scale-105 hover:shadow-md\",\n                        \"aria-label\": \"Calend\\xe1rio\",\n                        title: \"Ver calend\\xe1rio\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            size: 20,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/dashboard/scheduler/appointments-report'),\n                        className: \"p-2 text-gray-600 dark:text-gray-300 hover:bg-purple-100 dark:hover:bg-purple-900/30 hover:text-purple-600 dark:hover:text-purple-400 rounded-full transition-all duration-200 hover:scale-105 hover:shadow-md\",\n                        \"aria-label\": \"Meus Agendamentos\",\n                        title: \"Ver meus agendamentos\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            size: 20,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatButton__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notifications_NotificationButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_bug_report_BugReportButton__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__.ThemeToggle, {}, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 border-l border-gray-200 dark:border-gray-700 mx-1\",\n                        \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center gap-2 py-1 px-1 rounded-full hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                \"aria-expanded\": \"false\",\n                                \"aria-haspopup\": \"true\",\n                                \"aria-label\": \"Menu do usu\\xe1rio\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-9 w-9 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center font-medium text-purple-600 dark:text-purple-400 overflow-hidden\",\n                                        children: getProfileImage() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: getProfileImage(),\n                                            alt: \"Foto de perfil de \".concat(getDisplayName()),\n                                            className: \"h-10 w-10 rounded-full object-cover\",\n                                            onError: (e)=>{\n                                                e.target.onerror = null;\n                                                e.target.style.display = 'none';\n                                                e.target.parentNode.innerHTML = getInitials();\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, undefined) : getInitials()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:block text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-800 dark:text-gray-200 line-clamp-1\",\n                                                children: getDisplayName()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 208,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-purple-700 dark:text-purple-300 px-2 py-0.5 rounded-full inline-flex items-center mt-0.5 bg-purple-50 dark:bg-purple-900/30\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        size: 10,\n                                                        className: \"mr-1\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Cliente\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 209,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-gray-400 dark:text-gray-500 hidden md:block\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-md border border-gray-200 dark:border-gray-700 py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-150 origin-top-right\",\n                                role: \"menu\",\n                                \"aria-orientation\": \"vertical\",\n                                \"aria-labelledby\": \"user-menu-button\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-2 border-b border-gray-100 dark:border-gray-700 md:hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-800 dark:text-gray-200\",\n                                                children: getDisplayName()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 224,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                                                children: (user === null || user === void 0 ? void 0 : user.email) || '<EMAIL>'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 225,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-1 mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/dashboard/profile'),\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                                role: \"menuitem\",\n                                                children: \"Meu Perfil\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 234,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/dashboard/people/persons'),\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                                role: \"menuitem\",\n                                                children: \"Minhas Pessoas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/dashboard/scheduler/appointments-report'),\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                                role: \"menuitem\",\n                                                children: \"Meus Agendamentos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 248,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: logout,\n                                                className: \"w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 flex items-center transition-colors\",\n                                                role: \"menuitem\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_PanelLeftClose_PanelLeftOpen_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"mr-2\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Sair do Sistema\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 233,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClientHeader, \"Nu+1f0C/8eoWzJzvxrSWs8iK+Vc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__.useQuickNav,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname\n    ];\n});\n_c = ClientHeader;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClientHeader);\nvar _c;\n$RefreshReg$(_c, \"ClientHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/ClientHeader.js\n"));

/***/ })

});