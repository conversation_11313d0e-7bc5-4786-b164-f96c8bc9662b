import { useState, useEffect } from 'react';
import { preferencesService } from '@/services/preferencesService';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/contexts/ToastContext';
import { useCompanySelection } from '@/contexts/CompanySelectionContext';

/**
 * Hook para gerenciar preferências por empresa
 * Para SYSTEM_ADMIN: permite selecionar qualquer empresa
 * Para outros roles: usa a empresa do usuário atual
 */
export function useCompanyPreferences() {
  const { user, isSystemAdmin } = useAuth();
  const { toast_error, toast_success } = useToast();
  const { 
    selectedCompanyId, 
    companies, 
    isLoading: isLoadingCompanies,
    selectedCompany,
    getEffectiveCompanyId,
    canSelectCompany
  } = useCompanySelection();
  
  const [preferences, setPreferences] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Carregar preferências da empresa selecionada
  useEffect(() => {
    async function loadPreferences() {
      const companyId = getEffectiveCompanyId();
      
      try {
        setIsLoading(true);
        let data;
        
        if (isSystemAdmin() && companyId) {
          // SYSTEM_ADMIN pode acessar preferências de qualquer empresa
          data = await preferencesService.getForCompany(companyId);
        } else if (!isSystemAdmin()) {
          // Outros roles usam suas próprias preferências
          data = await preferencesService.get();
        } else {
          // SYSTEM_ADMIN operando sem empresa - usar preferências padrão
          data = getDefaultPreferences();
        }
        
        setPreferences(data);
      } catch (error) {
        console.error('Erro ao carregar preferências:', error);
        // Definir preferências padrão em caso de erro, incluindo notificações
        setPreferences({
          ...getDefaultPreferences(),
          notifications: {
            enabled: {
              NEW_REGISTRATION: false,
              APPOINTMENT_COMING: false,
              NEW_ACCESS: false,
              NEW_BACKUP: false,
              NEW_EXPORT: false,
              SYSTEM_ALERT: false,
              DOCUMENT_SHARED: true
            },
            required: {
              NEW_REGISTRATION: false,
              APPOINTMENT_COMING: false,
              NEW_ACCESS: false,
              NEW_BACKUP: false,
              NEW_EXPORT: false,
              SYSTEM_ALERT: false,
              DOCUMENT_SHARED: false
            }
          }
        });
      } finally {
        setIsLoading(false);
      }
    }

    // Evitar recarregamento desnecessário após salvar
    if (preferences === null) {
      loadPreferences();
    }
  }, [selectedCompanyId, isSystemAdmin, getEffectiveCompanyId]);

  // Salvar preferências da empresa selecionada
  const savePreferences = async (newPreferences) => {
    const companyId = getEffectiveCompanyId();
    
    if (isSystemAdmin() && !companyId) {
      toast_error('Selecione uma empresa para salvar as preferências ou opere no modo global');
      return false;
    }

    try {
      let data;
      
      if (isSystemAdmin() && companyId) {
        // SYSTEM_ADMIN pode salvar preferências de qualquer empresa
        data = await preferencesService.saveForCompany(companyId, newPreferences);
      } else if (!isSystemAdmin()) {
        // Outros roles salvam suas próprias preferências
        data = await preferencesService.save(newPreferences);
      } else {
        // SYSTEM_ADMIN operando sem empresa - não permitir salvamento
        toast_error('Selecione uma empresa para salvar as preferências');
        return false;
      }
      
      // Atualizar preferências localmente sem recarregar
      setPreferences(prevPreferences => ({
        ...prevPreferences,
        ...data
      }));
      
      // Usar setTimeout para evitar conflitos com outros processos
      setTimeout(() => {
        toast_success('Preferências salvas com sucesso!');
      }, 100);
      
      return true;
    } catch (error) {
      console.error('Erro ao salvar preferências:', error);
      toast_error('Erro ao salvar preferências da empresa');
      return false;
    }
  };

  // Preferências padrão - sincronizadas com backend
  const getDefaultPreferences = () => ({
    scheduling: {
      allowPastScheduling: false,
      startHour: "08:00",
      endHour: "18:00",
      requiredFields: {
        insurance: true,
        specialty: true,
        serviceType: true,
        location: true,
        professional: true,
        notes: false,
        title: false,
        description: false,
      },
      allowMultipleServices: false,
      requireClientConfirmation: false,
      selectedWeekDays: [1, 2, 3, 4, 5, 6],
      requireRecurrence: true,
      requireSequential: true,
      allowedHours: Array.from({ length: 24 }, (_, i) => i >= 8 && i <= 18),
      requireServiceValue: false,
      requireLocationAddress: false,
      requireLocationPhone: false,
      showWorkingHours: true,
      showServiceTypes: true,
      showLocations: true,
      showInsurance: true,
      showInsuranceLimit: true,
    },
    locations: {
      useAvailability: false,
      selectedWeekDays: [1, 2, 3, 4, 5]
    },
    user: {
      userCpfCnpj: true,
      userUnit: false,
      userBirthDate: true,
      userPhone: true,
      userCep: true,
      userRole: true,
      userProfilePhoto: false,
      require2FA: false
    },
    client: {
      clientCpfCnpj: true,
      clientCep: true,
      clientPhone: true
    },
    patient: {
      patientCpfCnpj: true,
      patientBirthDate: true,
      patientGender: false,
      patientEmail: false,
      patientPhone: false,
      patientCep: false,
      patientAssociateClient: true,
      patientObservation: false
    },
    userPrivacy: {
      hideUserCpf: false,
      hideUserCnpj: false,
      hideUserEmail: false,
      hideUserPhone: false,
      hideUserAddress: false,
      hideUserBirthDate: false,
      hideUserLastLoginIp: false
    },
    clientPrivacy: {
      hideClientEmail: false,
      hideClientFullName: false
    },
    patientPrivacy: {
      hidePatientCpf: false,
      hidePatientEmail: false,
      hidePatientPhone: false,
      hidePatientAddress: false,
      hidePatientBirthDate: false,
      hidePatientNotes: false,
      hidePatientProfileImage: false
    }
  });

  return {
    // Estados
    selectedCompanyId,
    companies,
    preferences,
    isLoading,
    isLoadingCompanies,
    selectedCompany,
    
    // Funções
    savePreferences,
    getEffectiveCompanyId,
    
    // Flags
    canSelectCompany,
    hasMultipleCompanies: companies.length > 1
  };
}