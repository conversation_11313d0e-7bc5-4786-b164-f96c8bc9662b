"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar/ClientSidebar.js":
/*!***********************************************************!*\
  !*** ./src/components/dashboard/Sidebar/ClientSidebar.js ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronLeft,Clock,FileText,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronLeft,Clock,FileText,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronLeft,Clock,FileText,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronLeft,Clock,FileText,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronLeft,Clock,FileText,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronLeft,Clock,FileText,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/CustomScrollArea */ \"(app-pages-browser)/./src/components/ui/CustomScrollArea.js\");\n// src/components/dashboard/Sidebar/ClientSidebar.js\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Client-specific submenu configuration\nconst clientModuleSubmenus = {\n    people: [\n        {\n            id: 'persons',\n            title: 'Pessoas',\n            icon: _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            description: 'Gerenciar pessoas relacionadas'\n        }\n    ],\n    scheduler: [\n        {\n            id: 'calendar',\n            title: 'Calendário',\n            icon: _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            description: 'Visualizar agenda'\n        },\n        {\n            id: 'appointments',\n            title: 'Meus Agendamentos',\n            icon: _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: 'Visualizar meus agendamentos'\n        },\n        {\n            id: 'requests',\n            title: 'Minhas Solicitações',\n            icon: _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: 'Acompanhar solicitações de agendamento'\n        }\n    ],\n    profile: [\n        {\n            id: 'profile',\n            title: 'Meu Perfil',\n            icon: _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: 'Gerenciar meu perfil e dados pessoais'\n        }\n    ]\n};\nconst ModuleTitle = (param)=>{\n    let { moduleId, title, icon: Icon, onDashboardClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6 flex justify-center items-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: onDashboardClick,\n            style: {\n                background: 'none',\n                border: 'none',\n                padding: 0\n            },\n            className: \"focus:outline-none cursor-pointer\",\n            \"aria-label\": \"Ir para o dashboard\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/logo_horizontal_sem_fundo.png\",\n                    alt: \"High Tide Logo\",\n                    className: \"h-12 dark:invert dark:text-white transition-all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                    lineNumber: 33,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ModuleTitle;\nconst ClientSidebar = (param)=>{\n    let { activeModule, activeModuleTitle, isSubmenuActive, handleModuleSubmenuClick, handleBackToModules, isSidebarOpen, isMinimized, toggleMinimized } = param;\n    var _clientModuleSubmenus_activeModule;\n    // Encontrar o objeto do módulo ativo para acessar seu ícone\n    const ModuleIcon = activeModule === 'people' ? _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"] : activeModule === 'scheduler' ? _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"] : activeModule === 'profile' ? _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"] : _barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-72 bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col \".concat(isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'),\n        \"aria-label\": \"Navega\\xe7\\xe3o lateral\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"flex-1 p-5\",\n                moduleColor: activeModule,\n                children: [\n                    activeModule && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModuleTitle, {\n                        moduleId: activeModule,\n                        title: activeModuleTitle,\n                        icon: ModuleIcon,\n                        onDashboardClick: handleBackToModules\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2\",\n                        \"aria-labelledby\": \"sidebar-heading\",\n                        children: activeModule && ((_clientModuleSubmenus_activeModule = clientModuleSubmenus[activeModule]) === null || _clientModuleSubmenus_activeModule === void 0 ? void 0 : _clientModuleSubmenus_activeModule.map((submenu)=>{\n                            const isActive = isSubmenuActive(activeModule, submenu.id);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleModuleSubmenuClick(activeModule, submenu.id),\n                                className: \"\\n                  group w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-300\\n                  \".concat(isActive ? \"rounded-xl border border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                       bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                       shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-module-${activeModule}-hover dark:hover:bg-module-${activeModule}-hover-dark border border-transparent hover:border-module-${activeModule}-border dark:hover:border-module-${activeModule}-border-dark', \"\\n                \"),\n                                \"aria-current\": isActive ? 'page' : undefined,\n                                children: [\n                                    submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"\\n                    \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                  \"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                            size: 20,\n                                            \"aria-hidden\": \"true\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                                            lineNumber: 107,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                                        lineNumber: 101,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                        children: submenu.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, submenu.id, true, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, undefined);\n                        }))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-5 border-t border-gray-100 dark:border-gray-700 mt-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToModules,\n                    className: \"\\n            group w-full py-3 px-4 rounded-lg flex items-center gap-3\\n            border-2 border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n            bg-transparent dark:bg-gray-800 hover:bg-module-\").concat(activeModule, \"-bg/10 dark:hover:bg-module-\").concat(activeModule, \"-bg-dark/10\\n            transition-all duration-300\\n          \"),\n                    \"aria-label\": \"Voltar para o dashboard principal\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            flex items-center justify-center w-8 h-8 rounded-full\\n            bg-module-\".concat(activeModule, \"-bg dark:bg-module-\").concat(activeModule, \"-bg-dark/70\\n            text-module-\").concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\\n            shadow-sm dark:shadow-md dark:shadow-black/20\\n            group-hover:scale-110 transition-transform duration-200\\n          \"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronLeft_Clock_FileText_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-gray-800 dark:text-gray-200\",\n                            children: \"Voltar a Tela Inicial\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Program Files (x86)\\\\High Tide\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\ClientSidebar.js\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ClientSidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClientSidebar);\nvar _c, _c1;\n$RefreshReg$(_c, \"ModuleTitle\");\n$RefreshReg$(_c1, \"ClientSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar/ClientSidebar.js\n"));

/***/ })

});