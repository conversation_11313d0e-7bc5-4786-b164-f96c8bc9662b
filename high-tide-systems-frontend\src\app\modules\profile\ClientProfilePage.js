import React, { useState, useEffect, useRef, useMemo } from "react";
import { useRouter } from "next/navigation";
import "./profile.css";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/contexts/ToastContext";
import { clientsService } from "@/app/modules/people/services/clientsService";
import { personsService } from "@/app/modules/people/services/personsService";
import { useCep } from "@/hooks/useCep";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Building,
  Shield,
  Save,
  Loader2,
  Clock,
  UserCheck,
  Lock,
  CheckCircle,
  AlertCircle,
  ArrowLeft,
  LayoutDashboard,
  Users,
  CreditCard,
  Search
} from "lucide-react";
import {
  ModuleHeader,
  ModuleInput,
  ModuleFormGroup,
  ModalButton,
  ModuleTabs,
  ModuleMaskedInput
} from "@/components/ui";
import ClientProfileImageUpload from "@/components/forms/ClientProfileImageUpload";

const ClientProfilePage = () => {
  const router = useRouter();
  const { user, refreshUserData } = useAuth();
  const { toast_success, toast_error } = useToast();
  const profileImageUploadRef = useRef(null);

  // Estado para controlar o carregamento
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  // Hook para busca de CEP
  const { searchAddressByCep, isLoading: isCepLoading, error: cepError } = useCep();

  // Inicializar o estado do formulário com valores vazios
  const [formData, setFormData] = useState({
    login: "",
    email: "",
    person: {
      fullName: "",
      email: "",
      phone: "",
      cpf: "",
      birthDate: "",
      address: "",
      neighborhood: "",
      city: "",
      state: "",
      postalCode: ""
    }
  });

  // Carregar dados do cliente
  useEffect(() => {
    const loadClientData = async () => {
      if (!user?.id) return;

      setIsLoading(true);
      try {
        // Buscar dados do cliente
        const clientData = await clientsService.getClient(user.id);
        console.log("Dados do cliente carregados:", clientData);

        // Buscar dados da pessoa principal associada ao cliente
        let personData = null;
        if (user.persons && user.persons.length > 0) {
          personData = await personsService.getPerson(user.persons[0].id);
          console.log("Dados da pessoa carregados:", personData);

          // Verificar se temos a URL completa da imagem
          if (personData.profileImageUrl && !personData.profileImageFullUrl) {
            console.log("Gerando URL completa da imagem de perfil");
            // Usar o método do personsService para obter a URL completa
            personData.profileImageFullUrl = personsService.getProfileImageUrl(
              personData.id,
              personData.profileImageUrl
            );
            console.log("URL completa gerada:", personData.profileImageFullUrl);
          }
        }

        // Atualizar o estado do formulário
        setFormData({
          login: clientData.login || "",
          email: clientData.email || "",
          person: personData ? {
            fullName: personData.fullName || "",
            email: personData.email || "",
            phone: personData.phone ? personData.phone.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3") : "",
            cpf: personData.cpf ? personData.cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4") : "",
            birthDate: personData.birthDate ? new Date(personData.birthDate).toISOString().split('T')[0] : "",
            address: personData.address || "",
            neighborhood: personData.neighborhood || "",
            city: personData.city || "",
            state: personData.state || "",
            postalCode: personData.postalCode ? personData.postalCode.replace(/(\d{5})(\d{3})/, "$1-$2") : "",
            profileImageUrl: personData.profileImageUrl || "",
            profileImageFullUrl: personData.profileImageFullUrl || ""
          } : {
            fullName: "",
            email: "",
            phone: "",
            cpf: "",
            birthDate: "",
            address: "",
            neighborhood: "",
            city: "",
            state: "",
            postalCode: "",
            profileImageUrl: "",
            profileImageFullUrl: ""
          }
        });
      } catch (error) {
        console.error("Erro ao carregar dados do cliente:", error);
        toast_error({
          title: "Erro",
          message: "Não foi possível carregar seus dados. Tente novamente mais tarde."
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadClientData();
  }, [user, toast_error]);

  // Manipular mudanças no formulário
  const handleChange = (e) => {
    const { name, value } = e.target;

    setFormData(prev => {
      if (name.startsWith("person.")) {
        const personField = name.replace("person.", "");
        return {
          ...prev,
          person: {
            ...prev.person,
            [personField]: value
          }
        };
      } else {
        return {
          ...prev,
          [name]: value
        };
      }
    });
  };

  // Função para buscar endereço pelo CEP
  const handleCepSearch = async (cep) => {
    if (!cep || cep.replace(/\D/g, '').length !== 8) return;

    // Mapeamento para campos de pessoa
    const fieldMapping = {
      logradouro: 'person.address',
      bairro: 'person.neighborhood',
      localidade: 'person.city',
      uf: 'person.state'
    };

    await searchAddressByCep(cep, setFormData, fieldMapping);
  };

  // Manipular envio do formulário
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      // Preparar dados para atualização do cliente
      const clientPayload = {
        email: formData.email
      };

      // Preparar dados para atualização da pessoa
      const personPayload = {
        fullName: formData.person.fullName,
        email: formData.person.email,
        phone: formData.person.phone ? formData.person.phone.replace(/\D/g, "") : null,
        cpf: formData.person.cpf ? formData.person.cpf.replace(/\D/g, "") : null,
        birthDate: formData.person.birthDate || null,
        address: formData.person.address || null,
        neighborhood: formData.person.neighborhood || null,
        city: formData.person.city || null,
        state: formData.person.state || null,
        postalCode: formData.person.postalCode ? formData.person.postalCode.replace(/\D/g, "") : null
      };

      // Atualizar cliente
      await clientsService.updateClient(user.id, clientPayload);

      // Atualizar pessoa associada ao cliente
      if (user.persons && user.persons.length > 0) {
        console.log('Person payload enviado:', personPayload);
        await personsService.updatePerson(user.persons[0].id, personPayload);
      }

      // Atualizar dados do usuário no contexto
      await refreshUserData();
      
      // Recarregar dados após salvar
      window.location.reload();

      toast_success({
        title: "Sucesso",
        message: "Seus dados foram atualizados com sucesso!"
      });
    } catch (error) {
      console.error("Erro ao atualizar perfil:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível atualizar seus dados. Tente novamente mais tarde."
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Manipular upload de imagem de perfil
  const handleProfileImageUpload = async (file) => {
    if (!file || !user?.persons || user.persons.length === 0) return;

    setIsUploadingImage(true);
    try {
      console.log("Iniciando upload de imagem para o cliente", user.persons[0].id);
      console.log("Arquivo selecionado:", file.name, file.type, file.size);

      // Upload da imagem para a pessoa associada ao cliente
      // Passamos o arquivo diretamente, não o formData
      await personsService.uploadProfileImage(user.persons[0].id, file);

      // Atualizar dados do usuário no contexto
      await refreshUserData();

      toast_success({
        title: "Sucesso",
        message: "Sua foto de perfil foi atualizada com sucesso!"
      });
    } catch (error) {
      console.error("Erro ao fazer upload da imagem:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível atualizar sua foto de perfil. Tente novamente mais tarde."
      });
    } finally {
      setIsUploadingImage(false);
    }
  };

  // Obter a pessoa principal associada ao cliente
  const mainPerson = useMemo(() => {
    const person = user?.persons && user.persons.length > 0 ? user.persons[0] : null;
    if (person) {
      console.log('Dados da pessoa principal:', person);
      console.log('URL da imagem de perfil:', person.profileImageUrl);
      console.log('URL completa da imagem de perfil:', person.profileImageFullUrl);
    }
    return person;
  }, [user]);

  // Renderizar o componente
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <button
          onClick={() => router.push('/dashboard')}
          className="flex items-center gap-2 px-4 py-2 bg-slate-100 hover:bg-slate-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-200 rounded-lg transition-colors shadow-sm"
        >
          <LayoutDashboard size={18} />
          <span>Voltar ao Dashboard</span>
        </button>
      </div>

      {/* Banner de perfil com gradiente */}
      <div className="relative rounded-xl overflow-hidden h-48 md:h-56 bg-gradient-to-r from-purple-500 to-indigo-600 bg-pattern shadow-xl">
        {/* Overlay para melhorar a legibilidade */}
        <div className="absolute inset-0 bg-black/10"></div>

        <div className="relative z-10 p-8 flex flex-col md:flex-row items-center md:items-start gap-6">
          {/* Foto de perfil com borda */}
          <div className="flex-shrink-0 rounded-full p-1 bg-white/20 backdrop-blur-sm shadow-xl relative group">
            <div className="h-32 w-32 rounded-full overflow-hidden border-4 border-white/30 shadow-inner">
              {mainPerson?.profileImageUrl ? (
                <img
                  src={mainPerson.profileImageFullUrl || personsService.getProfileImageUrl(mainPerson.id, mainPerson.profileImageUrl)}
                  alt={`Foto de perfil de ${mainPerson.fullName || 'Cliente'}`}
                  className="h-full w-full object-cover"
                  onError={(e) => {
                    console.error("Erro ao carregar imagem:", e.target.src);
                    e.target.onerror = null;
                    e.target.style.display = 'none';
                    e.target.parentNode.innerHTML = `<div class="flex items-center justify-center w-full h-full bg-slate-300 dark:bg-slate-700"><User size={40} className="text-slate-600 dark:text-slate-400" /></div>`;
                  }}
                />
              ) : (
                <div className="flex items-center justify-center w-full h-full bg-slate-300 dark:bg-slate-700">
                  <User size={40} className="text-slate-600 dark:text-slate-400" />
                </div>
              )}
            </div>

            {/* Overlay para upload de imagem */}
            <button
              type="button"
              onClick={() => profileImageUploadRef.current?.click()}
              className="absolute inset-0 flex items-center justify-center bg-black/40 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              title="Alterar foto de perfil"
              disabled={isUploadingImage}
            >
              {isUploadingImage ? (
                <Loader2 size={24} className="text-white animate-spin" />
              ) : (
                <div className="flex flex-col items-center">
                  <UserCheck size={24} className="text-white mb-1" />
                  <span className="text-white text-xs font-medium">Alterar foto</span>
                </div>
              )}
            </button>
          </div>

          {/* Informações do usuário */}
          <div className="flex flex-col items-center md:items-start text-white">
            <h2 className="text-2xl font-bold mb-1">{mainPerson?.fullName || user?.login}</h2>
            <p className="text-slate-200 mb-3">{user?.email}</p>

            <div className="flex flex-wrap gap-2">
              <div className="flex items-center gap-1.5 bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full text-sm">
                <Shield size={14} className="text-slate-200" />
                <span>Cliente</span>
              </div>

              {mainPerson?.cpf && (
                <div className="flex items-center gap-1.5 bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full text-sm">
                  <CreditCard size={14} className="text-slate-200" />
                  <span>CPF: {mainPerson.cpf.replace(/^(\d{3})(\d{3})(\d{3})(\d{2})$/, "$1.$2.$3-$4")}</span>
                </div>
              )}
            </div>
          </div>

          {/* Botão de ações adicionais */}
          <div className="absolute top-4 right-4">
            {/* Espaço reservado para ações futuras */}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Coluna da esquerda - Informações básicas e foto */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-4 flex items-center gap-2">
              <UserCheck size={18} className="text-slate-500 dark:text-slate-400" />
              Informações da Conta
            </h3>

            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-600 dark:text-purple-400">
                  <User size={20} />
                </div>
                <div>
                  <p className="text-sm text-neutral-500 dark:text-neutral-400">Login</p>
                  <p className="font-medium text-neutral-800 dark:text-neutral-200">{user?.login}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-600 dark:text-purple-400">
                  <Mail size={20} />
                </div>
                <div>
                  <p className="text-sm text-neutral-500 dark:text-neutral-400">Email</p>
                  <p className="font-medium text-neutral-800 dark:text-neutral-200">{user?.email}</p>
                </div>
              </div>
            </div>

            {/* Status da conta */}
            <div className="mt-6 p-4 bg-slate-50 dark:bg-slate-800/50 rounded-lg border border-slate-200 dark:border-slate-700">
              <div className="flex items-center gap-2 mb-3">
                {user?.active ? (
                  <CheckCircle size={18} className="text-green-500 dark:text-green-400" />
                ) : (
                  <AlertCircle size={18} className="text-red-500 dark:text-red-400" />
                )}
                <span className="font-medium text-neutral-800 dark:text-neutral-200">
                  Status: {user?.active ? 'Ativo' : 'Inativo'}
                </span>
              </div>

              {user?.lastLoginAt && (
                <div className="flex items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400 mb-2">
                  <Clock size={14} />
                  <span>Último acesso: {new Date(user.lastLoginAt).toLocaleString()}</span>
                </div>
              )}

              <div className="flex items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400">
                <Calendar size={14} />
                <span>Cadastrado em: {new Date(user?.createdAt).toLocaleDateString()}</span>
              </div>
            </div>

            {/* Estatísticas */}
            <div className="mt-6">
              <h4 className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-3">Estatísticas</h4>
              <div className="grid grid-cols-2 gap-3">
                <div className="p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg border border-slate-200 dark:border-slate-700 text-center">
                  <div className="text-2xl font-semibold text-slate-700 dark:text-slate-300">
                    {user?.persons?.length || 0}
                  </div>
                  <div className="text-xs text-slate-500 dark:text-slate-400">Pessoas</div>
                </div>
                <div className="p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg border border-slate-200 dark:border-slate-700 text-center">
                  <div className="text-2xl font-semibold text-slate-700 dark:text-slate-300">
                    {/* Aqui poderia ser o número de agendamentos, se disponível */}
                    0
                  </div>
                  <div className="text-xs text-slate-500 dark:text-slate-400">Agendamentos</div>
                </div>
              </div>
            </div>

            {/* Input escondido para upload de imagem */}
            <ClientProfileImageUpload
              ref={profileImageUploadRef}
              onImageSelected={handleProfileImageUpload}
            />
          </div>
        </div>

        {/* Coluna da direita - Formulário de edição */}
        <div className="lg:col-span-2">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-4 flex items-center gap-2">
              <User size={18} className="text-slate-500 dark:text-slate-400" />
              Meus Dados
            </h3>

            <form onSubmit={handleSubmit} className="space-y-6 profile-form">
              {/* Barra de progresso */}
              <div className="w-full bg-slate-100 dark:bg-slate-700 h-1.5 rounded-full overflow-hidden mb-6">
                <div className="bg-gradient-to-r from-purple-500 to-indigo-600 dark:from-purple-400 dark:to-indigo-500 h-full rounded-full" style={{ width: '100%' }}></div>
              </div>

              {/* Nome completo */}
              <ModuleFormGroup
                moduleColor="people"
                label="Nome Completo"
                htmlFor="person.fullName"
                icon={<User size={16} />}
              >
                <ModuleInput
                  moduleColor="people"
                  id="person.fullName"
                  name="person.fullName"
                  value={formData.person?.fullName || ""}
                  onChange={handleChange}
                  placeholder="Seu nome completo"
                  disabled={isLoading || isSaving}
                  required
                />
              </ModuleFormGroup>

              {/* Email */}
              <ModuleFormGroup
                moduleColor="people"
                label="Email"
                htmlFor="person.email"
                icon={<Mail size={16} />}
              >
                <ModuleInput
                  moduleColor="people"
                  id="person.email"
                  name="person.email"
                  type="email"
                  value={formData.person?.email || ""}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  disabled={isLoading || isSaving}
                />
              </ModuleFormGroup>

              {/* Telefone */}
              <ModuleFormGroup
                moduleColor="people"
                label="Telefone"
                htmlFor="person.phone"
                icon={<Phone size={16} />}
              >
                <ModuleMaskedInput
                  moduleColor="people"
                  mask="(__) _____-____"
                  replacement={{ _: /[0-9]/ }}
                  id="person.phone"
                  name="person.phone"
                  value={formData.person?.phone || ""}
                  onChange={handleChange}
                  placeholder="(00) 00000-0000"
                  disabled={isLoading || isSaving}
                />
              </ModuleFormGroup>

              {/* CPF */}
              <ModuleFormGroup
                moduleColor="people"
                label="CPF"
                htmlFor="person.cpf"
                icon={<CreditCard size={16} />}
              >
                <ModuleMaskedInput
                  moduleColor="people"
                  mask="___.___.___.___"
                  replacement={{ _: /[0-9]/ }}
                  id="person.cpf"
                  name="person.cpf"
                  value={formData.person?.cpf || ""}
                  onChange={handleChange}
                  placeholder="000.000.000-00"
                  disabled={isLoading || isSaving}
                />
              </ModuleFormGroup>

              {/* Data de Nascimento */}
              <ModuleFormGroup
                moduleColor="people"
                label="Data de Nascimento"
                htmlFor="person.birthDate"
                icon={<Calendar size={16} />}
              >
                <ModuleInput
                  moduleColor="people"
                  id="person.birthDate"
                  name="person.birthDate"
                  type="date"
                  value={formData.person?.birthDate || ""}
                  onChange={handleChange}
                  disabled={isLoading || isSaving}
                />
              </ModuleFormGroup>

              {/* Endereço */}
              <ModuleFormGroup
                moduleColor="people"
                label="Endereço"
                htmlFor="person.address"
                icon={<MapPin size={16} />}
              >
                <ModuleInput
                  moduleColor="people"
                  id="person.address"
                  name="person.address"
                  value={formData.person?.address || ""}
                  onChange={handleChange}
                  placeholder="Rua, número, complemento"
                  disabled={isLoading || isSaving}
                />
              </ModuleFormGroup>

              {/* Bairro */}
              <ModuleFormGroup
                moduleColor="people"
                label="Bairro"
                htmlFor="person.neighborhood"
                icon={<MapPin size={16} />}
              >
                <ModuleInput
                  moduleColor="people"
                  id="person.neighborhood"
                  name="person.neighborhood"
                  value={formData.person?.neighborhood || ""}
                  onChange={handleChange}
                  placeholder="Seu bairro"
                  disabled={isLoading || isSaving}
                />
              </ModuleFormGroup>

              {/* Cidade e Estado */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ModuleFormGroup
                  moduleColor="people"
                  label="Cidade"
                  htmlFor="person.city"
                  icon={<MapPin size={16} />}
                >
                  <ModuleInput
                    moduleColor="people"
                    id="person.city"
                    name="person.city"
                    value={formData.person?.city || ""}
                    onChange={handleChange}
                    placeholder="Sua cidade"
                    disabled={isLoading || isSaving}
                  />
                </ModuleFormGroup>

                <ModuleFormGroup
                  moduleColor="people"
                  label="Estado"
                  htmlFor="person.state"
                  icon={<MapPin size={16} />}
                >
                  <ModuleInput
                    moduleColor="people"
                    id="person.state"
                    name="person.state"
                    value={formData.person?.state || ""}
                    onChange={handleChange}
                    placeholder="Seu estado"
                    disabled={isLoading || isSaving}
                  />
                </ModuleFormGroup>
              </div>

              {/* CEP com busca automática */}
              <ModuleFormGroup
                moduleColor="people"
                label="CEP"
                htmlFor="person.postalCode"
                icon={<MapPin size={16} />}
              >
                <div className="relative">
                  <ModuleMaskedInput
                    moduleColor="people"
                    mask="_____-___"
                    replacement={{ _: /[0-9]/ }}
                    id="person.postalCode"
                    name="person.postalCode"
                    value={formData.person?.postalCode || ""}
                    onChange={(e) => {
                      handleChange(e);
                      // Buscar automaticamente quando CEP estiver completo
                      const cep = e.target.value;
                      if (cep && cep.replace(/\D/g, '').length === 8) {
                        handleCepSearch(cep);
                      }
                    }}
                    placeholder="00000-000"
                    className="pr-12"
                    disabled={isLoading || isSaving || isCepLoading}
                  />
                  <button
                    type="button"
                    onClick={() => {
                      const cep = formData.person?.postalCode;
                      handleCepSearch(cep);
                    }}
                    className="absolute inset-y-0 right-0 px-3 flex items-center bg-purple-500 hover:bg-purple-600 text-white rounded-r-lg transition-colors disabled:opacity-50"
                    disabled={isLoading || isSaving || isCepLoading}
                    title="Buscar CEP"
                  >
                    {isCepLoading ? (
                      <Loader2 size={16} className="animate-spin" />
                    ) : (
                      <Search size={16} />
                    )}
                  </button>
                </div>
                {cepError && (
                  <p className="mt-1 text-xs text-red-600 dark:text-red-400">
                    {cepError}
                  </p>
                )}
              </ModuleFormGroup>

              {/* Botão de salvar */}
              <div className="flex justify-end pt-4">
                <ModalButton
                  type="submit"
                  moduleColor="people"
                  variant="primary"
                  disabled={isLoading || isSaving}
                >
                  {isSaving ? (
                    <>
                      <Loader2 size={16} className="mr-2 animate-spin" />
                      Salvando...
                    </>
                  ) : (
                    <>
                      <Save size={16} className="mr-2" />
                      Salvar Alterações
                    </>
                  )}
                </ModalButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientProfilePage;
