"use client";

import React, { useState, useEffect, useRef } from "react";
import { User, Mail, Lock, Eye, EyeOff, CreditCard, Phone, Calendar, MapPin, FileText, Loader2, AlertCircle } from "lucide-react";
import { ModuleModal, ModalButton, ModuleInput, ModuleSelect, ModuleTextarea, ModuleFormGroup } from "@/components/ui";
import { clientsService } from "@/app/modules/people/services/clientsService";
import { personsService } from "@/app/modules/people/services/personsService";

import { usePreferences } from "@/hooks/usePreferences";
import { useAuth } from "@/contexts/AuthContext";
import { format } from "date-fns";
import AddressForm from "@/components/common/AddressForm";
import MaskedInput from "@/components/common/MaskedInput";
import ShareButton from "@/components/common/ShareButton";
import { validateBirthDate } from "@/utils/dateUtils";
import UserProfileImageUpload from "@/components/forms/UserProfileImageUpload";

const ClientFormModal = ({ isOpen, onClose, client, onSuccess }) => {
  const { getRequiredFieldsForValidation } = usePreferences();
  const { user: currentUser } = useAuth();
  const isSystemAdmin = currentUser?.role === 'SYSTEM_ADMIN';
  
  const [formData, setFormData] = useState({
    login: "",
    email: "",
    password: "",
    confirmPassword: "",
    person: {
      id: "",
      fullName: "",
      cpf: "",
      birthDate: "",
      phone: "",
      email: "",
      gender: "",
      address: "",
      neighborhood: "",
      city: "",
      state: "",
      postalCode: "",
      notes: "",
    }
  });

  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [editMode, setEditMode] = useState(false);




  // Load client data when editing
  useEffect(() => {
    if (client && isOpen) {
      setEditMode(true);

      // Format birthDate, if it exists
      let birthDateFormatted = "";
      if (client.persons && client.persons[0] && client.persons[0].birthDate) {
        try {
          birthDateFormatted = format(new Date(client.persons[0].birthDate), "yyyy-MM-dd");
        } catch (e) {
          console.error("Error formatting date:", e);
        }
      }

      // Formatar CPF
      let formattedCpf = "";
      if (client.persons && client.persons[0] && client.persons[0].cpf) {
        const cleanCpf = client.persons[0].cpf.replace(/\D/g, "");
        formattedCpf = cleanCpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
      }

      // Formatar telefone
      let formattedPhone = "";
      if (client.persons && client.persons[0] && client.persons[0].phone) {
        const cleanPhone = client.persons[0].phone.replace(/\D/g, "");
        formattedPhone = cleanPhone.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
      }

      // Formatar CEP
      let formattedPostalCode = "";
      if (client.persons && client.persons[0] && client.persons[0].postalCode) {
        const cleanPostalCode = client.persons[0].postalCode.replace(/\D/g, "");
        formattedPostalCode = cleanPostalCode.replace(/(\d{5})(\d{3})/, "$1-$2");
      }

      setFormData({
        login: client.login || "",
        email: client.email || "",
        password: "",
        confirmPassword: "",
        person: {
          id: client.persons && client.persons[0] ? client.persons[0].id || "" : "",
          fullName: client.persons && client.persons[0] ? client.persons[0].fullName || "" : "",
          cpf: formattedCpf || "",
          birthDate: birthDateFormatted,
          phone: formattedPhone || "",
          email: client.persons && client.persons[0] ? client.persons[0].email || "" : "",
          gender: client.persons && client.persons[0] ? client.persons[0].gender || "" : "",
          address: client.persons && client.persons[0] ? client.persons[0].address || "" : "",
          neighborhood: client.persons && client.persons[0] ? client.persons[0].neighborhood || "" : "",
          city: client.persons && client.persons[0] ? client.persons[0].city || "" : "",
          state: client.persons && client.persons[0] ? client.persons[0].state || "" : "",
          postalCode: formattedPostalCode || "",
          notes: client.persons && client.persons[0] ? client.persons[0].notes || "" : "",
        }
      });
    } else if (isOpen) {
      // Reset form when opening for new client
      setEditMode(false);
      resetForm();
    }
  }, [client, isOpen]);

  const resetForm = () => {
    setFormData({
      login: "",
      email: "",
      password: "",
      confirmPassword: "",
      person: {
        id: "",
        fullName: "",
        cpf: "",
        birthDate: "",
        phone: "",
        email: "",
        gender: "",
        address: "",
        neighborhood: "",
        city: "",
        state: "",
        postalCode: "",
        notes: "",
      }
    });
    setErrors({});
  };

  const validateForm = () => {
    const newErrors = {};
    const requiredFields = getRequiredFieldsForValidation('patient');

    // Client validation
    if (!formData.login) {
      newErrors.login = "Login é obrigatório";
    }

    if (!formData.email) {
      newErrors.email = "Email é obrigatório";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email inválido";
    }

    // No company validation needed - backend handles it automatically

    // Password is required only for new clients
    if (!editMode) {
      if (!formData.password) {
        newErrors.password = "Senha é obrigatória";
      } else if (formData.password.length < 6) {
        newErrors.password = "Senha deve ter no mínimo 6 caracteres";
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = "Senhas não conferem";
      }
    } else if (formData.password && formData.password.length < 6) {
      // If editing and entering a password, it must be at least 6 characters
      newErrors.password = "Senha deve ter no mínimo 6 caracteres";
    } else if (formData.password && formData.password !== formData.confirmPassword) {
      // If entering a password during edit, it must be confirmed
      newErrors.confirmPassword = "Senhas não conferem";
    }

    // Person validation
    if (!formData.person.fullName) {
      newErrors["person.fullName"] = "Nome completo é obrigatório";
    }

    // Validações baseadas nas preferências (usando preferências de paciente para titular)
    if (requiredFields.patientCpfCnpj && !formData.person.cpf) {
      newErrors["person.cpf"] = "CPF é obrigatório";
    }

    if (requiredFields.patientPhone && !formData.person.phone) {
      newErrors["person.phone"] = "Telefone é obrigatório";
    }

    if (requiredFields.patientCep && !formData.person.postalCode) {
      newErrors["person.postalCode"] = "CEP é obrigatório";
    }

    if (requiredFields.patientBirthDate && !formData.person.birthDate) {
      newErrors["person.birthDate"] = "Data de nascimento é obrigatória";
    }

    if (requiredFields.patientGender && !formData.person.gender) {
      newErrors["person.gender"] = "Gênero é obrigatório";
    }

    if (requiredFields.patientProfilePhoto && !formData.person.profileImageUrl && !formData.person.profileImageFile) {
      newErrors["person.profilePhoto"] = "Foto de perfil é obrigatória";
    }

    // Validações de formato (apenas se o campo estiver preenchido)
    if (formData.person.cpf) {
      const cleanCpf = formData.person.cpf.replace(/\D/g, "");
      if (cleanCpf.length !== 11) {
        newErrors["person.cpf"] = "CPF deve ter 11 dígitos";
      }
    }

    if (formData.person.email && !/\S+@\S+\.\S+/.test(formData.person.email)) {
      newErrors["person.email"] = "Email inválido";
    }

    // Validar data de nascimento (se preenchida, deve ser válida)
    if (formData.person.birthDate) {
      const validation = validateBirthDate(formData.person.birthDate);
      if (!validation.isValid) {
        newErrors["person.birthDate"] = validation.message;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Check if it's a person field or a client field
    if (name.startsWith("person.")) {
      const personField = name.replace("person.", "");
      setFormData((prev) => ({
        ...prev,
        person: {
          ...prev.person,
          [personField]: value
        }
      }));

      // Validar data de nascimento em tempo real
      if (personField === "birthDate") {
        if (value) {
          const validation = validateBirthDate(value);
          if (!validation.isValid) {
            setErrors(prev => ({ ...prev, [name]: validation.message }));
          } else {
            setErrors(prev => ({ ...prev, [name]: undefined }));
          }
        } else {
          setErrors(prev => ({ ...prev, [name]: undefined }));
        }
      }
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }

    // Clear error when user starts typing (exceto para birthDate que tem validação específica)
    if (errors[name] && name !== "person.birthDate") {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);

    try {
      // Prepare person data
      const personData = {
        fullName: formData.person.fullName,
        cpf: formData.person.cpf ? formData.person.cpf.replace(/\D/g, "") : undefined,
        birthDate: formData.person.birthDate || undefined,
        phone: formData.person.phone ? formData.person.phone.replace(/\D/g, "") : undefined,
        email: formData.person.email || undefined,
        gender: formData.person.gender || undefined,
        address: formData.person.address || undefined,
        neighborhood: formData.person.neighborhood || undefined,
        city: formData.person.city || undefined,
        state: formData.person.state || undefined,
        postalCode: formData.person.postalCode || undefined,
        notes: formData.person.notes || undefined,
      };

      console.log('Dados da pessoa a serem enviados:', personData);

      if (editMode) {
        // Update existing client account
        const clientPayload = {
          email: formData.email,
        };

        // Add password only if it's set
        if (formData.password) {
          clientPayload.password = formData.password;
        }

        await clientsService.updateClient(client.id, clientPayload);

        // Update the associated person if we have a person ID
        if (formData.person.id) {
          try {
            // Use personsService to update the person
            await personsService.updatePerson(formData.person.id, personData);
          } catch (personError) {
            console.error("Erro ao atualizar pessoa:", personError);
            throw personError;
          }
        }
      } else {
        // Create new client with person
        const payload = {
          login: formData.login,
          email: formData.email,
          password: formData.password,
          person: personData
        };

        await clientsService.createClient(payload);
      }

      onSuccess();
    } catch (error) {
      console.error("Erro ao salvar cliente:", error);

      // Handle API validation errors
      if (error.response?.data?.errors) {
        const apiErrors = {};
        error.response.data.errors.forEach(err => {
          apiErrors[err.param] = err.msg;
        });
        setErrors(apiErrors);
      } else {
        setErrors({
          submit: error.response?.data?.message || "Erro ao salvar cliente"
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Não precisamos mais dessas classes, pois usaremos os componentes de módulo

  // Componente de rodapé com botões
  const modalFooter = (
    <div className="flex justify-end gap-3">
      <ModalButton
        variant="secondary"
        moduleColor="people"
        onClick={onClose}
        disabled={isLoading}
      >
        Cancelar
      </ModalButton>

      <ModalButton
        variant="primary"
        moduleColor="people"
        type="submit"
        form="client-form"
        isLoading={isLoading}
      >
        {editMode ? "Atualizar" : "Salvar"}
      </ModalButton>
    </div>
  );

  return (
    <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
      {/* Overlay de fundo escuro */}
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>

      <div className="fixed left-[50%] top-[50%] z-[12050] w-full translate-x-[-50%] translate-y-[-50%] border-2 border-orange-300 dark:border-orange-600 bg-background shadow-lg duration-200 rounded-xl max-w-3xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="pb-4 border-b-2 border-orange-400 dark:border-orange-500 flex-shrink-0 px-6 pt-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg text-white">
              <User className="h-5 w-5" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-orange-800 dark:text-white border-l-4 border-orange-400 dark:border-orange-500 pl-3">
                {editMode ? 'Editar Cliente' : 'Novo Cliente'}
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 pl-3">
                {editMode ? 'Modifique as informações do cliente' : 'Preencha as informações para criar um novo cliente'}
              </p>
            </div>
            {client && (
              <div className="ml-auto">
                <ShareButton
                  itemType="client"
                  itemId={client.id}
                  itemTitle={client.fullName || client.persons?.[0]?.fullName}
                  size="sm"
                  variant="ghost"
                />
              </div>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="flex flex-col h-full max-h-[calc(90vh-120px)]">
          {/* Form Content */}
          <div className="flex-1 overflow-y-auto p-6">
      <form id="client-form" onSubmit={handleSubmit} className="space-y-6">
            {errors.submit && (
              <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-center gap-2">
                <AlertCircle size={16} />
                <span>{errors.submit}</span>
              </div>
            )}

            <div className="mb-8">
              <div className="border-b-2 border-orange-400 dark:border-orange-500 pb-3 mb-4">
                <h4 className="text-xl font-bold text-neutral-800 dark:text-white mb-1 border-l-4 border-orange-500 pl-3">
                  Dados da Conta
                </h4>
                <p className="text-xs text-neutral-600 dark:text-gray-300 pl-3">
                  Informações de acesso do cliente:
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-20">
              {/* Login */}
              <ModuleFormGroup
                moduleColor="people"
                label="Login *"
                htmlFor="login"
                icon={<User size={16} />}
                error={errors.login}
                errorMessage={errors.login}
              >
                <ModuleInput
                  moduleColor="people"
                  type="text"
                  id="login"
                  name="login"
                  value={formData.login}
                  onChange={handleChange}
                  placeholder="Username"
                  disabled={isLoading || editMode} // Login remains non-editable for existing clients
                  error={!!errors.login}
                />
              </ModuleFormGroup>

              {/* Email */}
              <ModuleFormGroup
                moduleColor="people"
                label="Email *"
                htmlFor="email"
                icon={<Mail size={16} />}
                error={errors.email}
                errorMessage={errors.email}
              >
                <ModuleInput
                  moduleColor="people"
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  disabled={isLoading}
                  error={!!errors.email}
                />
              </ModuleFormGroup>

              {/* Password */}
              <ModuleFormGroup
                moduleColor="people"
                label={`Senha ${!editMode ? "*" : ""}`}
                htmlFor="password"
                icon={<Lock size={16} />}
                error={errors.password}
                errorMessage={errors.password}
                helpText={editMode ? "Deixe em branco para manter a senha atual" : "Mínimo de 6 caracteres"}
              >
                <div className="relative">
                  <ModuleInput
                    moduleColor="people"
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    placeholder={editMode ? "••••••••" : "Senha"}
                    required={!editMode}
                    disabled={isLoading}
                    error={!!errors.password}
                    className="pr-10"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-400"
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5" />
                      ) : (
                        <Eye className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                </div>
              </ModuleFormGroup>

              {/* Confirm Password */}
              <ModuleFormGroup
                moduleColor="people"
                label={`Confirmar Senha ${!editMode ? "*" : ""}`}
                htmlFor="confirmPassword"
                icon={<Lock size={16} />}
                error={errors.confirmPassword}
                errorMessage={errors.confirmPassword}
              >
                <ModuleInput
                  moduleColor="people"
                  type={showPassword ? "text" : "password"}
                  id="confirmPassword"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  placeholder="Confirme a senha"
                  required={!editMode}
                  disabled={isLoading}
                  error={!!errors.confirmPassword}
                />
              </ModuleFormGroup>


            </div>

            <div className="mb-8" style={{marginTop: '70px'}}>
              <div className="border-b-2 border-orange-400 dark:border-orange-500 pb-3 mb-4">
                <h4 className="text-xl font-bold text-neutral-800 dark:text-white mb-1 border-l-4 border-orange-500 pl-3">
                  Dados do Titular
                </h4>
                <p className="text-xs text-neutral-600 dark:text-gray-300 pl-3">
                  Informações pessoais do titular da conta:
                </p>
              </div>
            </div>

            {/* Foto de perfil do titular */}
            <div className="flex flex-col items-center mb-6">
              <UserProfileImageUpload
                userId={formData.person.id}
                initialImageUrl={formData.person.profileImageUrl}
                deferUpload={true}
                onImageUploaded={(url, file) => {
                  setFormData(prev => ({
                    ...prev,
                    person: {
                      ...prev.person,
                      profileImageUrl: url || '',
                      profileImageFile: file
                    }
                  }));
                }}
                size="large"
                disabled={isLoading}
              />
              {errors["person.profilePhoto"] && (
                <p className="mt-2 text-xs text-red-600 dark:text-red-400 text-center">
                  {errors["person.profilePhoto"]}
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Full name */}
              <ModuleFormGroup
                moduleColor="people"
                label="Nome completo *"
                htmlFor="person.fullName"
                icon={<User size={16} />}
                error={errors["person.fullName"]}
                errorMessage={errors["person.fullName"]}
                className="md:col-span-2"
              >
                <ModuleInput
                  moduleColor="people"
                  type="text"
                  id="person.fullName"
                  name="person.fullName"
                  value={formData.person.fullName}
                  onChange={handleChange}
                  placeholder="Nome completo"
                  disabled={isLoading}
                  error={!!errors["person.fullName"]}
                />
              </ModuleFormGroup>

              {/* CPF */}
              <ModuleFormGroup
                moduleColor="people"
                label="CPF"
                htmlFor="person.cpf"
                icon={<CreditCard size={16} />}
                error={errors["person.cpf"]}
                errorMessage={errors["person.cpf"]}
              >
                <div className="relative">
                  <MaskedInput
                    type="cpf"
                    value={formData.person.cpf}
                    onChange={(e) =>
                      handleChange({
                        target: { name: "person.cpf", value: e.target.value },
                      })
                    }
                    placeholder="000.000.000-00"
                    className={`w-full rounded-md border ${errors["person.cpf"] ? "border-red-300 dark:border-red-700" : "border-neutral-300 dark:border-neutral-600"} px-3 py-2 text-sm transition-colors duration-200 bg-white dark:bg-gray-700 dark:text-gray-200 outline-none`}
                    disabled={isLoading}
                  />
                </div>
              </ModuleFormGroup>

              {/* Birth date */}
              <ModuleFormGroup
                moduleColor="people"
                label="Data de Nascimento"
                htmlFor="person.birthDate"
                icon={<Calendar size={16} />}
                error={!!errors["person.birthDate"]}
                errorMessage={errors["person.birthDate"]}
              >
                <ModuleInput
                  moduleColor="people"
                  type="date"
                  id="person.birthDate"
                  name="person.birthDate"
                  value={formData.person.birthDate}
                  onChange={handleChange}
                  disabled={isLoading}
                  error={!!errors["person.birthDate"]}
                  min="1900-01-01"
                  max={new Date().toISOString().split('T')[0]}
                />
              </ModuleFormGroup>

              {/* Gender */}
              <ModuleFormGroup
                moduleColor="people"
                label="Gênero"
                htmlFor="person.gender"
                icon={<User size={16} />}
              >
                <ModuleSelect
                  moduleColor="people"
                  id="person.gender"
                  name="person.gender"
                  value={formData.person.gender}
                  onChange={handleChange}
                  disabled={isLoading}
                >
                  <option value="">Selecione</option>
                  <option value="M">Masculino</option>
                  <option value="F">Feminino</option>
                  <option value="O">Outro</option>
                </ModuleSelect>
              </ModuleFormGroup>

              {/* Person Email */}
              <ModuleFormGroup
                moduleColor="people"
                label="Email da Pessoa"
                htmlFor="person.email"
                icon={<Mail size={16} />}
                error={errors["person.email"]}
                errorMessage={errors["person.email"]}
                helpText="Deixe em branco para usar o mesmo email da conta"
              >
                <ModuleInput
                  moduleColor="people"
                  type="email"
                  id="person.email"
                  name="person.email"
                  value={formData.person.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  disabled={isLoading}
                  error={!!errors["person.email"]}
                />
              </ModuleFormGroup>

              {/* Phone */}
              <ModuleFormGroup
                moduleColor="people"
                label="Telefone"
                htmlFor="person.phone"
                icon={<Phone size={16} />}
                error={errors["person.phone"]}
                errorMessage={errors["person.phone"]}
              >
                <div className="relative">
                  <MaskedInput
                    type="phone"
                    value={formData.person.phone}
                    onChange={(e) =>
                      handleChange({
                        target: { name: "person.phone", value: e.target.value },
                      })
                    }
                    placeholder="(00) 00000-0000"
                    className={`w-full rounded-md border ${errors["person.phone"] ? "border-red-300 dark:border-red-700" : "border-neutral-300 dark:border-neutral-600"} px-3 py-2 text-sm transition-colors duration-200 bg-white dark:bg-gray-700 dark:text-gray-200 outline-none`}
                    disabled={isLoading}
                  />
                </div>
              </ModuleFormGroup>

              {/* Address */}
              <div className="md:col-span-2">
              <div className="mb-8 mt-12">
                <div className="border-b-2 border-orange-400 dark:border-orange-500 pb-3 mb-4">
                  <h4 className="text-xl font-bold text-neutral-800 dark:text-white mb-1 border-l-4 border-orange-500 pl-3">
                    Endereço
                  </h4>
                  <p className="text-xs text-neutral-600 dark:text-gray-300 pl-3">
                    Informações de endereço do titular:
                  </p>
                </div>
              </div>
                {console.log('Estado atual do formulário antes de renderizar AddressForm:', formData)}
                <AddressForm
                  formData={formData}
                  setFormData={(newFormData) => {
                    console.log('Atualizando formData no ClientFormModal:', newFormData);
                    setFormData(newFormData);
                  }}
                  errors={errors}
                  isLoading={isLoading}
                  prefix="person."
                  fieldMapping={{
                    // Mapeamento personalizado para os campos da API ViaCEP
                    logradouro: "person.address",
                    bairro: "person.neighborhood",
                    localidade: "person.city",
                    uf: "person.state",
                    cep: "person.postalCode"
                  }}
                  classes={{
                    label: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",
                    input: "block w-full pl-10 pr-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-module-people-border focus:border-module-people-border dark:focus:ring-module-people-border-dark dark:focus:border-module-people-border-dark bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",
                    error: "mt-1 text-xs text-red-600 dark:text-red-400",
                    iconContainer: "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                  }}
                />
              </div>

              {/* Notes */}
              <ModuleFormGroup
                moduleColor="people"
                label="Observações"
                htmlFor="person.notes"
                icon={<FileText size={16} />}
                className="md:col-span-2"
              >
                <ModuleTextarea
                  moduleColor="people"
                  id="person.notes"
                  name="person.notes"
                  value={formData.person.notes}
                  onChange={handleChange}
                  placeholder="Observações adicionais"
                  rows={3}
                  disabled={isLoading}
                />
              </ModuleFormGroup>
            </div>
      </form>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 border-t-2 border-gray-300 dark:border-gray-600 pt-4 flex-shrink-0 px-6 pb-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
              disabled={isLoading}
            >
              Cancelar
            </button>
            <button
              type="submit"
              form="client-form"
              className="px-4 py-2 bg-orange-500 dark:bg-orange-600 text-white rounded-lg hover:bg-orange-600 dark:hover:bg-orange-700 transition-colors flex items-center gap-2"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 size={16} className="animate-spin" />
                  <span>Salvando...</span>
                </>
              ) : (
                <>
                  <User size={16} />
                  <span>{editMode ? "Atualizar" : "Salvar"}</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientFormModal;