const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const AuthController = require('../controllers/authController');
const { authenticate } = require('../middlewares/auth');
const { checkLoginAttempts, logLoginAttempt, validatePasswordPolicy } = require('../middlewares/securityMiddleware');

router.post('/register', [
  body('fullName').notEmpty().withMessage('Nome é obrigatório'),
  body('email').isEmail().withMessage('Email inválido'),
  body('password').isLength({ min: 6 }).withMessage('Senha deve ter no mínimo 6 caracteres'),
  body('module').optional().isIn(['ADMIN', 'FINANCIAL', 'HR', 'SCHEDULER', 'USER'])
    .withMessage('Role inválido')
], validatePasswordPolicy, AuthController.register);

router.post('/login', [
  body('email').optional().isEmail().withMessage('Email inválido'),
  body('username').optional(),
  body('password').notEmpty().withMessage('Senha é obrigatória'),
  body('loginType').optional().isIn(['email', 'username']).withMessage('Tipo de login inválido')
], checkLoginAttempts, logLoginAttempt, AuthController.login);

router.get('/me', authenticate, AuthController.me);

// 2FA Routes
router.post('/verify-2fa', [
  body('userId').notEmpty().withMessage('User ID é obrigatório'),
  body('token').isLength({ min: 6, max: 6 }).withMessage('Token deve ter 6 dígitos')
], AuthController.verifyTwoFactor);

router.post('/resend-2fa', [
  body('userId').notEmpty().withMessage('User ID é obrigatório')
], AuthController.resendTwoFactor);

// router.post('/change-password', [
//   body('currentPassword').notEmpty().withMessage('Senha atual é obrigatória'),
//   body('newPassword').isLength({ min: 6 }).withMessage('Nova senha deve ter no mínimo 6 caracteres')
// ], authenticate, validatePasswordPolicy, AuthController.changePassword);

router.post('/request-email-confirmation', [
  body('email').isEmail().withMessage('Email inválido')
], AuthController.requestEmailConfirmation);

router.post('/verify-email-confirmation', [
  body('email').isEmail().withMessage('Email inválido'),
  body('code').notEmpty().withMessage('Código é obrigatório')
], AuthController.verifyEmailConfirmation);

// Rotas para sessões de signup
router.post('/create-signup-session', [
  body('login').notEmpty().withMessage('Login é obrigatório'),
  body('fullName').notEmpty().withMessage('Nome completo é obrigatório'),
  body('email').isEmail().withMessage('Email inválido'),
  body('password').isLength({ min: 8 }).withMessage('Senha deve ter no mínimo 8 caracteres')
], validatePasswordPolicy, AuthController.createSignupSession);

router.get('/signup-session/:sessionId', AuthController.getSignupSession);
router.post('/signup-session/finalize', AuthController.finalizeSignup);
router.get('/signup-session/:sessionId/payment-status', AuthController.checkPaymentSignupStatus);

module.exports = router;